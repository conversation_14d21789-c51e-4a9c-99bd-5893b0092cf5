#!/bin/bash

echo "🚀 Setting up Trading Application..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18 or higher is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js version: $(node -v)"

# Install root dependencies
echo "📦 Installing root dependencies..."
npm install

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd frontend
npm install
cd ..

# Install backend dependencies
echo "📦 Installing backend dependencies..."
cd backend
npm install
cd ..

echo "✅ All dependencies installed successfully!"

echo ""
echo "🔧 Next steps:"
echo "1. Set up MySQL database and update backend/.env with your database credentials"
echo "2. Start the backend: npm run backend:dev"
echo "3. Start the frontend: npm run frontend:dev"
echo "4. Access the application at http://localhost:4200"
echo ""
echo "📚 Default login credentials:"
echo "   Admin: <EMAIL> / admin123"
echo "   User: <EMAIL> / password123"
