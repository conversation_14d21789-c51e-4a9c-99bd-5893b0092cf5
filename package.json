{"name": "trading-app", "version": "1.0.0", "description": "Web-based trading application", "scripts": {"frontend:dev": "cd frontend && npm run start", "backend:dev": "cd backend && npm run start:dev", "frontend:build": "cd frontend && npm run build", "backend:build": "cd backend && npm run build", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install"}, "workspaces": ["frontend", "backend"], "devDependencies": {"concurrently": "^8.2.2"}}