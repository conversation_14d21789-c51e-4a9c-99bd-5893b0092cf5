# Trading Application

A full-stack web-based trading application built with Angular 20, NestJS 11, and MySQL 8.

## Features

- **User Authentication**: JWT-based authentication with role-based access control
- **Market Data**: Real-time market data viewing with charts
- **Trading**: Buy/sell orders (market & limit types)
- **Order Management**: Order book and trade history
- **Portfolio**: User holdings, balances, and P&L tracking
- **Admin Panel**: User and asset management

## Tech Stack

### Frontend
- Angular 20
- TailwindCSS
- TypeScript
- RxJS

### Backend
- NestJS 11.1.3
- TypeORM
- MySQL 8
- JWT Authentication
- Swagger API Documentation

## Project Structure

```
trading-app/
├── frontend/          # Angular application
│   ├── src/
│   │   ├── app/
│   │   │   ├── components/
│   │   │   ├── services/
│   │   │   ├── guards/
│   │   │   ├── models/
│   │   │   └── pages/
│   │   └── assets/
├── backend/           # NestJS API
│   ├── src/
│   │   ├── auth/
│   │   ├── users/
│   │   ├── assets/
│   │   ├── orders/
│   │   ├── trades/
│   │   ├── portfolios/
│   │   └── common/
└── README.md
```

## Setup Instructions

### Prerequisites
- Node.js (v18 or higher)
- MySQL 8
- npm or yarn

### Database Setup
1. Install MySQL 8 and create a database named `trading_app`
2. Update database credentials in `backend/.env`

### Installation

1. **Install all dependencies:**
   ```bash
   npm run install:all
   ```

2. **Start the backend:**
   ```bash
   npm run backend:dev
   ```

3. **Start the frontend (in a new terminal):**
   ```bash
   npm run frontend:dev
   ```

### Access the Application

- **Frontend**: http://localhost:4200
- **Backend API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api

## Development

### Backend Development
- API runs on port 3000
- Swagger documentation available at `/api`
- Database synchronization enabled in development

### Frontend Development
- Angular dev server runs on port 4200
- Hot reload enabled
- TailwindCSS for styling

## API Endpoints

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `GET /auth/profile` - Get user profile

### Assets
- `GET /assets` - Get all assets
- `GET /assets/:id` - Get asset by ID

### Orders
- `POST /orders` - Create order
- `GET /orders` - Get user orders
- `DELETE /orders/:id` - Cancel order

### Trades
- `GET /trades` - Get trade history

### Portfolio
- `GET /portfolio` - Get user portfolio

## Environment Variables

Copy `backend/.env.example` to `backend/.env` and update the values:

```env
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=trading_app
JWT_SECRET=your-secret-key
```

## Next Steps

1. Set up MySQL database
2. Install dependencies
3. Start both frontend and backend
4. Access the application at http://localhost:4200
