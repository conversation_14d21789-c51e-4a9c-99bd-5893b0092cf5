import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Asset } from './entities/asset.entity';

@Injectable()
export class AssetsService {
  constructor(
    @InjectRepository(Asset)
    private assetsRepository: Repository<Asset>,
  ) {}

  async findAll(): Promise<Asset[]> {
    return this.assetsRepository.find({
      where: { isActive: true },
      order: { symbol: 'ASC' },
    });
  }

  async findOne(id: string): Promise<Asset> {
    const asset = await this.assetsRepository.findOne({
      where: { id, isActive: true },
    });

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    return asset;
  }

  async findBySymbol(symbol: string): Promise<Asset> {
    const asset = await this.assetsRepository.findOne({
      where: { symbol: symbol.toUpperCase(), isActive: true },
    });

    if (!asset) {
      throw new NotFoundException('Asset not found');
    }

    return asset;
  }

  async updatePrice(id: string, newPrice: number): Promise<Asset> {
    const asset = await this.findOne(id);
    
    asset.previousClose = asset.currentPrice;
    asset.currentPrice = newPrice;
    
    return this.assetsRepository.save(asset);
  }

  async getTradableAssets(): Promise<Asset[]> {
    return this.assetsRepository.find({
      where: { isActive: true, isTradeable: true },
      order: { symbol: 'ASC' },
    });
  }

  async getMarketData(): Promise<Asset[]> {
    const assets = await this.findAll();
    
    // In a real application, you would fetch real-time data from external APIs
    // For now, we'll simulate some price movements
    return assets.map(asset => ({
      ...asset,
      priceChange: asset.priceChange,
      priceChangePercent: asset.priceChangePercent,
    }));
  }

  // Admin functions
  async create(assetData: Partial<Asset>): Promise<Asset> {
    const asset = this.assetsRepository.create(assetData);
    return this.assetsRepository.save(asset);
  }

  async update(id: string, updateData: Partial<Asset>): Promise<Asset> {
    await this.assetsRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const asset = await this.findOne(id);
    asset.isActive = false;
    await this.assetsRepository.save(asset);
  }
}
