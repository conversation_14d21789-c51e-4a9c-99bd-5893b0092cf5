import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { AssetsService } from './assets.service';
import { Asset } from './entities/asset.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRole } from '../users/entities/user.entity';

@ApiTags('assets')
@Controller('assets')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class AssetsController {
  constructor(private readonly assetsService: AssetsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all assets' })
  findAll(): Promise<Asset[]> {
    return this.assetsService.findAll();
  }

  @Get('market-data')
  @ApiOperation({ summary: 'Get market data for all assets' })
  getMarketData(): Promise<Asset[]> {
    return this.assetsService.getMarketData();
  }

  @Get('tradeable')
  @ApiOperation({ summary: 'Get all tradeable assets' })
  getTradableAssets(): Promise<Asset[]> {
    return this.assetsService.getTradableAssets();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get asset by ID' })
  findOne(@Param('id') id: string): Promise<Asset> {
    return this.assetsService.findOne(id);
  }

  @Post()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Create new asset (Admin only)' })
  create(@Body() createAssetDto: Partial<Asset>): Promise<Asset> {
    return this.assetsService.create(createAssetDto);
  }

  @Patch(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update asset (Admin only)' })
  update(@Param('id') id: string, @Body() updateAssetDto: Partial<Asset>): Promise<Asset> {
    return this.assetsService.update(id, updateAssetDto);
  }

  @Patch(':id/price')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Update asset price (Admin only)' })
  updatePrice(@Param('id') id: string, @Body('price') price: number): Promise<Asset> {
    return this.assetsService.updatePrice(id, price);
  }

  @Delete(':id')
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Delete asset (Admin only)' })
  remove(@Param('id') id: string): Promise<void> {
    return this.assetsService.remove(id);
  }
}
