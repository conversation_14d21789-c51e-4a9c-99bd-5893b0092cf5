import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SeedingService } from './seeding.service';
import { User } from '../../users/entities/user.entity';
import { Asset } from '../../assets/entities/asset.entity';
import { Portfolio } from '../../portfolios/entities/portfolio.entity';

@Module({
  imports: [TypeOrmModule.forFeature([User, Asset, Portfolio])],
  providers: [SeedingService],
  exports: [SeedingService],
})
export class SeedingModule {}
