import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User, UserRole } from '../../users/entities/user.entity';
import { Asset, AssetType } from '../../assets/entities/asset.entity';
import { Portfolio } from '../../portfolios/entities/portfolio.entity';
import * as bcrypt from 'bcryptjs';

@Injectable()
export class SeedingService {
  private readonly logger = new Logger(SeedingService.name);

  constructor(
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(Asset)
    private assetsRepository: Repository<Asset>,
    @InjectRepository(Portfolio)
    private portfoliosRepository: Repository<Portfolio>,
  ) {}

  async seedDatabase(): Promise<void> {
    this.logger.log('Starting database seeding...');

    await this.seedUsers();
    await this.seedAssets();
    await this.seedPortfolios();

    this.logger.log('Database seeding completed!');
  }

  private async seedUsers(): Promise<void> {
    const userCount = await this.usersRepository.count();
    if (userCount > 0) {
      this.logger.log('Users already exist, skipping user seeding');
      return;
    }

    const users = [
      {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        password: await bcrypt.hash('admin123', 10),
        role: UserRole.ADMIN,
      },
      {
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        password: await bcrypt.hash('password123', 10),
        role: UserRole.USER,
      },
      {
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        password: await bcrypt.hash('password123', 10),
        role: UserRole.USER,
      },
    ];

    await this.usersRepository.save(users);
    this.logger.log(`Seeded ${users.length} users`);
  }

  private async seedAssets(): Promise<void> {
    const assetCount = await this.assetsRepository.count();
    if (assetCount > 0) {
      this.logger.log('Assets already exist, skipping asset seeding');
      return;
    }

    const assets = [
      {
        symbol: 'AAPL',
        name: 'Apple Inc.',
        type: AssetType.STOCK,
        currentPrice: 175.50,
        previousClose: 173.25,
        volume: 45000000,
        marketCap: 2800000000000,
      },
      {
        symbol: 'GOOGL',
        name: 'Alphabet Inc.',
        type: AssetType.STOCK,
        currentPrice: 2750.80,
        previousClose: 2735.40,
        volume: 1200000,
        marketCap: 1800000000000,
      },
      {
        symbol: 'MSFT',
        name: 'Microsoft Corporation',
        type: AssetType.STOCK,
        currentPrice: 415.25,
        previousClose: 412.10,
        volume: 25000000,
        marketCap: 3100000000000,
      },
      {
        symbol: 'BTC',
        name: 'Bitcoin',
        type: AssetType.CRYPTO,
        currentPrice: 45000.00,
        previousClose: 44200.00,
        volume: 15000000000,
        marketCap: 850000000000,
      },
      {
        symbol: 'ETH',
        name: 'Ethereum',
        type: AssetType.CRYPTO,
        currentPrice: 3200.50,
        previousClose: 3150.25,
        volume: 8000000000,
        marketCap: 380000000000,
      },
    ];

    await this.assetsRepository.save(assets);
    this.logger.log(`Seeded ${assets.length} assets`);
  }

  private async seedPortfolios(): Promise<void> {
    const users = await this.usersRepository.find();
    
    for (const user of users) {
      const existingPortfolio = await this.portfoliosRepository.findOne({
        where: { userId: user.id },
      });

      if (!existingPortfolio) {
        const portfolio = this.portfoliosRepository.create({
          userId: user.id,
          cashBalance: user.role === UserRole.ADMIN ? 100000 : 10000,
          totalValue: user.role === UserRole.ADMIN ? 100000 : 10000,
          totalPnL: 0,
          totalPnLPercent: 0,
        });

        await this.portfoliosRepository.save(portfolio);
      }
    }

    this.logger.log('Seeded portfolios for all users');
  }
}
