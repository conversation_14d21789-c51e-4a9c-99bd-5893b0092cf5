import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { Asset } from '../../assets/entities/asset.entity';
import { Order } from '../../orders/entities/order.entity';

@Entity('trades')
export class Trade {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('decimal', { precision: 10, scale: 2 })
  quantity: number;

  @Column('decimal', { precision: 10, scale: 2 })
  price: number;

  @Column('decimal', { precision: 10, scale: 2 })
  totalValue: number;

  @Column('decimal', { precision: 5, scale: 4, default: 0 })
  fee: number;

  @CreateDateColumn()
  executedAt: Date;

  @ManyToOne(() => User, user => user.trades)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  userId: string;

  @ManyToOne(() => Asset, asset => asset.trades)
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column()
  assetId: string;

  @ManyToOne(() => Order)
  @JoinColumn({ name: 'buyOrderId' })
  buyOrder: Order;

  @Column({ nullable: true })
  buyOrderId: string;

  @ManyToOne(() => Order)
  @JoinColumn({ name: 'sellOrderId' })
  sellOrder: Order;

  @Column({ nullable: true })
  sellOrderId: string;

  // Additional fields for tracking
  @Column()
  buyUserId: string;

  @Column()
  sellUserId: string;
}
