import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiT<PERSON>s, ApiBearerAuth, ApiOperation } from '@nestjs/swagger';
import { TradesService } from './trades.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { User } from '../users/entities/user.entity';

@ApiTags('trades')
@Controller('trades')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class TradesController {
  constructor(private readonly tradesService: TradesService) {}

  @Get()
  @ApiOperation({ summary: 'Get user trades' })
  findUserTrades(@CurrentUser() user: User) {
    return this.tradesService.findByUser(user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get trade by ID' })
  findOne(@Param('id') id: string) {
    return this.tradesService.findOne(id);
  }
}
