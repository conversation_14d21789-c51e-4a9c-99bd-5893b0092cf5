import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Trade } from './entities/trade.entity';

@Injectable()
export class TradesService {
  constructor(
    @InjectRepository(Trade)
    private tradesRepository: Repository<Trade>,
  ) {}

  async findAll(): Promise<Trade[]> {
    return this.tradesRepository.find({
      relations: ['asset', 'user'],
      order: { executedAt: 'DESC' },
    });
  }

  async findByUser(userId: string): Promise<Trade[]> {
    return this.tradesRepository.find({
      where: { userId },
      relations: ['asset'],
      order: { executedAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Trade> {
    return this.tradesRepository.findOne({
      where: { id },
      relations: ['asset', 'user'],
    });
  }
}
