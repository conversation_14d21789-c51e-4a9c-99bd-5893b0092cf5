import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Min } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { OrderType, OrderSide } from '../entities/order.entity';

export class CreateOrderDto {
  @ApiProperty({ enum: OrderType, example: OrderType.LIMIT })
  @IsEnum(OrderType)
  type: OrderType;

  @ApiProperty({ enum: OrderSide, example: OrderSide.BUY })
  @IsEnum(OrderSide)
  side: OrderSide;

  @ApiProperty({ example: 10.5 })
  @IsNumber()
  @Min(0.01)
  quantity: number;

  @ApiProperty({ example: 150.25, required: false, description: 'Required for limit orders' })
  @IsOptional()
  @IsNumber()
  @Min(0.01)
  price?: number;

  @ApiProperty({ example: 'uuid-of-asset' })
  @IsUUID()
  assetId: string;
}
