import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Portfolio } from './entities/portfolio.entity';
import { PortfolioHolding } from './entities/portfolio-holding.entity';

@Injectable()
export class PortfoliosService {
  constructor(
    @InjectRepository(Portfolio)
    private portfoliosRepository: Repository<Portfolio>,
    @InjectRepository(PortfolioHolding)
    private holdingsRepository: Repository<PortfolioHolding>,
  ) {}

  async createInitialPortfolio(userId: string): Promise<Portfolio> {
    const portfolio = this.portfoliosRepository.create({
      userId,
      cashBalance: 10000, // Starting balance
      totalValue: 10000,
      totalPnL: 0,
      totalPnLPercent: 0,
    });

    return this.portfoliosRepository.save(portfolio);
  }

  async findByUserId(userId: string): Promise<Portfolio> {
    const portfolio = await this.portfoliosRepository.findOne({
      where: { userId },
      relations: ['holdings', 'holdings.asset'],
    });

    if (!portfolio) {
      throw new NotFoundException('Portfolio not found');
    }

    return portfolio;
  }

  async updateCashBalance(userId: string, amount: number): Promise<Portfolio> {
    const portfolio = await this.findByUserId(userId);
    portfolio.cashBalance += amount;
    portfolio.totalValue = portfolio.cashBalance + portfolio.investedValue;
    
    return this.portfoliosRepository.save(portfolio);
  }

  async addHolding(
    userId: string,
    assetId: string,
    quantity: number,
    price: number,
  ): Promise<PortfolioHolding> {
    const portfolio = await this.findByUserId(userId);
    
    // Check if holding already exists
    let holding = await this.holdingsRepository.findOne({
      where: { portfolioId: portfolio.id, assetId },
    });

    if (holding) {
      // Update existing holding
      const totalCost = holding.totalCost + (quantity * price);
      const totalQuantity = holding.quantity + quantity;
      
      holding.quantity = totalQuantity;
      holding.averageCost = totalCost / totalQuantity;
      holding.totalCost = totalCost;
      holding.updateCurrentValue(price);
    } else {
      // Create new holding
      holding = this.holdingsRepository.create({
        portfolioId: portfolio.id,
        assetId,
        quantity,
        averageCost: price,
        totalCost: quantity * price,
        currentValue: quantity * price,
        unrealizedPnL: 0,
        unrealizedPnLPercent: 0,
      });
    }

    return this.holdingsRepository.save(holding);
  }

  async removeHolding(
    userId: string,
    assetId: string,
    quantity: number,
    price: number,
  ): Promise<PortfolioHolding | null> {
    const portfolio = await this.findByUserId(userId);
    
    const holding = await this.holdingsRepository.findOne({
      where: { portfolioId: portfolio.id, assetId },
    });

    if (!holding || holding.quantity < quantity) {
      throw new NotFoundException('Insufficient holdings');
    }

    if (holding.quantity === quantity) {
      // Remove holding completely
      await this.holdingsRepository.remove(holding);
      return null;
    } else {
      // Reduce holding quantity
      holding.quantity -= quantity;
      holding.totalCost = holding.quantity * holding.averageCost;
      holding.updateCurrentValue(price);
      
      return this.holdingsRepository.save(holding);
    }
  }

  async updatePortfolioValues(userId: string): Promise<Portfolio> {
    const portfolio = await this.findByUserId(userId);
    
    let totalInvestedValue = 0;
    let totalPnL = 0;

    for (const holding of portfolio.holdings) {
      totalInvestedValue += holding.currentValue;
      totalPnL += holding.unrealizedPnL;
    }

    portfolio.totalValue = portfolio.cashBalance + totalInvestedValue;
    portfolio.totalPnL = totalPnL;
    portfolio.totalPnLPercent = totalInvestedValue > 0 ? (totalPnL / totalInvestedValue) * 100 : 0;

    return this.portfoliosRepository.save(portfolio);
  }
}
