import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Portfolio } from './portfolio.entity';
import { Asset } from '../../assets/entities/asset.entity';

@Entity('portfolio_holdings')
export class PortfolioHolding {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('decimal', { precision: 10, scale: 2 })
  quantity: number;

  @Column('decimal', { precision: 10, scale: 2 })
  averageCost: number;

  @Column('decimal', { precision: 15, scale: 2 })
  totalCost: number;

  @Column('decimal', { precision: 15, scale: 2 })
  currentValue: number;

  @Column('decimal', { precision: 15, scale: 2 })
  unrealizedPnL: number;

  @Column('decimal', { precision: 5, scale: 2 })
  unrealizedPnLPercent: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Portfolio, portfolio => portfolio.holdings)
  @JoinColumn({ name: 'portfolioId' })
  portfolio: Portfolio;

  @Column()
  portfolioId: string;

  @ManyToOne(() => Asset)
  @JoinColumn({ name: 'assetId' })
  asset: Asset;

  @Column()
  assetId: string;

  // Method to update current value and PnL
  updateCurrentValue(currentPrice: number): void {
    this.currentValue = this.quantity * currentPrice;
    this.unrealizedPnL = this.currentValue - this.totalCost;
    this.unrealizedPnLPercent = this.totalCost > 0 ? (this.unrealizedPnL / this.totalCost) * 100 : 0;
  }
}
