import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToOne, JoinColumn, OneToMany } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { PortfolioHolding } from './portfolio-holding.entity';

@Entity('portfolios')
export class Portfolio {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('decimal', { precision: 15, scale: 2, default: 0 })
  cashBalance: number;

  @Column('decimal', { precision: 15, scale: 2, default: 0 })
  totalValue: number;

  @Column('decimal', { precision: 15, scale: 2, default: 0 })
  totalPnL: number;

  @Column('decimal', { precision: 5, scale: 2, default: 0 })
  totalPnLPercent: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToOne(() => User, user => user.portfolio)
  @JoinColumn({ name: 'userId' })
  user: User;

  @Column()
  userId: string;

  @OneToMany(() => PortfolioHolding, holding => holding.portfolio, { cascade: true })
  holdings: PortfolioHolding[];

  // Computed properties
  get investedValue(): number {
    return this.totalValue - this.cashBalance;
  }

  get availableCash(): number {
    return this.cashBalance;
  }
}
