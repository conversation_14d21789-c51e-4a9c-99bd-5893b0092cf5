import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';

export interface IndianStock {
  symbol: string;
  name: string;
  price: number;
  change: number;
  changePercent: number;
  volume: number;
  marketCap?: number;
  sector?: string;
  high?: number;
  low?: number;
  open?: number;
  previousClose?: number;
}

export interface NSEStock {
  symbol: string;
  companyName: string;
  lastPrice: number;
  change: number;
  pChange: number;
  totalTradedVolume: number;
  totalTradedValue: number;
  dayHigh: number;
  dayLow: number;
  open: number;
  previousClose: number;
}

@Injectable({
  providedIn: 'root'
})
export class IndianStocksService {
  // Using multiple APIs for real stock data
  private readonly YAHOO_FINANCE_API = 'https://query1.finance.yahoo.com/v8/finance/chart';
  private readonly CORS_PROXY = 'https://api.allorigins.win/get?url=';
  private readonly CORS_PROXY_RAW = 'https://api.allorigins.win/raw?url=';

  // Alternative free APIs
  private readonly ALPHA_VANTAGE_API = 'https://www.alphavantage.co/query';
  private readonly TWELVE_DATA_API = 'https://api.twelvedata.com';

  // Indian stock symbols with Yahoo Finance suffixes
  private readonly INDIAN_STOCKS_YAHOO = [
    'RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS', 'HINDUNILVR.NS',
    'ICICIBANK.NS', 'KOTAKBANK.NS', 'BHARTIARTL.NS', 'ITC.NS', 'SBIN.NS',
    'LT.NS', 'HCLTECH.NS', 'ASIANPAINT.NS', 'MARUTI.NS', 'BAJFINANCE.NS',
    'WIPRO.NS', 'ULTRACEMCO.NS', 'AXISBANK.NS', 'TITAN.NS', 'SUNPHARMA.NS',
    'NESTLEIND.NS', 'POWERGRID.NS', 'NTPC.NS', 'ONGC.NS', 'TECHM.NS',
    'TATAMOTORS.NS', 'TATASTEEL.NS', 'JSWSTEEL.NS', 'HINDALCO.NS', 'COALINDIA.NS'
  ];

  // Sector mapping for Indian stocks
  private readonly SECTOR_MAPPING: { [key: string]: string } = {
    'RELIANCE': 'Energy',
    'TCS': 'Technology',
    'HDFCBANK': 'Banking',
    'INFY': 'Technology',
    'HINDUNILVR': 'Consumer Goods',
    'ICICIBANK': 'Banking',
    'KOTAKBANK': 'Banking',
    'BHARTIARTL': 'Telecommunications',
    'ITC': 'Consumer Goods',
    'SBIN': 'Banking',
    'LT': 'Construction',
    'HCLTECH': 'Technology',
    'ASIANPAINT': 'Chemicals',
    'MARUTI': 'Automotive',
    'BAJFINANCE': 'Financial Services',
    'WIPRO': 'Technology',
    'ULTRACEMCO': 'Cement',
    'AXISBANK': 'Banking',
    'TITAN': 'Consumer Goods',
    'SUNPHARMA': 'Pharmaceuticals'
  };

  constructor(private http: HttpClient) {}

  async getIndianStocks(): Promise<IndianStock[]> {
    try {
      // Try to fetch real data from Yahoo Finance API
      const realData = await this.fetchYahooFinanceStocks();
      if (realData && realData.length > 0) {
        console.log('Successfully fetched real stock data from Yahoo Finance');
        return realData;
      }
    } catch (error) {
      console.warn('Failed to fetch real Yahoo Finance data:', error);
    }

    try {
      // Try alternative API
      const alternativeData = await this.fetchAlternativeStocks();
      if (alternativeData && alternativeData.length > 0) {
        console.log('Successfully fetched real stock data from alternative API');
        return alternativeData;
      }
    } catch (error) {
      console.warn('Failed to fetch alternative data:', error);
    }

    console.log('Falling back to enhanced mock data');
    // Fallback to enhanced mock data
    return this.getEnhancedMockData();
  }

  private async fetchYahooFinanceStocks(): Promise<IndianStock[]> {
    try {
      const stocks: IndianStock[] = [];

      // Fetch data for popular Indian stocks from Yahoo Finance
      const popularStocks = this.INDIAN_STOCKS_YAHOO.slice(0, 15); // Limit to avoid too many requests

      for (const symbol of popularStocks) {
        try {
          const stockData = await this.fetchSingleYahooStock(symbol);
          if (stockData) {
            stocks.push(stockData);
          }
          // Add small delay to avoid rate limiting
          await this.delay(100);
        } catch (error) {
          console.warn(`Failed to fetch data for ${symbol}:`, error);
        }
      }

      return stocks;
    } catch (error) {
      console.error('Error fetching Yahoo Finance data:', error);
      throw error;
    }
  }

  private async fetchSingleYahooStock(symbol: string): Promise<IndianStock | null> {
    try {
      const url = `${this.YAHOO_FINANCE_API}/${symbol}`;
      const proxyUrl = `${this.CORS_PROXY}${encodeURIComponent(url)}`;

      const response = await this.http.get<any>(proxyUrl).toPromise();

      if (response && response.contents) {
        const data = JSON.parse(response.contents);
        if (data.chart && data.chart.result && data.chart.result[0]) {
          return this.mapYahooToIndianStock(data.chart.result[0], symbol);
        }
      }

      return null;
    } catch (error) {
      console.error(`Error fetching Yahoo data for ${symbol}:`, error);
      return null;
    }
  }

  private mapYahooToIndianStock(yahooData: any, symbol: string): IndianStock {
    const meta = yahooData.meta;
    const quote = yahooData.indicators?.quote?.[0];

    const cleanSymbol = symbol.replace('.NS', '');
    const currentPrice = meta.regularMarketPrice || 0;
    const previousClose = meta.previousClose || 0;
    const change = currentPrice - previousClose;
    const changePercent = previousClose > 0 ? (change / previousClose) * 100 : 0;

    return {
      symbol: cleanSymbol,
      name: this.getCompanyName(cleanSymbol),
      price: Math.round(currentPrice * 100) / 100,
      change: Math.round(change * 100) / 100,
      changePercent: Math.round(changePercent * 100) / 100,
      volume: quote?.volume?.[quote.volume.length - 1] || 0,
      high: meta.regularMarketDayHigh || 0,
      low: meta.regularMarketDayLow || 0,
      open: quote?.open?.[0] || 0,
      previousClose: previousClose,
      sector: this.SECTOR_MAPPING[cleanSymbol] || 'Others'
    };
  }

  private async fetchAlternativeStocks(): Promise<IndianStock[]> {
    try {
      // Using a free API that provides Indian stock data
      const url = 'https://latest-stock-price.p.rapidapi.com/price?Indices=NIFTY%2050';
      const proxyUrl = `${this.CORS_PROXY_RAW}${encodeURIComponent(url)}`;

      const response = await this.http.get<any>(proxyUrl).toPromise();

      if (response && Array.isArray(response)) {
        return response.slice(0, 20).map((stock: any) => this.mapAlternativeToIndianStock(stock));
      }

      return [];
    } catch (error) {
      console.error('Error fetching alternative stock data:', error);
      throw error;
    }
  }

  private mapAlternativeToIndianStock(stockData: any): IndianStock {
    const change = (stockData.lastPrice || 0) - (stockData.previousClose || 0);
    const changePercent = stockData.previousClose > 0 ? (change / stockData.previousClose) * 100 : 0;

    return {
      symbol: stockData.symbol || '',
      name: this.getCompanyName(stockData.symbol || ''),
      price: stockData.lastPrice || 0,
      change: Math.round(change * 100) / 100,
      changePercent: Math.round(changePercent * 100) / 100,
      volume: stockData.totalTradedVolume || 0,
      high: stockData.dayHigh || 0,
      low: stockData.dayLow || 0,
      open: stockData.open || 0,
      previousClose: stockData.previousClose || 0,
      sector: this.SECTOR_MAPPING[stockData.symbol] || 'Others'
    };
  }

  private getCompanyName(symbol: string): string {
    const companyNames: { [key: string]: string } = {
      'RELIANCE': 'Reliance Industries Limited',
      'TCS': 'Tata Consultancy Services',
      'HDFCBANK': 'HDFC Bank Limited',
      'INFY': 'Infosys Limited',
      'HINDUNILVR': 'Hindustan Unilever Limited',
      'ICICIBANK': 'ICICI Bank Limited',
      'KOTAKBANK': 'Kotak Mahindra Bank',
      'BHARTIARTL': 'Bharti Airtel Limited',
      'ITC': 'ITC Limited',
      'SBIN': 'State Bank of India',
      'LT': 'Larsen & Toubro Limited',
      'HCLTECH': 'HCL Technologies Limited',
      'ASIANPAINT': 'Asian Paints Limited',
      'MARUTI': 'Maruti Suzuki India Limited',
      'BAJFINANCE': 'Bajaj Finance Limited',
      'WIPRO': 'Wipro Limited',
      'ULTRACEMCO': 'UltraTech Cement Limited',
      'AXISBANK': 'Axis Bank Limited',
      'TITAN': 'Titan Company Limited',
      'SUNPHARMA': 'Sun Pharmaceutical Industries'
    };

    return companyNames[symbol] || symbol;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getEnhancedMockData(): IndianStock[] {
    // Enhanced mock data with more realistic Indian stock information
    const stocksData = [
      { symbol: 'RELIANCE', name: 'Reliance Industries Limited', basePrice: 2450, sector: 'Energy' },
      { symbol: 'TCS', name: 'Tata Consultancy Services', basePrice: 3650, sector: 'Technology' },
      { symbol: 'HDFCBANK', name: 'HDFC Bank Limited', basePrice: 1580, sector: 'Banking' },
      { symbol: 'INFY', name: 'Infosys Limited', basePrice: 1420, sector: 'Technology' },
      { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Limited', basePrice: 2380, sector: 'Consumer Goods' },
      { symbol: 'ICICIBANK', name: 'ICICI Bank Limited', basePrice: 950, sector: 'Banking' },
      { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank', basePrice: 1750, sector: 'Banking' },
      { symbol: 'BHARTIARTL', name: 'Bharti Airtel Limited', basePrice: 850, sector: 'Telecommunications' },
      { symbol: 'ITC', name: 'ITC Limited', basePrice: 420, sector: 'Consumer Goods' },
      { symbol: 'SBIN', name: 'State Bank of India', basePrice: 580, sector: 'Banking' },
      { symbol: 'LT', name: 'Larsen & Toubro Limited', basePrice: 2150, sector: 'Construction' },
      { symbol: 'HCLTECH', name: 'HCL Technologies Limited', basePrice: 1180, sector: 'Technology' },
      { symbol: 'ASIANPAINT', name: 'Asian Paints Limited', basePrice: 3250, sector: 'Chemicals' },
      { symbol: 'MARUTI', name: 'Maruti Suzuki India Limited', basePrice: 9850, sector: 'Automotive' },
      { symbol: 'BAJFINANCE', name: 'Bajaj Finance Limited', basePrice: 6750, sector: 'Financial Services' },
      { symbol: 'WIPRO', name: 'Wipro Limited', basePrice: 420, sector: 'Technology' },
      { symbol: 'ULTRACEMCO', name: 'UltraTech Cement Limited', basePrice: 8950, sector: 'Cement' },
      { symbol: 'AXISBANK', name: 'Axis Bank Limited', basePrice: 1050, sector: 'Banking' },
      { symbol: 'TITAN', name: 'Titan Company Limited', basePrice: 2850, sector: 'Consumer Goods' },
      { symbol: 'SUNPHARMA', name: 'Sun Pharmaceutical Industries', basePrice: 1150, sector: 'Pharmaceuticals' },
      { symbol: 'NESTLEIND', name: 'Nestle India Limited', basePrice: 22500, sector: 'Consumer Goods' },
      { symbol: 'POWERGRID', name: 'Power Grid Corporation', basePrice: 220, sector: 'Utilities' },
      { symbol: 'NTPC', name: 'NTPC Limited', basePrice: 180, sector: 'Utilities' },
      { symbol: 'ONGC', name: 'Oil & Natural Gas Corporation', basePrice: 160, sector: 'Energy' },
      { symbol: 'TECHM', name: 'Tech Mahindra Limited', basePrice: 1450, sector: 'Technology' },
      { symbol: 'TATAMOTORS', name: 'Tata Motors Limited', basePrice: 750, sector: 'Automotive' },
      { symbol: 'TATASTEEL', name: 'Tata Steel Limited', basePrice: 120, sector: 'Steel' },
      { symbol: 'JSWSTEEL', name: 'JSW Steel Limited', basePrice: 850, sector: 'Steel' },
      { symbol: 'HINDALCO', name: 'Hindalco Industries Limited', basePrice: 450, sector: 'Metals' },
      { symbol: 'COALINDIA', name: 'Coal India Limited', basePrice: 280, sector: 'Mining' }
    ];

    return stocksData.map(stock => {
      const priceVariation = (Math.random() - 0.5) * 0.1; // ±5% variation
      const currentPrice = stock.basePrice * (1 + priceVariation);
      const change = currentPrice - stock.basePrice;
      const changePercent = (change / stock.basePrice) * 100;
      const volume = Math.floor(Math.random() * 50000000) + 1000000; // 1M to 50M volume

      return {
        symbol: stock.symbol,
        name: stock.name,
        price: Math.round(currentPrice * 100) / 100,
        change: Math.round(change * 100) / 100,
        changePercent: Math.round(changePercent * 100) / 100,
        volume: volume,
        high: Math.round(currentPrice * 1.02 * 100) / 100,
        low: Math.round(currentPrice * 0.98 * 100) / 100,
        open: Math.round(stock.basePrice * (1 + (Math.random() - 0.5) * 0.02) * 100) / 100,
        previousClose: stock.basePrice,
        sector: stock.sector,
        marketCap: Math.floor((currentPrice * Math.random() * 1000000000) / 10000000) // Market cap in crores
      };
    });
  }

  // Fetch all available stocks from multiple sources
  async getAllIndianStocks(): Promise<IndianStock[]> {
    try {
      const allStocks: IndianStock[] = [];

      // Fetch all stocks from Yahoo Finance
      console.log('Fetching all Indian stocks from Yahoo Finance...');
      const yahooStocks = await this.fetchAllYahooStocks();
      allStocks.push(...yahooStocks);

      // If we don't have enough real data, try alternative sources
      if (allStocks.length < 20) {
        try {
          const alternativeStocks = await this.fetchAlternativeStocks();
          allStocks.push(...alternativeStocks);
        } catch (error) {
          console.warn('Failed to fetch alternative stocks:', error);
        }
      }

      // Remove duplicates based on symbol
      const uniqueStocks = allStocks.filter((stock, index, self) =>
        index === self.findIndex(s => s.symbol === stock.symbol)
      );

      if (uniqueStocks.length > 0) {
        console.log(`Successfully fetched ${uniqueStocks.length} real stocks`);
        return uniqueStocks;
      }

      console.log('No real data available, using enhanced mock data');
      return this.getEnhancedMockData();
    } catch (error) {
      console.error('Error fetching all Indian stocks:', error);
      return this.getEnhancedMockData();
    }
  }

  private async fetchAllYahooStocks(): Promise<IndianStock[]> {
    try {
      const stocks: IndianStock[] = [];

      // Fetch data for all Indian stocks from Yahoo Finance
      for (const symbol of this.INDIAN_STOCKS_YAHOO) {
        try {
          const stockData = await this.fetchSingleYahooStock(symbol);
          if (stockData) {
            stocks.push(stockData);
          }
          // Add small delay to avoid rate limiting
          await this.delay(50);
        } catch (error) {
          console.warn(`Failed to fetch data for ${symbol}:`, error);
        }
      }

      return stocks;
    } catch (error) {
      console.error('Error fetching all Yahoo Finance stocks:', error);
      return [];
    }
  }

  // Method to get real-time data
  async getRealTimePrice(symbol: string): Promise<any> {
    try {
      // Try to fetch real-time data from Yahoo Finance
      const yahooSymbol = `${symbol}.NS`;
      const url = `${this.YAHOO_FINANCE_API}/${yahooSymbol}`;
      const proxyUrl = `${this.CORS_PROXY}${encodeURIComponent(url)}`;

      const response = await this.http.get<any>(proxyUrl).toPromise();

      if (response && response.contents) {
        const data = JSON.parse(response.contents);
        if (data.chart && data.chart.result && data.chart.result[0]) {
          const meta = data.chart.result[0].meta;
          const currentPrice = meta.regularMarketPrice || 0;
          const previousClose = meta.previousClose || 0;
          const change = currentPrice - previousClose;

          return {
            price: currentPrice.toFixed(2),
            change: change.toFixed(2),
            changePercent: previousClose > 0 ? ((change / previousClose) * 100).toFixed(2) : '0.00'
          };
        }
      }

      // Fallback to mock data
      return {
        price: (Math.random() * 3000 + 100).toFixed(2),
        change: ((Math.random() - 0.5) * 100).toFixed(2)
      };
    } catch (error) {
      console.error('Error fetching real-time price:', error);
      return {
        price: (Math.random() * 3000 + 100).toFixed(2),
        change: ((Math.random() - 0.5) * 100).toFixed(2)
      };
    }
  }

  // Get top gainers
  getTopGainers(stocks: IndianStock[]): IndianStock[] {
    return stocks
      .filter(stock => stock.changePercent > 0)
      .sort((a, b) => b.changePercent - a.changePercent)
      .slice(0, 5);
  }

  // Get top losers
  getTopLosers(stocks: IndianStock[]): IndianStock[] {
    return stocks
      .filter(stock => stock.changePercent < 0)
      .sort((a, b) => a.changePercent - b.changePercent)
      .slice(0, 5);
  }

  // Get stocks by sector
  getStocksBySector(stocks: IndianStock[], sector: string): IndianStock[] {
    return stocks.filter(stock => stock.sector === sector);
  }

  // Get available sectors
  getAvailableSectors(stocks: IndianStock[]): string[] {
    const sectors = stocks.map(stock => stock.sector).filter(Boolean);
    return [...new Set(sectors)] as string[];
  }
}
