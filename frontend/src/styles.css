@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }
  
  .btn-primary {
    @apply bg-primary-500 text-white hover:bg-primary-600;
  }
  
  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600;
  }
  
  .btn-danger {
    @apply bg-danger-500 text-white hover:bg-danger-600;
  }
  
  .btn-outline {
    @apply border border-gray-300 text-gray-700 hover:bg-gray-50;
  }
  
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }
  
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
}
