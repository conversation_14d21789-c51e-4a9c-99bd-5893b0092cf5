{"ast": null, "code": "import { AsyncAction } from './AsyncAction';\nimport { immediateProvider } from './immediateProvider';\nexport class AsapAction extends AsyncAction {\n  constructor(scheduler, work) {\n    super(scheduler, work);\n    this.scheduler = scheduler;\n    this.work = work;\n  }\n  requestAsyncId(scheduler, id, delay = 0) {\n    if (delay !== null && delay > 0) {\n      return super.requestAsyncId(scheduler, id, delay);\n    }\n    scheduler.actions.push(this);\n    return scheduler._scheduled || (scheduler._scheduled = immediateProvider.setImmediate(scheduler.flush.bind(scheduler, undefined)));\n  }\n  recycleAsyncId(scheduler, id, delay = 0) {\n    var _a;\n    if (delay != null ? delay > 0 : this.delay > 0) {\n      return super.recycleAsyncId(scheduler, id, delay);\n    }\n    const {\n      actions\n    } = scheduler;\n    if (id != null && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n      immediateProvider.clearImmediate(id);\n      if (scheduler._scheduled === id) {\n        scheduler._scheduled = undefined;\n      }\n    }\n    return undefined;\n  }\n}", "map": {"version": 3, "names": ["AsyncAction", "immediate<PERSON>rovider", "AsapAction", "constructor", "scheduler", "work", "requestAsyncId", "id", "delay", "actions", "push", "_scheduled", "setImmediate", "flush", "bind", "undefined", "recycleAsyncId", "_a", "length", "clearImmediate"], "sources": ["/var/www/html/trading-app/node_modules/rxjs/dist/esm/internal/scheduler/AsapAction.js"], "sourcesContent": ["import { AsyncAction } from './AsyncAction';\nimport { immediateProvider } from './immediateProvider';\nexport class AsapAction extends AsyncAction {\n    constructor(scheduler, work) {\n        super(scheduler, work);\n        this.scheduler = scheduler;\n        this.work = work;\n    }\n    requestAsyncId(scheduler, id, delay = 0) {\n        if (delay !== null && delay > 0) {\n            return super.requestAsyncId(scheduler, id, delay);\n        }\n        scheduler.actions.push(this);\n        return scheduler._scheduled || (scheduler._scheduled = immediateProvider.setImmediate(scheduler.flush.bind(scheduler, undefined)));\n    }\n    recycleAsyncId(scheduler, id, delay = 0) {\n        var _a;\n        if (delay != null ? delay > 0 : this.delay > 0) {\n            return super.recycleAsyncId(scheduler, id, delay);\n        }\n        const { actions } = scheduler;\n        if (id != null && ((_a = actions[actions.length - 1]) === null || _a === void 0 ? void 0 : _a.id) !== id) {\n            immediateProvider.clearImmediate(id);\n            if (scheduler._scheduled === id) {\n                scheduler._scheduled = undefined;\n            }\n        }\n        return undefined;\n    }\n}\n"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,iBAAiB,QAAQ,qBAAqB;AACvD,OAAO,MAAMC,UAAU,SAASF,WAAW,CAAC;EACxCG,WAAWA,CAACC,SAAS,EAAEC,IAAI,EAAE;IACzB,KAAK,CAACD,SAAS,EAAEC,IAAI,CAAC;IACtB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;EACAC,cAAcA,CAACF,SAAS,EAAEG,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,GAAG,CAAC,EAAE;MAC7B,OAAO,KAAK,CAACF,cAAc,CAACF,SAAS,EAAEG,EAAE,EAAEC,KAAK,CAAC;IACrD;IACAJ,SAAS,CAACK,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;IAC5B,OAAON,SAAS,CAACO,UAAU,KAAKP,SAAS,CAACO,UAAU,GAAGV,iBAAiB,CAACW,YAAY,CAACR,SAAS,CAACS,KAAK,CAACC,IAAI,CAACV,SAAS,EAAEW,SAAS,CAAC,CAAC,CAAC;EACtI;EACAC,cAAcA,CAACZ,SAAS,EAAEG,EAAE,EAAEC,KAAK,GAAG,CAAC,EAAE;IACrC,IAAIS,EAAE;IACN,IAAIT,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,EAAE;MAC5C,OAAO,KAAK,CAACQ,cAAc,CAACZ,SAAS,EAAEG,EAAE,EAAEC,KAAK,CAAC;IACrD;IACA,MAAM;MAAEC;IAAQ,CAAC,GAAGL,SAAS;IAC7B,IAAIG,EAAE,IAAI,IAAI,IAAI,CAAC,CAACU,EAAE,GAAGR,OAAO,CAACA,OAAO,CAACS,MAAM,GAAG,CAAC,CAAC,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACV,EAAE,MAAMA,EAAE,EAAE;MACtGN,iBAAiB,CAACkB,cAAc,CAACZ,EAAE,CAAC;MACpC,IAAIH,SAAS,CAACO,UAAU,KAAKJ,EAAE,EAAE;QAC7BH,SAAS,CAACO,UAAU,GAAGI,SAAS;MACpC;IACJ;IACA,OAAOA,SAAS;EACpB;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}