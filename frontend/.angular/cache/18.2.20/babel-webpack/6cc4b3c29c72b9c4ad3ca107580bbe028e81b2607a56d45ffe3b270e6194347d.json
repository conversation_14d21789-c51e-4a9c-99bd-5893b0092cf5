{"ast": null, "code": "import _asyncToGenerator from \"/var/www/html/trading-app/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/indian-stocks.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _forTrack0 = ($index, $item) => $item.symbol;\nfunction IndianStocksComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" Show Popular (\", ctx_r0.popularStocksCount, \") \");\n  }\n}\nfunction IndianStocksComponent_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" Show All Stocks (\", ctx_r0.allStocksCount, \") \");\n  }\n}\nfunction IndianStocksComponent_Conditional_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n    i0.ɵɵtext(1, \" Fetching Real Data... \");\n  }\n}\nfunction IndianStocksComponent_Conditional_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0, \" \\uD83D\\uDD04 Get Real Data \");\n  }\n}\nfunction IndianStocksComponent_Conditional_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n}\nfunction IndianStocksComponent_Conditional_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.topGainer.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.topGainer.symbol);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"+\", ctx_r0.topGainer.changePercent.toFixed(2), \"%\");\n  }\n}\nfunction IndianStocksComponent_Conditional_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 15);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 38);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 40);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.topLoser.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.topLoser.symbol);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.topLoser.changePercent.toFixed(2), \"%\");\n  }\n}\nfunction IndianStocksComponent_For_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 22);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const sector_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", sector_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(sector_r2);\n  }\n}\nfunction IndianStocksComponent_For_96_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 33)(1, \"td\")(2, \"div\")(3, \"div\", 41);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 43);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\", 15);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\")(21, \"button\", 44);\n    i0.ɵɵtext(22, \"Trade\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const stock_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(stock_r3.symbol);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stock_r3.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", stock_r3.sector, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", stock_r3.price.toFixed(2), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(stock_r3.change >= 0 ? \"text-green-600\" : \"text-red-600\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", stock_r3.change >= 0 ? \"+\" : \"\", \"\\u20B9\", stock_r3.change.toFixed(2), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(stock_r3.changePercent >= 0 ? \"text-green-600\" : \"text-red-600\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", stock_r3.changePercent >= 0 ? \"+\" : \"\", \"\", stock_r3.changePercent.toFixed(2), \"% \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formatVolume(stock_r3.volume));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", stock_r3.marketCap == null ? null : stock_r3.marketCap.toLocaleString(), \"\");\n  }\n}\nfunction IndianStocksComponent_ForEmpty_97_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 45);\n    i0.ɵɵtext(2, \" No stocks found matching your criteria \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IndianStocksComponent_Conditional_104_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 46);\n    i0.ɵɵtext(1, \" Live NSE Data \");\n  }\n}\nfunction IndianStocksComponent_Conditional_105_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 47);\n    i0.ɵɵtext(1, \" Demo Data \");\n  }\n}\nfunction IndianStocksComponent_Conditional_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 37);\n    i0.ɵɵtext(1, \" (Real NSE API may be blocked by CORS policy) \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class IndianStocksComponent {\n  constructor(indianStocksService) {\n    this.indianStocksService = indianStocksService;\n    this.stocks = [];\n    this.allStocks = [];\n    this.filteredStocks = [];\n    this.availableSectors = [];\n    this.selectedSector = '';\n    this.searchTerm = '';\n    this.sortBy = 'name';\n    this.loading = false;\n    this.lastUpdated = new Date();\n    this.showAllStocks = false;\n    this.isRealData = false;\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.loadStocks();\n    })();\n  }\n  loadStocks() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.loading = true;\n      _this2.isRealData = false; // Reset flag\n      try {\n        if (_this2.showAllStocks) {\n          _this2.allStocks = yield _this2.indianStocksService.getAllIndianStocks();\n          _this2.stocks = _this2.allStocks;\n        } else {\n          _this2.stocks = yield _this2.indianStocksService.getIndianStocks();\n        }\n        // Check if we got real data by looking for realistic price variations\n        _this2.isRealData = _this2.detectRealData(_this2.stocks);\n        _this2.availableSectors = _this2.indianStocksService.getAvailableSectors(_this2.stocks);\n        _this2.updateMarketSummary();\n        _this2.filterStocks();\n        _this2.lastUpdated = new Date();\n        if (_this2.isRealData) {\n          console.log('✅ Successfully loaded real stock data!');\n        } else {\n          console.log('⚠️ Using demo data (real API may be blocked)');\n        }\n      } catch (error) {\n        console.error('Error loading stocks:', error);\n        _this2.isRealData = false;\n      } finally {\n        _this2.loading = false;\n      }\n    })();\n  }\n  detectRealData(stocks) {\n    if (stocks.length === 0) return false;\n    // Check for realistic price variations and non-round numbers\n    const hasRealisticPrices = stocks.some(stock => {\n      const price = stock.price;\n      // Real stock prices are rarely perfect round numbers\n      return price > 0 && (price % 1 !== 0 || price % 10 !== 0);\n    });\n    // Check for realistic volume numbers\n    const hasRealisticVolume = stocks.some(stock => {\n      return stock.volume > 0 && stock.volume !== Math.floor(stock.volume);\n    });\n    // Check if we have proper company names (not just symbols)\n    const hasCompanyNames = stocks.some(stock => {\n      return stock.name && stock.name !== stock.symbol && stock.name.includes('Limited');\n    });\n    return hasRealisticPrices || hasRealisticVolume || hasCompanyNames;\n  }\n  toggleAllStocks() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.showAllStocks = !_this3.showAllStocks;\n      yield _this3.loadStocks();\n    })();\n  }\n  refreshData() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      yield _this4.loadStocks();\n    })();\n  }\n  forceRealDataRefresh() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      console.log('🔄 Force refreshing with real data...');\n      _this5.loading = true;\n      _this5.isRealData = false;\n      try {\n        // Clear any cached data and force fresh fetch\n        if (_this5.showAllStocks) {\n          _this5.allStocks = yield _this5.indianStocksService.getAllIndianStocks();\n          _this5.stocks = _this5.allStocks;\n        } else {\n          _this5.stocks = yield _this5.indianStocksService.getIndianStocks();\n        }\n        _this5.isRealData = _this5.detectRealData(_this5.stocks);\n        _this5.availableSectors = _this5.indianStocksService.getAvailableSectors(_this5.stocks);\n        _this5.updateMarketSummary();\n        _this5.filterStocks();\n        _this5.lastUpdated = new Date();\n        if (_this5.isRealData) {\n          console.log('✅ Successfully fetched real data!');\n        } else {\n          console.log('⚠️ Still using demo data - real APIs may be blocked');\n        }\n      } catch (error) {\n        console.error('Error force refreshing data:', error);\n      } finally {\n        _this5.loading = false;\n      }\n    })();\n  }\n  filterStocks() {\n    let filtered = [...this.stocks];\n    // Filter by sector\n    if (this.selectedSector) {\n      filtered = this.indianStocksService.getStocksBySector(filtered, this.selectedSector);\n    }\n    // Filter by search term\n    if (this.searchTerm) {\n      const term = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(stock => stock.name.toLowerCase().includes(term) || stock.symbol.toLowerCase().includes(term));\n    }\n    this.filteredStocks = filtered;\n    this.sortStocks();\n  }\n  sortStocks() {\n    this.filteredStocks.sort((a, b) => {\n      const aValue = a[this.sortBy];\n      const bValue = b[this.sortBy];\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return aValue.localeCompare(bValue);\n      }\n      if (typeof aValue === 'number' && typeof bValue === 'number') {\n        return bValue - aValue; // Descending order for numbers\n      }\n      return 0;\n    });\n  }\n  updateMarketSummary() {\n    const gainers = this.indianStocksService.getTopGainers(this.stocks);\n    const losers = this.indianStocksService.getTopLosers(this.stocks);\n    this.topGainer = gainers[0];\n    this.topLoser = losers[0];\n  }\n  getGainersCount() {\n    return this.stocks.filter(stock => stock.changePercent > 0).length;\n  }\n  getLosersCount() {\n    return this.stocks.filter(stock => stock.changePercent < 0).length;\n  }\n  formatVolume(volume) {\n    if (volume >= 10000000) {\n      return (volume / 10000000).toFixed(1) + 'Cr';\n    } else if (volume >= 100000) {\n      return (volume / 100000).toFixed(1) + 'L';\n    } else if (volume >= 1000) {\n      return (volume / 1000).toFixed(1) + 'K';\n    }\n    return volume.toString();\n  }\n  get popularStocksCount() {\n    return 30; // Popular stocks count\n  }\n  get allStocksCount() {\n    return this.allStocks.length || 150; // Estimated all stocks count\n  }\n  static {\n    this.ɵfac = function IndianStocksComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IndianStocksComponent)(i0.ɵɵdirectiveInject(i1.IndianStocksService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IndianStocksComponent,\n      selectors: [[\"app-indian-stocks\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 107,\n      vars: 23,\n      consts: [[1, \"space-y-6\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-900\"], [1, \"text-gray-600\", \"mt-1\"], [1, \"flex\", \"gap-3\"], [1, \"btn\", \"btn-outline\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-success\", 3, \"click\", \"disabled\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"inline-block\", \"animate-spin\", \"rounded-full\", \"h-4\", \"w-4\", \"border-b-2\", \"border-white\", \"mr-2\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"card\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\", \"mb-2\"], [1, \"space-y-2\"], [1, \"flex\", \"justify-between\"], [1, \"text-gray-600\"], [1, \"font-medium\"], [1, \"font-medium\", \"text-green-600\"], [1, \"font-medium\", \"text-red-600\"], [1, \"flex\", \"flex-wrap\", \"gap-4\", \"items-center\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [1, \"input\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"\"], [3, \"value\"], [\"type\", \"text\", \"placeholder\", \"Search stocks...\", 1, \"input\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"name\"], [\"value\", \"price\"], [\"value\", \"change\"], [\"value\", \"changePercent\"], [\"value\", \"volume\"], [1, \"overflow-x-auto\"], [1, \"table\"], [1, \"bg-gray-50\"], [1, \"divide-y\", \"divide-gray-200\"], [1, \"hover:bg-gray-50\"], [1, \"text-center\", \"text-sm\", \"text-gray-500\", \"space-y-2\"], [1, \"flex\", \"justify-center\", \"items-center\", \"gap-2\"], [1, \"inline-flex\", \"items-center\", \"px-2.5\", \"py-0.5\", \"rounded-full\", \"text-xs\", \"font-medium\"], [1, \"text-xs\", \"text-gray-400\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"text-lg\", \"font-bold\", \"text-green-600\"], [1, \"text-lg\", \"font-bold\", \"text-red-600\"], [1, \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"inline-flex\", \"items-center\", \"px-2.5\", \"py-0.5\", \"rounded-full\", \"text-xs\", \"font-medium\", \"bg-blue-100\", \"text-blue-800\"], [1, \"btn\", \"btn-primary\", \"text-sm\"], [\"colspan\", \"8\", 1, \"text-center\", \"py-8\", \"text-gray-500\"], [1, \"w-2\", \"h-2\", \"bg-green-400\", \"rounded-full\", \"mr-1\"], [1, \"w-2\", \"h-2\", \"bg-yellow-400\", \"rounded-full\", \"mr-1\"]],\n      template: function IndianStocksComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\")(3, \"h1\", 2);\n          i0.ɵɵtext(4, \"Indian Stocks\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 3);\n          i0.ɵɵtext(6, \"Live prices from National Stock Exchange (NSE)\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 4)(8, \"button\", 5);\n          i0.ɵɵlistener(\"click\", function IndianStocksComponent_Template_button_click_8_listener() {\n            return ctx.toggleAllStocks();\n          });\n          i0.ɵɵtemplate(9, IndianStocksComponent_Conditional_9_Template, 1, 1)(10, IndianStocksComponent_Conditional_10_Template, 1, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function IndianStocksComponent_Template_button_click_11_listener() {\n            return ctx.forceRealDataRefresh();\n          });\n          i0.ɵɵtemplate(12, IndianStocksComponent_Conditional_12_Template, 2, 0)(13, IndianStocksComponent_Conditional_13_Template, 1, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 7);\n          i0.ɵɵlistener(\"click\", function IndianStocksComponent_Template_button_click_14_listener() {\n            return ctx.refreshData();\n          });\n          i0.ɵɵtemplate(15, IndianStocksComponent_Conditional_15_Template, 1, 0, \"span\", 8);\n          i0.ɵɵtext(16, \" Refresh \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 9)(18, \"div\", 10)(19, \"h3\", 11);\n          i0.ɵɵtext(20, \"Market Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 12)(22, \"div\", 13)(23, \"span\", 14);\n          i0.ɵɵtext(24, \"Total Stocks:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 15);\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"div\", 13)(28, \"span\", 14);\n          i0.ɵɵtext(29, \"Gainers:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"span\", 16);\n          i0.ɵɵtext(31);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(32, \"div\", 13)(33, \"span\", 14);\n          i0.ɵɵtext(34, \"Losers:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"span\", 17);\n          i0.ɵɵtext(36);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"div\", 10)(38, \"h3\", 11);\n          i0.ɵɵtext(39, \"Top Gainer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(40, IndianStocksComponent_Conditional_40_Template, 7, 3, \"div\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 10)(42, \"h3\", 11);\n          i0.ɵɵtext(43, \"Top Loser\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(44, IndianStocksComponent_Conditional_44_Template, 7, 3, \"div\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(45, \"div\", 10)(46, \"div\", 18)(47, \"div\")(48, \"label\", 19);\n          i0.ɵɵtext(49, \"Filter by Sector\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"select\", 20);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function IndianStocksComponent_Template_select_ngModelChange_50_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedSector, $event) || (ctx.selectedSector = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function IndianStocksComponent_Template_select_ngModelChange_50_listener() {\n            return ctx.filterStocks();\n          });\n          i0.ɵɵelementStart(51, \"option\", 21);\n          i0.ɵɵtext(52, \"All Sectors\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵrepeaterCreate(53, IndianStocksComponent_For_54_Template, 2, 2, \"option\", 22, i0.ɵɵrepeaterTrackByIdentity);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"div\")(56, \"label\", 19);\n          i0.ɵɵtext(57, \"Search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"input\", 23);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function IndianStocksComponent_Template_input_ngModelChange_58_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function IndianStocksComponent_Template_input_ngModelChange_58_listener() {\n            return ctx.filterStocks();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(59, \"div\")(60, \"label\", 19);\n          i0.ɵɵtext(61, \"Sort by\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"select\", 20);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function IndianStocksComponent_Template_select_ngModelChange_62_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.sortBy, $event) || (ctx.sortBy = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function IndianStocksComponent_Template_select_ngModelChange_62_listener() {\n            return ctx.sortStocks();\n          });\n          i0.ɵɵelementStart(63, \"option\", 24);\n          i0.ɵɵtext(64, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"option\", 25);\n          i0.ɵɵtext(66, \"Price\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"option\", 26);\n          i0.ɵɵtext(68, \"Change\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"option\", 27);\n          i0.ɵɵtext(70, \"Change %\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"option\", 28);\n          i0.ɵɵtext(72, \"Volume\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(73, \"div\", 10)(74, \"div\", 29)(75, \"table\", 30)(76, \"thead\", 31)(77, \"tr\")(78, \"th\");\n          i0.ɵɵtext(79, \"Stock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(80, \"th\");\n          i0.ɵɵtext(81, \"Sector\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"th\");\n          i0.ɵɵtext(83, \"Price (\\u20B9)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"th\");\n          i0.ɵɵtext(85, \"Change\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"th\");\n          i0.ɵɵtext(87, \"Change %\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"th\");\n          i0.ɵɵtext(89, \"Volume\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(90, \"th\");\n          i0.ɵɵtext(91, \"Market Cap (Cr)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"th\");\n          i0.ɵɵtext(93, \"Action\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(94, \"tbody\", 32);\n          i0.ɵɵrepeaterCreate(95, IndianStocksComponent_For_96_Template, 23, 14, \"tr\", 33, _forTrack0, false, IndianStocksComponent_ForEmpty_97_Template, 3, 0, \"tr\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(98, \"div\", 34)(99, \"div\");\n          i0.ɵɵtext(100);\n          i0.ɵɵpipe(101, \"date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"div\", 35)(103, \"span\", 36);\n          i0.ɵɵtemplate(104, IndianStocksComponent_Conditional_104_Template, 2, 0)(105, IndianStocksComponent_Conditional_105_Template, 2, 0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(106, IndianStocksComponent_Conditional_106_Template, 2, 0, \"span\", 37);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.showAllStocks ? 9 : 10);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.loading ? 12 : 13);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.loading ? 15 : -1);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.stocks.length);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.getGainersCount());\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.getLosersCount());\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(ctx.topGainer ? 40 : -1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(ctx.topLoser ? 44 : -1);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedSector);\n          i0.ɵɵadvance(3);\n          i0.ɵɵrepeater(ctx.availableSectors);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.sortBy);\n          i0.ɵɵadvance(33);\n          i0.ɵɵrepeater(ctx.filteredStocks);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"Last updated: \", i0.ɵɵpipeBind2(101, 20, ctx.lastUpdated, \"medium\"), \"\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵclassMap(ctx.isRealData ? \"bg-green-100 text-green-800\" : \"bg-yellow-100 text-yellow-800\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.isRealData ? 104 : 105);\n          i0.ɵɵadvance(2);\n          i0.ɵɵconditional(!ctx.isRealData ? 106 : -1);\n        }\n      },\n      dependencies: [CommonModule, i2.DatePipe, FormsModule, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵtext", "ɵɵtextInterpolate1", "ctx_r0", "popularStocksCount", "allStocksCount", "ɵɵelement", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "<PERSON><PERSON><PERSON><PERSON>", "name", "symbol", "changePercent", "toFixed", "topLoser", "ɵɵproperty", "sector_r2", "stock_r3", "sector", "price", "ɵɵclassMap", "change", "ɵɵtextInterpolate2", "formatVolume", "volume", "marketCap", "toLocaleString", "IndianStocksComponent", "constructor", "indianStocksService", "stocks", "allStocks", "filteredStocks", "availableSectors", "selectedSector", "searchTerm", "sortBy", "loading", "lastUpdated", "Date", "showAllStocks", "isRealData", "ngOnInit", "_this", "_asyncToGenerator", "loadStocks", "_this2", "getAllIndianStocks", "getIndianStocks", "detectRealData", "getAvailableSectors", "updateMarketSummary", "filterStocks", "console", "log", "error", "length", "hasRealisticPrices", "some", "stock", "hasRealisticVolume", "Math", "floor", "hasCompanyNames", "includes", "toggleAllStocks", "_this3", "refreshData", "_this4", "forceRealDataRefresh", "_this5", "filtered", "getStocksBySector", "term", "toLowerCase", "filter", "sortStocks", "sort", "a", "b", "aValue", "bValue", "localeCompare", "gainers", "getTopGainers", "losers", "getTopLosers", "getGainersCount", "getLosersCount", "toString", "ɵɵdirectiveInject", "i1", "IndianStocksService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "IndianStocksComponent_Template", "rf", "ctx", "ɵɵlistener", "IndianStocksComponent_Template_button_click_8_listener", "ɵɵtemplate", "IndianStocksComponent_Conditional_9_Template", "IndianStocksComponent_Conditional_10_Template", "IndianStocksComponent_Template_button_click_11_listener", "IndianStocksComponent_Conditional_12_Template", "IndianStocksComponent_Conditional_13_Template", "IndianStocksComponent_Template_button_click_14_listener", "IndianStocksComponent_Conditional_15_Template", "IndianStocksComponent_Conditional_40_Template", "IndianStocksComponent_Conditional_44_Template", "ɵɵtwoWayListener", "IndianStocksComponent_Template_select_ngModelChange_50_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵrepeaterCreate", "IndianStocksComponent_For_54_Template", "ɵɵrepeaterTrackByIdentity", "IndianStocksComponent_Template_input_ngModelChange_58_listener", "IndianStocksComponent_Template_select_ngModelChange_62_listener", "IndianStocksComponent_For_96_Template", "_forTrack0", "IndianStocksComponent_ForEmpty_97_Template", "IndianStocksComponent_Conditional_104_Template", "IndianStocksComponent_Conditional_105_Template", "IndianStocksComponent_Conditional_106_Template", "ɵɵconditional", "ɵɵtwoWayProperty", "ɵɵrepeater", "ɵɵpipeBind2", "i2", "DatePipe", "i3", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgModel", "encapsulation"], "sources": ["/var/www/html/trading-app/frontend/src/app/pages/indian-stocks/indian-stocks.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IndianStocksService, IndianStock } from '../../services/indian-stocks.service';\n\n@Component({\n  selector: 'app-indian-stocks',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"space-y-6\">\n      <!-- Header -->\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900\">Indian Stocks</h1>\n          <p class=\"text-gray-600 mt-1\">Live prices from National Stock Exchange (NSE)</p>\n        </div>\n        <div class=\"flex gap-3\">\n          <button\n            (click)=\"toggleAllStocks()\"\n            [disabled]=\"loading\"\n            class=\"btn btn-outline\">\n            @if (showAllStocks) {\n              Show Popular ({{ popularStocksCount }})\n            } @else {\n              Show All Stocks ({{ allStocksCount }})\n            }\n          </button>\n          <button\n            (click)=\"forceRealDataRefresh()\"\n            [disabled]=\"loading\"\n            class=\"btn btn-success\">\n            @if (loading) {\n              <span class=\"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></span>\n              Fetching Real Data...\n            } @else {\n              🔄 Get Real Data\n            }\n          </button>\n          <button\n            (click)=\"refreshData()\"\n            [disabled]=\"loading\"\n            class=\"btn btn-primary\">\n            @if (loading) {\n              <span class=\"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></span>\n            }\n            Refresh\n          </button>\n        </div>\n      </div>\n\n      <!-- Market Summary -->\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div class=\"card\">\n          <h3 class=\"text-lg font-medium text-gray-900 mb-2\">Market Overview</h3>\n          <div class=\"space-y-2\">\n            <div class=\"flex justify-between\">\n              <span class=\"text-gray-600\">Total Stocks:</span>\n              <span class=\"font-medium\">{{ stocks.length }}</span>\n            </div>\n            <div class=\"flex justify-between\">\n              <span class=\"text-gray-600\">Gainers:</span>\n              <span class=\"font-medium text-green-600\">{{ getGainersCount() }}</span>\n            </div>\n            <div class=\"flex justify-between\">\n              <span class=\"text-gray-600\">Losers:</span>\n              <span class=\"font-medium text-red-600\">{{ getLosersCount() }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"card\">\n          <h3 class=\"text-lg font-medium text-gray-900 mb-2\">Top Gainer</h3>\n          @if (topGainer) {\n            <div>\n              <p class=\"font-medium\">{{ topGainer.name }}</p>\n              <p class=\"text-sm text-gray-600\">{{ topGainer.symbol }}</p>\n              <p class=\"text-lg font-bold text-green-600\">+{{ topGainer.changePercent.toFixed(2) }}%</p>\n            </div>\n          }\n        </div>\n\n        <div class=\"card\">\n          <h3 class=\"text-lg font-medium text-gray-900 mb-2\">Top Loser</h3>\n          @if (topLoser) {\n            <div>\n              <p class=\"font-medium\">{{ topLoser.name }}</p>\n              <p class=\"text-sm text-gray-600\">{{ topLoser.symbol }}</p>\n              <p class=\"text-lg font-bold text-red-600\">{{ topLoser.changePercent.toFixed(2) }}%</p>\n            </div>\n          }\n        </div>\n      </div>\n\n      <!-- Filters -->\n      <div class=\"card\">\n        <div class=\"flex flex-wrap gap-4 items-center\">\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-1\">Filter by Sector</label>\n            <select [(ngModel)]=\"selectedSector\" (ngModelChange)=\"filterStocks()\" class=\"input\">\n              <option value=\"\">All Sectors</option>\n              @for (sector of availableSectors; track sector) {\n                <option [value]=\"sector\">{{ sector }}</option>\n              }\n            </select>\n          </div>\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-1\">Search</label>\n            <input \n              type=\"text\" \n              [(ngModel)]=\"searchTerm\" \n              (ngModelChange)=\"filterStocks()\"\n              placeholder=\"Search stocks...\" \n              class=\"input\">\n          </div>\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-1\">Sort by</label>\n            <select [(ngModel)]=\"sortBy\" (ngModelChange)=\"sortStocks()\" class=\"input\">\n              <option value=\"name\">Name</option>\n              <option value=\"price\">Price</option>\n              <option value=\"change\">Change</option>\n              <option value=\"changePercent\">Change %</option>\n              <option value=\"volume\">Volume</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      <!-- Stocks Table -->\n      <div class=\"card\">\n        <div class=\"overflow-x-auto\">\n          <table class=\"table\">\n            <thead class=\"bg-gray-50\">\n              <tr>\n                <th>Stock</th>\n                <th>Sector</th>\n                <th>Price (₹)</th>\n                <th>Change</th>\n                <th>Change %</th>\n                <th>Volume</th>\n                <th>Market Cap (Cr)</th>\n                <th>Action</th>\n              </tr>\n            </thead>\n            <tbody class=\"divide-y divide-gray-200\">\n              @for (stock of filteredStocks; track stock.symbol) {\n                <tr class=\"hover:bg-gray-50\">\n                  <td>\n                    <div>\n                      <div class=\"font-medium text-gray-900\">{{ stock.symbol }}</div>\n                      <div class=\"text-sm text-gray-500\">{{ stock.name }}</div>\n                    </div>\n                  </td>\n                  <td>\n                    <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                      {{ stock.sector }}\n                    </span>\n                  </td>\n                  <td class=\"font-medium\">₹{{ stock.price.toFixed(2) }}</td>\n                  <td [class]=\"stock.change >= 0 ? 'text-green-600' : 'text-red-600'\">\n                    {{ stock.change >= 0 ? '+' : '' }}₹{{ stock.change.toFixed(2) }}\n                  </td>\n                  <td [class]=\"stock.changePercent >= 0 ? 'text-green-600' : 'text-red-600'\">\n                    {{ stock.changePercent >= 0 ? '+' : '' }}{{ stock.changePercent.toFixed(2) }}%\n                  </td>\n                  <td>{{ formatVolume(stock.volume) }}</td>\n                  <td>₹{{ stock.marketCap?.toLocaleString() }}</td>\n                  <td>\n                    <button class=\"btn btn-primary text-sm\">Trade</button>\n                  </td>\n                </tr>\n              } @empty {\n                <tr>\n                  <td colspan=\"8\" class=\"text-center py-8 text-gray-500\">\n                    No stocks found matching your criteria\n                  </td>\n                </tr>\n              }\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      <!-- Data Source Info -->\n      <div class=\"text-center text-sm text-gray-500 space-y-2\">\n        <div>Last updated: {{ lastUpdated | date:'medium' }}</div>\n        <div class=\"flex justify-center items-center gap-2\">\n          <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                [class]=\"isRealData ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'\">\n            @if (isRealData) {\n              <span class=\"w-2 h-2 bg-green-400 rounded-full mr-1\"></span>\n              Live NSE Data\n            } @else {\n              <span class=\"w-2 h-2 bg-yellow-400 rounded-full mr-1\"></span>\n              Demo Data\n            }\n          </span>\n          @if (!isRealData) {\n            <span class=\"text-xs text-gray-400\">\n              (Real NSE API may be blocked by CORS policy)\n            </span>\n          }\n        </div>\n      </div>\n    </div>\n  `\n})\nexport class IndianStocksComponent implements OnInit {\n  stocks: IndianStock[] = [];\n  allStocks: IndianStock[] = [];\n  filteredStocks: IndianStock[] = [];\n  availableSectors: string[] = [];\n  selectedSector = '';\n  searchTerm = '';\n  sortBy = 'name';\n  loading = false;\n  lastUpdated = new Date();\n  showAllStocks = false;\n  isRealData = false;\n\n  topGainer?: IndianStock;\n  topLoser?: IndianStock;\n\n  constructor(private indianStocksService: IndianStocksService) {}\n\n  async ngOnInit(): Promise<void> {\n    await this.loadStocks();\n  }\n\n  async loadStocks(): Promise<void> {\n    this.loading = true;\n    this.isRealData = false; // Reset flag\n\n    try {\n      if (this.showAllStocks) {\n        this.allStocks = await this.indianStocksService.getAllIndianStocks();\n        this.stocks = this.allStocks;\n      } else {\n        this.stocks = await this.indianStocksService.getIndianStocks();\n      }\n\n      // Check if we got real data by looking for realistic price variations\n      this.isRealData = this.detectRealData(this.stocks);\n\n      this.availableSectors = this.indianStocksService.getAvailableSectors(this.stocks);\n      this.updateMarketSummary();\n      this.filterStocks();\n      this.lastUpdated = new Date();\n\n      if (this.isRealData) {\n        console.log('✅ Successfully loaded real stock data!');\n      } else {\n        console.log('⚠️ Using demo data (real API may be blocked)');\n      }\n    } catch (error) {\n      console.error('Error loading stocks:', error);\n      this.isRealData = false;\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  private detectRealData(stocks: IndianStock[]): boolean {\n    if (stocks.length === 0) return false;\n\n    // Check for realistic price variations and non-round numbers\n    const hasRealisticPrices = stocks.some(stock => {\n      const price = stock.price;\n      // Real stock prices are rarely perfect round numbers\n      return price > 0 && (price % 1 !== 0 || price % 10 !== 0);\n    });\n\n    // Check for realistic volume numbers\n    const hasRealisticVolume = stocks.some(stock => {\n      return stock.volume > 0 && stock.volume !== Math.floor(stock.volume);\n    });\n\n    // Check if we have proper company names (not just symbols)\n    const hasCompanyNames = stocks.some(stock => {\n      return stock.name && stock.name !== stock.symbol && stock.name.includes('Limited');\n    });\n\n    return hasRealisticPrices || hasRealisticVolume || hasCompanyNames;\n  }\n\n  async toggleAllStocks(): Promise<void> {\n    this.showAllStocks = !this.showAllStocks;\n    await this.loadStocks();\n  }\n\n  async refreshData(): Promise<void> {\n    await this.loadStocks();\n  }\n\n  async forceRealDataRefresh(): Promise<void> {\n    console.log('🔄 Force refreshing with real data...');\n    this.loading = true;\n    this.isRealData = false;\n\n    try {\n      // Clear any cached data and force fresh fetch\n      if (this.showAllStocks) {\n        this.allStocks = await this.indianStocksService.getAllIndianStocks();\n        this.stocks = this.allStocks;\n      } else {\n        this.stocks = await this.indianStocksService.getIndianStocks();\n      }\n\n      this.isRealData = this.detectRealData(this.stocks);\n      this.availableSectors = this.indianStocksService.getAvailableSectors(this.stocks);\n      this.updateMarketSummary();\n      this.filterStocks();\n      this.lastUpdated = new Date();\n\n      if (this.isRealData) {\n        console.log('✅ Successfully fetched real data!');\n      } else {\n        console.log('⚠️ Still using demo data - real APIs may be blocked');\n      }\n    } catch (error) {\n      console.error('Error force refreshing data:', error);\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  filterStocks(): void {\n    let filtered = [...this.stocks];\n\n    // Filter by sector\n    if (this.selectedSector) {\n      filtered = this.indianStocksService.getStocksBySector(filtered, this.selectedSector);\n    }\n\n    // Filter by search term\n    if (this.searchTerm) {\n      const term = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(stock => \n        stock.name.toLowerCase().includes(term) || \n        stock.symbol.toLowerCase().includes(term)\n      );\n    }\n\n    this.filteredStocks = filtered;\n    this.sortStocks();\n  }\n\n  sortStocks(): void {\n    this.filteredStocks.sort((a, b) => {\n      const aValue = a[this.sortBy as keyof IndianStock];\n      const bValue = b[this.sortBy as keyof IndianStock];\n\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return aValue.localeCompare(bValue);\n      }\n\n      if (typeof aValue === 'number' && typeof bValue === 'number') {\n        return bValue - aValue; // Descending order for numbers\n      }\n\n      return 0;\n    });\n  }\n\n  updateMarketSummary(): void {\n    const gainers = this.indianStocksService.getTopGainers(this.stocks);\n    const losers = this.indianStocksService.getTopLosers(this.stocks);\n    \n    this.topGainer = gainers[0];\n    this.topLoser = losers[0];\n  }\n\n  getGainersCount(): number {\n    return this.stocks.filter(stock => stock.changePercent > 0).length;\n  }\n\n  getLosersCount(): number {\n    return this.stocks.filter(stock => stock.changePercent < 0).length;\n  }\n\n  formatVolume(volume: number): string {\n    if (volume >= 10000000) {\n      return (volume / 10000000).toFixed(1) + 'Cr';\n    } else if (volume >= 100000) {\n      return (volume / 100000).toFixed(1) + 'L';\n    } else if (volume >= 1000) {\n      return (volume / 1000).toFixed(1) + 'K';\n    }\n    return volume.toString();\n  }\n\n  get popularStocksCount(): number {\n    return 30; // Popular stocks count\n  }\n\n  get allStocksCount(): number {\n    return this.allStocks.length || 150; // Estimated all stocks count\n  }\n}\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;IAqB9BC,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,oBAAAC,MAAA,CAAAC,kBAAA,OACF;;;;;IACEJ,EAAA,CAAAC,MAAA,GACF;;;;IADED,EAAA,CAAAE,kBAAA,uBAAAC,MAAA,CAAAE,cAAA,OACF;;;;;IAOEL,EAAA,CAAAM,SAAA,cAAiG;IACjGN,EAAA,CAAAC,MAAA,8BACF;;;;;IACED,EAAA,CAAAC,MAAA,mCACF;;;;;IAOED,EAAA,CAAAM,SAAA,cAAiG;;;;;IA+BjGN,EADF,CAAAO,cAAA,UAAK,YACoB;IAAAP,EAAA,CAAAC,MAAA,GAAoB;IAAAD,EAAA,CAAAQ,YAAA,EAAI;IAC/CR,EAAA,CAAAO,cAAA,YAAiC;IAAAP,EAAA,CAAAC,MAAA,GAAsB;IAAAD,EAAA,CAAAQ,YAAA,EAAI;IAC3DR,EAAA,CAAAO,cAAA,YAA4C;IAAAP,EAAA,CAAAC,MAAA,GAA0C;IACxFD,EADwF,CAAAQ,YAAA,EAAI,EACtF;;;;IAHmBR,EAAA,CAAAS,SAAA,GAAoB;IAApBT,EAAA,CAAAU,iBAAA,CAAAP,MAAA,CAAAQ,SAAA,CAAAC,IAAA,CAAoB;IACVZ,EAAA,CAAAS,SAAA,GAAsB;IAAtBT,EAAA,CAAAU,iBAAA,CAAAP,MAAA,CAAAQ,SAAA,CAAAE,MAAA,CAAsB;IACXb,EAAA,CAAAS,SAAA,GAA0C;IAA1CT,EAAA,CAAAE,kBAAA,MAAAC,MAAA,CAAAQ,SAAA,CAAAG,aAAA,CAAAC,OAAA,SAA0C;;;;;IAStFf,EADF,CAAAO,cAAA,UAAK,YACoB;IAAAP,EAAA,CAAAC,MAAA,GAAmB;IAAAD,EAAA,CAAAQ,YAAA,EAAI;IAC9CR,EAAA,CAAAO,cAAA,YAAiC;IAAAP,EAAA,CAAAC,MAAA,GAAqB;IAAAD,EAAA,CAAAQ,YAAA,EAAI;IAC1DR,EAAA,CAAAO,cAAA,YAA0C;IAAAP,EAAA,CAAAC,MAAA,GAAwC;IACpFD,EADoF,CAAAQ,YAAA,EAAI,EAClF;;;;IAHmBR,EAAA,CAAAS,SAAA,GAAmB;IAAnBT,EAAA,CAAAU,iBAAA,CAAAP,MAAA,CAAAa,QAAA,CAAAJ,IAAA,CAAmB;IACTZ,EAAA,CAAAS,SAAA,GAAqB;IAArBT,EAAA,CAAAU,iBAAA,CAAAP,MAAA,CAAAa,QAAA,CAAAH,MAAA,CAAqB;IACZb,EAAA,CAAAS,SAAA,GAAwC;IAAxCT,EAAA,CAAAE,kBAAA,KAAAC,MAAA,CAAAa,QAAA,CAAAF,aAAA,CAAAC,OAAA,SAAwC;;;;;IAchFf,EAAA,CAAAO,cAAA,iBAAyB;IAAAP,EAAA,CAAAC,MAAA,GAAY;IAAAD,EAAA,CAAAQ,YAAA,EAAS;;;;IAAtCR,EAAA,CAAAiB,UAAA,UAAAC,SAAA,CAAgB;IAAClB,EAAA,CAAAS,SAAA,EAAY;IAAZT,EAAA,CAAAU,iBAAA,CAAAQ,SAAA,CAAY;;;;;IA+C/BlB,EAHN,CAAAO,cAAA,aAA6B,SACvB,UACG,cACoC;IAAAP,EAAA,CAAAC,MAAA,GAAkB;IAAAD,EAAA,CAAAQ,YAAA,EAAM;IAC/DR,EAAA,CAAAO,cAAA,cAAmC;IAAAP,EAAA,CAAAC,MAAA,GAAgB;IAEvDD,EAFuD,CAAAQ,YAAA,EAAM,EACrD,EACH;IAEHR,EADF,CAAAO,cAAA,SAAI,eAC8G;IAC9GP,EAAA,CAAAC,MAAA,GACF;IACFD,EADE,CAAAQ,YAAA,EAAO,EACJ;IACLR,EAAA,CAAAO,cAAA,cAAwB;IAAAP,EAAA,CAAAC,MAAA,IAA6B;IAAAD,EAAA,CAAAQ,YAAA,EAAK;IAC1DR,EAAA,CAAAO,cAAA,UAAoE;IAClEP,EAAA,CAAAC,MAAA,IACF;IAAAD,EAAA,CAAAQ,YAAA,EAAK;IACLR,EAAA,CAAAO,cAAA,UAA2E;IACzEP,EAAA,CAAAC,MAAA,IACF;IAAAD,EAAA,CAAAQ,YAAA,EAAK;IACLR,EAAA,CAAAO,cAAA,UAAI;IAAAP,EAAA,CAAAC,MAAA,IAAgC;IAAAD,EAAA,CAAAQ,YAAA,EAAK;IACzCR,EAAA,CAAAO,cAAA,UAAI;IAAAP,EAAA,CAAAC,MAAA,IAAwC;IAAAD,EAAA,CAAAQ,YAAA,EAAK;IAE/CR,EADF,CAAAO,cAAA,UAAI,kBACsC;IAAAP,EAAA,CAAAC,MAAA,aAAK;IAEjDD,EAFiD,CAAAQ,YAAA,EAAS,EACnD,EACF;;;;;IArBwCR,EAAA,CAAAS,SAAA,GAAkB;IAAlBT,EAAA,CAAAU,iBAAA,CAAAS,QAAA,CAAAN,MAAA,CAAkB;IACtBb,EAAA,CAAAS,SAAA,GAAgB;IAAhBT,EAAA,CAAAU,iBAAA,CAAAS,QAAA,CAAAP,IAAA,CAAgB;IAKnDZ,EAAA,CAAAS,SAAA,GACF;IADET,EAAA,CAAAE,kBAAA,MAAAiB,QAAA,CAAAC,MAAA,MACF;IAEsBpB,EAAA,CAAAS,SAAA,GAA6B;IAA7BT,EAAA,CAAAE,kBAAA,WAAAiB,QAAA,CAAAE,KAAA,CAAAN,OAAA,QAA6B;IACjDf,EAAA,CAAAS,SAAA,EAA+D;IAA/DT,EAAA,CAAAsB,UAAA,CAAAH,QAAA,CAAAI,MAAA,0CAA+D;IACjEvB,EAAA,CAAAS,SAAA,EACF;IADET,EAAA,CAAAwB,kBAAA,MAAAL,QAAA,CAAAI,MAAA,4BAAAJ,QAAA,CAAAI,MAAA,CAAAR,OAAA,SACF;IACIf,EAAA,CAAAS,SAAA,EAAsE;IAAtET,EAAA,CAAAsB,UAAA,CAAAH,QAAA,CAAAL,aAAA,0CAAsE;IACxEd,EAAA,CAAAS,SAAA,EACF;IADET,EAAA,CAAAwB,kBAAA,MAAAL,QAAA,CAAAL,aAAA,sBAAAK,QAAA,CAAAL,aAAA,CAAAC,OAAA,UACF;IACIf,EAAA,CAAAS,SAAA,GAAgC;IAAhCT,EAAA,CAAAU,iBAAA,CAAAP,MAAA,CAAAsB,YAAA,CAAAN,QAAA,CAAAO,MAAA,EAAgC;IAChC1B,EAAA,CAAAS,SAAA,GAAwC;IAAxCT,EAAA,CAAAE,kBAAA,WAAAiB,QAAA,CAAAQ,SAAA,kBAAAR,QAAA,CAAAQ,SAAA,CAAAC,cAAA,OAAwC;;;;;IAO5C5B,EADF,CAAAO,cAAA,SAAI,aACqD;IACrDP,EAAA,CAAAC,MAAA,+CACF;IACFD,EADE,CAAAQ,YAAA,EAAK,EACF;;;;;IAcPR,EAAA,CAAAM,SAAA,eAA4D;IAC5DN,EAAA,CAAAC,MAAA,sBACF;;;;;IACED,EAAA,CAAAM,SAAA,eAA6D;IAC7DN,EAAA,CAAAC,MAAA,kBACF;;;;;IAGAD,EAAA,CAAAO,cAAA,eAAoC;IAClCP,EAAA,CAAAC,MAAA,qDACF;IAAAD,EAAA,CAAAQ,YAAA,EAAO;;;AAOnB,OAAM,MAAOqB,qBAAqB;EAgBhCC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IAfvC,KAAAC,MAAM,GAAkB,EAAE;IAC1B,KAAAC,SAAS,GAAkB,EAAE;IAC7B,KAAAC,cAAc,GAAkB,EAAE;IAClC,KAAAC,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,MAAM,GAAG,MAAM;IACf,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE;IACxB,KAAAC,aAAa,GAAG,KAAK;IACrB,KAAAC,UAAU,GAAG,KAAK;EAK6C;EAEzDC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,MAAMD,KAAI,CAACE,UAAU,EAAE;IAAC;EAC1B;EAEMA,UAAUA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAF,iBAAA;MACdE,MAAI,CAACT,OAAO,GAAG,IAAI;MACnBS,MAAI,CAACL,UAAU,GAAG,KAAK,CAAC,CAAC;MAEzB,IAAI;QACF,IAAIK,MAAI,CAACN,aAAa,EAAE;UACtBM,MAAI,CAACf,SAAS,SAASe,MAAI,CAACjB,mBAAmB,CAACkB,kBAAkB,EAAE;UACpED,MAAI,CAAChB,MAAM,GAAGgB,MAAI,CAACf,SAAS;QAC9B,CAAC,MAAM;UACLe,MAAI,CAAChB,MAAM,SAASgB,MAAI,CAACjB,mBAAmB,CAACmB,eAAe,EAAE;QAChE;QAEA;QACAF,MAAI,CAACL,UAAU,GAAGK,MAAI,CAACG,cAAc,CAACH,MAAI,CAAChB,MAAM,CAAC;QAElDgB,MAAI,CAACb,gBAAgB,GAAGa,MAAI,CAACjB,mBAAmB,CAACqB,mBAAmB,CAACJ,MAAI,CAAChB,MAAM,CAAC;QACjFgB,MAAI,CAACK,mBAAmB,EAAE;QAC1BL,MAAI,CAACM,YAAY,EAAE;QACnBN,MAAI,CAACR,WAAW,GAAG,IAAIC,IAAI,EAAE;QAE7B,IAAIO,MAAI,CAACL,UAAU,EAAE;UACnBY,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACvD,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC7D;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CT,MAAI,CAACL,UAAU,GAAG,KAAK;MACzB,CAAC,SAAS;QACRK,MAAI,CAACT,OAAO,GAAG,KAAK;MACtB;IAAC;EACH;EAEQY,cAAcA,CAACnB,MAAqB;IAC1C,IAAIA,MAAM,CAAC0B,MAAM,KAAK,CAAC,EAAE,OAAO,KAAK;IAErC;IACA,MAAMC,kBAAkB,GAAG3B,MAAM,CAAC4B,IAAI,CAACC,KAAK,IAAG;MAC7C,MAAMxC,KAAK,GAAGwC,KAAK,CAACxC,KAAK;MACzB;MACA,OAAOA,KAAK,GAAG,CAAC,KAAKA,KAAK,GAAG,CAAC,KAAK,CAAC,IAAIA,KAAK,GAAG,EAAE,KAAK,CAAC,CAAC;IAC3D,CAAC,CAAC;IAEF;IACA,MAAMyC,kBAAkB,GAAG9B,MAAM,CAAC4B,IAAI,CAACC,KAAK,IAAG;MAC7C,OAAOA,KAAK,CAACnC,MAAM,GAAG,CAAC,IAAImC,KAAK,CAACnC,MAAM,KAAKqC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACnC,MAAM,CAAC;IACtE,CAAC,CAAC;IAEF;IACA,MAAMuC,eAAe,GAAGjC,MAAM,CAAC4B,IAAI,CAACC,KAAK,IAAG;MAC1C,OAAOA,KAAK,CAACjD,IAAI,IAAIiD,KAAK,CAACjD,IAAI,KAAKiD,KAAK,CAAChD,MAAM,IAAIgD,KAAK,CAACjD,IAAI,CAACsD,QAAQ,CAAC,SAAS,CAAC;IACpF,CAAC,CAAC;IAEF,OAAOP,kBAAkB,IAAIG,kBAAkB,IAAIG,eAAe;EACpE;EAEME,eAAeA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAtB,iBAAA;MACnBsB,MAAI,CAAC1B,aAAa,GAAG,CAAC0B,MAAI,CAAC1B,aAAa;MACxC,MAAM0B,MAAI,CAACrB,UAAU,EAAE;IAAC;EAC1B;EAEMsB,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAxB,iBAAA;MACf,MAAMwB,MAAI,CAACvB,UAAU,EAAE;IAAC;EAC1B;EAEMwB,oBAAoBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA1B,iBAAA;MACxBS,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpDgB,MAAI,CAACjC,OAAO,GAAG,IAAI;MACnBiC,MAAI,CAAC7B,UAAU,GAAG,KAAK;MAEvB,IAAI;QACF;QACA,IAAI6B,MAAI,CAAC9B,aAAa,EAAE;UACtB8B,MAAI,CAACvC,SAAS,SAASuC,MAAI,CAACzC,mBAAmB,CAACkB,kBAAkB,EAAE;UACpEuB,MAAI,CAACxC,MAAM,GAAGwC,MAAI,CAACvC,SAAS;QAC9B,CAAC,MAAM;UACLuC,MAAI,CAACxC,MAAM,SAASwC,MAAI,CAACzC,mBAAmB,CAACmB,eAAe,EAAE;QAChE;QAEAsB,MAAI,CAAC7B,UAAU,GAAG6B,MAAI,CAACrB,cAAc,CAACqB,MAAI,CAACxC,MAAM,CAAC;QAClDwC,MAAI,CAACrC,gBAAgB,GAAGqC,MAAI,CAACzC,mBAAmB,CAACqB,mBAAmB,CAACoB,MAAI,CAACxC,MAAM,CAAC;QACjFwC,MAAI,CAACnB,mBAAmB,EAAE;QAC1BmB,MAAI,CAAClB,YAAY,EAAE;QACnBkB,MAAI,CAAChC,WAAW,GAAG,IAAIC,IAAI,EAAE;QAE7B,IAAI+B,MAAI,CAAC7B,UAAU,EAAE;UACnBY,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAClD,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;QACpE;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD,CAAC,SAAS;QACRe,MAAI,CAACjC,OAAO,GAAG,KAAK;MACtB;IAAC;EACH;EAEAe,YAAYA,CAAA;IACV,IAAImB,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACzC,MAAM,CAAC;IAE/B;IACA,IAAI,IAAI,CAACI,cAAc,EAAE;MACvBqC,QAAQ,GAAG,IAAI,CAAC1C,mBAAmB,CAAC2C,iBAAiB,CAACD,QAAQ,EAAE,IAAI,CAACrC,cAAc,CAAC;IACtF;IAEA;IACA,IAAI,IAAI,CAACC,UAAU,EAAE;MACnB,MAAMsC,IAAI,GAAG,IAAI,CAACtC,UAAU,CAACuC,WAAW,EAAE;MAC1CH,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAAChB,KAAK,IAC9BA,KAAK,CAACjD,IAAI,CAACgE,WAAW,EAAE,CAACV,QAAQ,CAACS,IAAI,CAAC,IACvCd,KAAK,CAAChD,MAAM,CAAC+D,WAAW,EAAE,CAACV,QAAQ,CAACS,IAAI,CAAC,CAC1C;IACH;IAEA,IAAI,CAACzC,cAAc,GAAGuC,QAAQ;IAC9B,IAAI,CAACK,UAAU,EAAE;EACnB;EAEAA,UAAUA,CAAA;IACR,IAAI,CAAC5C,cAAc,CAAC6C,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAChC,MAAMC,MAAM,GAAGF,CAAC,CAAC,IAAI,CAAC1C,MAA2B,CAAC;MAClD,MAAM6C,MAAM,GAAGF,CAAC,CAAC,IAAI,CAAC3C,MAA2B,CAAC;MAElD,IAAI,OAAO4C,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC5D,OAAOD,MAAM,CAACE,aAAa,CAACD,MAAM,CAAC;MACrC;MAEA,IAAI,OAAOD,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC5D,OAAOA,MAAM,GAAGD,MAAM,CAAC,CAAC;MAC1B;MAEA,OAAO,CAAC;IACV,CAAC,CAAC;EACJ;EAEA7B,mBAAmBA,CAAA;IACjB,MAAMgC,OAAO,GAAG,IAAI,CAACtD,mBAAmB,CAACuD,aAAa,CAAC,IAAI,CAACtD,MAAM,CAAC;IACnE,MAAMuD,MAAM,GAAG,IAAI,CAACxD,mBAAmB,CAACyD,YAAY,CAAC,IAAI,CAACxD,MAAM,CAAC;IAEjE,IAAI,CAACrB,SAAS,GAAG0E,OAAO,CAAC,CAAC,CAAC;IAC3B,IAAI,CAACrE,QAAQ,GAAGuE,MAAM,CAAC,CAAC,CAAC;EAC3B;EAEAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACzD,MAAM,CAAC6C,MAAM,CAAChB,KAAK,IAAIA,KAAK,CAAC/C,aAAa,GAAG,CAAC,CAAC,CAAC4C,MAAM;EACpE;EAEAgC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC1D,MAAM,CAAC6C,MAAM,CAAChB,KAAK,IAAIA,KAAK,CAAC/C,aAAa,GAAG,CAAC,CAAC,CAAC4C,MAAM;EACpE;EAEAjC,YAAYA,CAACC,MAAc;IACzB,IAAIA,MAAM,IAAI,QAAQ,EAAE;MACtB,OAAO,CAACA,MAAM,GAAG,QAAQ,EAAEX,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;IAC9C,CAAC,MAAM,IAAIW,MAAM,IAAI,MAAM,EAAE;MAC3B,OAAO,CAACA,MAAM,GAAG,MAAM,EAAEX,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC3C,CAAC,MAAM,IAAIW,MAAM,IAAI,IAAI,EAAE;MACzB,OAAO,CAACA,MAAM,GAAG,IAAI,EAAEX,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACzC;IACA,OAAOW,MAAM,CAACiE,QAAQ,EAAE;EAC1B;EAEA,IAAIvF,kBAAkBA,CAAA;IACpB,OAAO,EAAE,CAAC,CAAC;EACb;EAEA,IAAIC,cAAcA,CAAA;IAChB,OAAO,IAAI,CAAC4B,SAAS,CAACyB,MAAM,IAAI,GAAG,CAAC,CAAC;EACvC;;;uCA9LW7B,qBAAqB,EAAA7B,EAAA,CAAA4F,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAArBjE,qBAAqB;MAAAkE,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAjG,EAAA,CAAAkG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjMxBxG,EAJN,CAAAO,cAAA,aAAuB,aAE0B,UACxC,YAC0C;UAAAP,EAAA,CAAAC,MAAA,oBAAa;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UAC/DR,EAAA,CAAAO,cAAA,WAA8B;UAAAP,EAAA,CAAAC,MAAA,qDAA8C;UAC9ED,EAD8E,CAAAQ,YAAA,EAAI,EAC5E;UAEJR,EADF,CAAAO,cAAA,aAAwB,gBAII;UAFxBP,EAAA,CAAA0G,UAAA,mBAAAC,uDAAA;YAAA,OAASF,GAAA,CAAAtC,eAAA,EAAiB;UAAA,EAAC;UAKzBnE,EAFF,CAAA4G,UAAA,IAAAC,4CAAA,OAAqB,KAAAC,6CAAA,OAEZ;UAGX9G,EAAA,CAAAQ,YAAA,EAAS;UACTR,EAAA,CAAAO,cAAA,iBAG0B;UAFxBP,EAAA,CAAA0G,UAAA,mBAAAK,wDAAA;YAAA,OAASN,GAAA,CAAAlC,oBAAA,EAAsB;UAAA,EAAC;UAM9BvE,EAHF,CAAA4G,UAAA,KAAAI,6CAAA,OAAe,KAAAC,6CAAA,OAGN;UAGXjH,EAAA,CAAAQ,YAAA,EAAS;UACTR,EAAA,CAAAO,cAAA,iBAG0B;UAFxBP,EAAA,CAAA0G,UAAA,mBAAAQ,wDAAA;YAAA,OAAST,GAAA,CAAApC,WAAA,EAAa;UAAA,EAAC;UAGvBrE,EAAA,CAAA4G,UAAA,KAAAO,6CAAA,kBAAe;UAGfnH,EAAA,CAAAC,MAAA,iBACF;UAEJD,EAFI,CAAAQ,YAAA,EAAS,EACL,EACF;UAKFR,EAFJ,CAAAO,cAAA,cAAmD,eAC/B,cACmC;UAAAP,EAAA,CAAAC,MAAA,uBAAe;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UAGnER,EAFJ,CAAAO,cAAA,eAAuB,eACa,gBACJ;UAAAP,EAAA,CAAAC,MAAA,qBAAa;UAAAD,EAAA,CAAAQ,YAAA,EAAO;UAChDR,EAAA,CAAAO,cAAA,gBAA0B;UAAAP,EAAA,CAAAC,MAAA,IAAmB;UAC/CD,EAD+C,CAAAQ,YAAA,EAAO,EAChD;UAEJR,EADF,CAAAO,cAAA,eAAkC,gBACJ;UAAAP,EAAA,CAAAC,MAAA,gBAAQ;UAAAD,EAAA,CAAAQ,YAAA,EAAO;UAC3CR,EAAA,CAAAO,cAAA,gBAAyC;UAAAP,EAAA,CAAAC,MAAA,IAAuB;UAClED,EADkE,CAAAQ,YAAA,EAAO,EACnE;UAEJR,EADF,CAAAO,cAAA,eAAkC,gBACJ;UAAAP,EAAA,CAAAC,MAAA,eAAO;UAAAD,EAAA,CAAAQ,YAAA,EAAO;UAC1CR,EAAA,CAAAO,cAAA,gBAAuC;UAAAP,EAAA,CAAAC,MAAA,IAAsB;UAGnED,EAHmE,CAAAQ,YAAA,EAAO,EAChE,EACF,EACF;UAGJR,EADF,CAAAO,cAAA,eAAkB,cACmC;UAAAP,EAAA,CAAAC,MAAA,kBAAU;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UAClER,EAAA,CAAA4G,UAAA,KAAAQ,6CAAA,cAAiB;UAOnBpH,EAAA,CAAAQ,YAAA,EAAM;UAGJR,EADF,CAAAO,cAAA,eAAkB,cACmC;UAAAP,EAAA,CAAAC,MAAA,iBAAS;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UACjER,EAAA,CAAA4G,UAAA,KAAAS,6CAAA,cAAgB;UAQpBrH,EADE,CAAAQ,YAAA,EAAM,EACF;UAMAR,EAHN,CAAAO,cAAA,eAAkB,eAC+B,WACxC,iBACyD;UAAAP,EAAA,CAAAC,MAAA,wBAAgB;UAAAD,EAAA,CAAAQ,YAAA,EAAQ;UACpFR,EAAA,CAAAO,cAAA,kBAAoF;UAA5EP,EAAA,CAAAsH,gBAAA,2BAAAC,gEAAAC,MAAA;YAAAxH,EAAA,CAAAyH,kBAAA,CAAAhB,GAAA,CAAArE,cAAA,EAAAoF,MAAA,MAAAf,GAAA,CAAArE,cAAA,GAAAoF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAACxH,EAAA,CAAA0G,UAAA,2BAAAa,gEAAA;YAAA,OAAiBd,GAAA,CAAAnD,YAAA,EAAc;UAAA,EAAC;UACnEtD,EAAA,CAAAO,cAAA,kBAAiB;UAAAP,EAAA,CAAAC,MAAA,mBAAW;UAAAD,EAAA,CAAAQ,YAAA,EAAS;UACrCR,EAAA,CAAA0H,gBAAA,KAAAC,qCAAA,sBAAA3H,EAAA,CAAA4H,yBAAA,CAEC;UAEL5H,EADE,CAAAQ,YAAA,EAAS,EACL;UAEJR,EADF,CAAAO,cAAA,WAAK,iBACyD;UAAAP,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAQ,YAAA,EAAQ;UAC1ER,EAAA,CAAAO,cAAA,iBAKgB;UAHdP,EAAA,CAAAsH,gBAAA,2BAAAO,+DAAAL,MAAA;YAAAxH,EAAA,CAAAyH,kBAAA,CAAAhB,GAAA,CAAApE,UAAA,EAAAmF,MAAA,MAAAf,GAAA,CAAApE,UAAA,GAAAmF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UACxBxH,EAAA,CAAA0G,UAAA,2BAAAmB,+DAAA;YAAA,OAAiBpB,GAAA,CAAAnD,YAAA,EAAc;UAAA,EAAC;UAGpCtD,EANE,CAAAQ,YAAA,EAKgB,EACZ;UAEJR,EADF,CAAAO,cAAA,WAAK,iBACyD;UAAAP,EAAA,CAAAC,MAAA,eAAO;UAAAD,EAAA,CAAAQ,YAAA,EAAQ;UAC3ER,EAAA,CAAAO,cAAA,kBAA0E;UAAlEP,EAAA,CAAAsH,gBAAA,2BAAAQ,gEAAAN,MAAA;YAAAxH,EAAA,CAAAyH,kBAAA,CAAAhB,GAAA,CAAAnE,MAAA,EAAAkF,MAAA,MAAAf,GAAA,CAAAnE,MAAA,GAAAkF,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoB;UAACxH,EAAA,CAAA0G,UAAA,2BAAAoB,gEAAA;YAAA,OAAiBrB,GAAA,CAAA3B,UAAA,EAAY;UAAA,EAAC;UACzD9E,EAAA,CAAAO,cAAA,kBAAqB;UAAAP,EAAA,CAAAC,MAAA,YAAI;UAAAD,EAAA,CAAAQ,YAAA,EAAS;UAClCR,EAAA,CAAAO,cAAA,kBAAsB;UAAAP,EAAA,CAAAC,MAAA,aAAK;UAAAD,EAAA,CAAAQ,YAAA,EAAS;UACpCR,EAAA,CAAAO,cAAA,kBAAuB;UAAAP,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAQ,YAAA,EAAS;UACtCR,EAAA,CAAAO,cAAA,kBAA8B;UAAAP,EAAA,CAAAC,MAAA,gBAAQ;UAAAD,EAAA,CAAAQ,YAAA,EAAS;UAC/CR,EAAA,CAAAO,cAAA,kBAAuB;UAAAP,EAAA,CAAAC,MAAA,cAAM;UAIrCD,EAJqC,CAAAQ,YAAA,EAAS,EAC/B,EACL,EACF,EACF;UAQIR,EALV,CAAAO,cAAA,eAAkB,eACa,iBACN,iBACO,UACpB,UACE;UAAAP,EAAA,CAAAC,MAAA,aAAK;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UACdR,EAAA,CAAAO,cAAA,UAAI;UAAAP,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UACfR,EAAA,CAAAO,cAAA,UAAI;UAAAP,EAAA,CAAAC,MAAA,sBAAS;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UAClBR,EAAA,CAAAO,cAAA,UAAI;UAAAP,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UACfR,EAAA,CAAAO,cAAA,UAAI;UAAAP,EAAA,CAAAC,MAAA,gBAAQ;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UACjBR,EAAA,CAAAO,cAAA,UAAI;UAAAP,EAAA,CAAAC,MAAA,cAAM;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UACfR,EAAA,CAAAO,cAAA,UAAI;UAAAP,EAAA,CAAAC,MAAA,uBAAe;UAAAD,EAAA,CAAAQ,YAAA,EAAK;UACxBR,EAAA,CAAAO,cAAA,UAAI;UAAAP,EAAA,CAAAC,MAAA,cAAM;UAEdD,EAFc,CAAAQ,YAAA,EAAK,EACZ,EACC;UACRR,EAAA,CAAAO,cAAA,iBAAwC;UACtCP,EAAA,CAAA0H,gBAAA,KAAAK,qCAAA,oBAAAC,UAAA,SAAAC,0CAAA,aAgCC;UAITjI,EAHM,CAAAQ,YAAA,EAAQ,EACF,EACJ,EACF;UAIJR,EADF,CAAAO,cAAA,eAAyD,WAClD;UAAAP,EAAA,CAAAC,MAAA,KAA+C;;UAAAD,EAAA,CAAAQ,YAAA,EAAM;UAExDR,EADF,CAAAO,cAAA,gBAAoD,iBAE2C;UAIzFP,EAHF,CAAA4G,UAAA,MAAAsB,8CAAA,OAAkB,MAAAC,8CAAA,OAGT;UAIXnI,EAAA,CAAAQ,YAAA,EAAO;UACPR,EAAA,CAAA4G,UAAA,MAAAwB,8CAAA,mBAAmB;UAOzBpI,EAFI,CAAAQ,YAAA,EAAM,EACF,EACF;;;UAxLER,EAAA,CAAAS,SAAA,GAAoB;UAApBT,EAAA,CAAAiB,UAAA,aAAAwF,GAAA,CAAAlE,OAAA,CAAoB;UAEpBvC,EAAA,CAAAS,SAAA,EAIC;UAJDT,EAAA,CAAAqI,aAAA,CAAA5B,GAAA,CAAA/D,aAAA,UAIC;UAID1C,EAAA,CAAAS,SAAA,GAAoB;UAApBT,EAAA,CAAAiB,UAAA,aAAAwF,GAAA,CAAAlE,OAAA,CAAoB;UAEpBvC,EAAA,CAAAS,SAAA,EAKC;UALDT,EAAA,CAAAqI,aAAA,CAAA5B,GAAA,CAAAlE,OAAA,WAKC;UAIDvC,EAAA,CAAAS,SAAA,GAAoB;UAApBT,EAAA,CAAAiB,UAAA,aAAAwF,GAAA,CAAAlE,OAAA,CAAoB;UAEpBvC,EAAA,CAAAS,SAAA,EAEC;UAFDT,EAAA,CAAAqI,aAAA,CAAA5B,GAAA,CAAAlE,OAAA,WAEC;UAa2BvC,EAAA,CAAAS,SAAA,IAAmB;UAAnBT,EAAA,CAAAU,iBAAA,CAAA+F,GAAA,CAAAzE,MAAA,CAAA0B,MAAA,CAAmB;UAIJ1D,EAAA,CAAAS,SAAA,GAAuB;UAAvBT,EAAA,CAAAU,iBAAA,CAAA+F,GAAA,CAAAhB,eAAA,GAAuB;UAIzBzF,EAAA,CAAAS,SAAA,GAAsB;UAAtBT,EAAA,CAAAU,iBAAA,CAAA+F,GAAA,CAAAf,cAAA,GAAsB;UAOjE1F,EAAA,CAAAS,SAAA,GAMC;UANDT,EAAA,CAAAqI,aAAA,CAAA5B,GAAA,CAAA9F,SAAA,WAMC;UAKDX,EAAA,CAAAS,SAAA,GAMC;UANDT,EAAA,CAAAqI,aAAA,CAAA5B,GAAA,CAAAzF,QAAA,WAMC;UASShB,EAAA,CAAAS,SAAA,GAA4B;UAA5BT,EAAA,CAAAsI,gBAAA,YAAA7B,GAAA,CAAArE,cAAA,CAA4B;UAElCpC,EAAA,CAAAS,SAAA,GAEC;UAFDT,EAAA,CAAAuI,UAAA,CAAA9B,GAAA,CAAAtE,gBAAA,CAEC;UAODnC,EAAA,CAAAS,SAAA,GAAwB;UAAxBT,EAAA,CAAAsI,gBAAA,YAAA7B,GAAA,CAAApE,UAAA,CAAwB;UAOlBrC,EAAA,CAAAS,SAAA,GAAoB;UAApBT,EAAA,CAAAsI,gBAAA,YAAA7B,GAAA,CAAAnE,MAAA,CAAoB;UA4B1BtC,EAAA,CAAAS,SAAA,IAgCC;UAhCDT,EAAA,CAAAuI,UAAA,CAAA9B,GAAA,CAAAvE,cAAA,CAgCC;UAQFlC,EAAA,CAAAS,SAAA,GAA+C;UAA/CT,EAAA,CAAAE,kBAAA,mBAAAF,EAAA,CAAAwI,WAAA,UAAA/B,GAAA,CAAAjE,WAAA,gBAA+C;UAG5CxC,EAAA,CAAAS,SAAA,GAAsF;UAAtFT,EAAA,CAAAsB,UAAA,CAAAmF,GAAA,CAAA9D,UAAA,mEAAsF;UAC1F3C,EAAA,CAAAS,SAAA,EAMC;UANDT,EAAA,CAAAqI,aAAA,CAAA5B,GAAA,CAAA9D,UAAA,aAMC;UAEH3C,EAAA,CAAAS,SAAA,GAIC;UAJDT,EAAA,CAAAqI,aAAA,EAAA5B,GAAA,CAAA9D,UAAA,YAIC;;;qBAjMC7C,YAAY,EAAA2I,EAAA,CAAAC,QAAA,EAAE3I,WAAW,EAAA4I,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,0BAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,OAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}