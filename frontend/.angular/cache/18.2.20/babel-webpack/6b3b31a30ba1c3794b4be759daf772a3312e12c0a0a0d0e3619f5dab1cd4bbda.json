{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nimport { log } from \"../utils/log.js\";\nvar WebSocketClient = /*#__PURE__*/function () {\n  /**\n   * @param {string} url\n   */\n  function WebSocketClient(url) {\n    _classCallCheck(this, WebSocketClient);\n    this.client = new WebSocket(url);\n    this.client.onerror = function (error) {\n      log.error(error);\n    };\n  }\n\n  /**\n   * @param {(...args: any[]) => void} f\n   */\n  return _createClass(WebSocketClient, [{\n    key: \"onOpen\",\n    value: function onOpen(f) {\n      this.client.onopen = f;\n    }\n\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onClose\",\n    value: function onClose(f) {\n      this.client.onclose = f;\n    }\n\n    // call f with the message string as the first argument\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onMessage\",\n    value: function onMessage(f) {\n      this.client.onmessage = function (e) {\n        f(e.data);\n      };\n    }\n  }]);\n}();\nexport { WebSocketClient as default };", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "a", "n", "TypeError", "_defineProperties", "e", "r", "t", "length", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "i", "_toPrimitive", "toPrimitive", "call", "String", "Number", "log", "WebSocketClient", "url", "client", "WebSocket", "onerror", "error", "value", "onOpen", "f", "onopen", "onClose", "onclose", "onMessage", "onmessage", "data", "default"], "sources": ["/var/www/html/trading-app/node_modules/webpack-dev-server/client/clients/WebSocketClient.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { log } from \"../utils/log.js\";\nvar WebSocketClient = /*#__PURE__*/function () {\n  /**\n   * @param {string} url\n   */\n  function WebSocketClient(url) {\n    _classCallCheck(this, WebSocketClient);\n    this.client = new WebSocket(url);\n    this.client.onerror = function (error) {\n      log.error(error);\n    };\n  }\n\n  /**\n   * @param {(...args: any[]) => void} f\n   */\n  return _createClass(WebSocketClient, [{\n    key: \"onOpen\",\n    value: function onOpen(f) {\n      this.client.onopen = f;\n    }\n\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onClose\",\n    value: function onClose(f) {\n      this.client.onclose = f;\n    }\n\n    // call f with the message string as the first argument\n    /**\n     * @param {(...args: any[]) => void} f\n     */\n  }, {\n    key: \"onMessage\",\n    value: function onMessage(f) {\n      this.client.onmessage = function (e) {\n        f(e.data);\n      };\n    }\n  }]);\n}();\nexport { WebSocketClient as default };"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,EAAED,CAAC,YAAYC,CAAC,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;AAAE;AAClH,SAASC,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIZ,CAAC,GAAGW,CAAC,CAACC,CAAC,CAAC;IAAEZ,CAAC,CAACc,UAAU,GAAGd,CAAC,CAACc,UAAU,IAAI,CAAC,CAAC,EAAEd,CAAC,CAACe,YAAY,GAAG,CAAC,CAAC,EAAE,OAAO,IAAIf,CAAC,KAAKA,CAAC,CAACgB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACC,cAAc,CAACR,CAAC,EAAES,cAAc,CAACnB,CAAC,CAACoB,GAAG,CAAC,EAAEpB,CAAC,CAAC;EAAE;AAAE;AACvO,SAASqB,YAAYA,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,IAAIF,iBAAiB,CAACC,CAAC,CAACN,SAAS,EAAEO,CAAC,CAAC,EAAEC,CAAC,IAAIH,iBAAiB,CAACC,CAAC,EAAEE,CAAC,CAAC,EAAEK,MAAM,CAACC,cAAc,CAACR,CAAC,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,EAAEN,CAAC;AAAE;AAC1K,SAASS,cAAcA,CAACP,CAAC,EAAE;EAAE,IAAIU,CAAC,GAAGC,YAAY,CAACX,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIb,OAAO,CAACuB,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACX,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIZ,OAAO,CAACa,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACX,MAAM,CAACuB,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKd,CAAC,EAAE;IAAE,IAAIY,CAAC,GAAGZ,CAAC,CAACe,IAAI,CAACb,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIZ,OAAO,CAACuB,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAId,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKG,CAAC,GAAGe,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAAE;AAC3T,SAASgB,GAAG,QAAQ,iBAAiB;AACrC,IAAIC,eAAe,GAAG,aAAa,YAAY;EAC7C;AACF;AACA;EACE,SAASA,eAAeA,CAACC,GAAG,EAAE;IAC5BzB,eAAe,CAAC,IAAI,EAAEwB,eAAe,CAAC;IACtC,IAAI,CAACE,MAAM,GAAG,IAAIC,SAAS,CAACF,GAAG,CAAC;IAChC,IAAI,CAACC,MAAM,CAACE,OAAO,GAAG,UAAUC,KAAK,EAAE;MACrCN,GAAG,CAACM,KAAK,CAACA,KAAK,CAAC;IAClB,CAAC;EACH;;EAEA;AACF;AACA;EACE,OAAOb,YAAY,CAACQ,eAAe,EAAE,CAAC;IACpCT,GAAG,EAAE,QAAQ;IACbe,KAAK,EAAE,SAASC,MAAMA,CAACC,CAAC,EAAE;MACxB,IAAI,CAACN,MAAM,CAACO,MAAM,GAAGD,CAAC;IACxB;;IAEA;AACJ;AACA;EACE,CAAC,EAAE;IACDjB,GAAG,EAAE,SAAS;IACde,KAAK,EAAE,SAASI,OAAOA,CAACF,CAAC,EAAE;MACzB,IAAI,CAACN,MAAM,CAACS,OAAO,GAAGH,CAAC;IACzB;;IAEA;IACA;AACJ;AACA;EACE,CAAC,EAAE;IACDjB,GAAG,EAAE,WAAW;IAChBe,KAAK,EAAE,SAASM,SAASA,CAACJ,CAAC,EAAE;MAC3B,IAAI,CAACN,MAAM,CAACW,SAAS,GAAG,UAAUhC,CAAC,EAAE;QACnC2B,CAAC,CAAC3B,CAAC,CAACiC,IAAI,CAAC;MACX,CAAC;IACH;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH,SAASd,eAAe,IAAIe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}