{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../services/auth.service\";\nimport * as i3 from \"@angular/router\";\nfunction LoginComponent_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.error, \" \");\n  }\n}\nfunction LoginComponent_Conditional_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 10);\n    i0.ɵɵtext(1, \"Please enter a valid email address\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Conditional_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 10);\n    i0.ɵɵtext(1, \"Password is required\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction LoginComponent_Conditional_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 14);\n  }\n}\nexport class LoginComponent {\n  constructor(fb, authService) {\n    this.fb = fb;\n    this.authService = authService;\n    this.loading = false;\n    this.error = '';\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', Validators.required]\n    });\n  }\n  onSubmit() {\n    if (this.loginForm.valid) {\n      this.loading = true;\n      this.error = '';\n      this.authService.login(this.loginForm.value).subscribe({\n        next: response => {\n          this.loading = false;\n          this.authService.handleAuthSuccess(response);\n        },\n        error: error => {\n          this.loading = false;\n          this.error = error.error?.message || 'Login failed. Please try again.';\n        }\n      });\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 26,\n      vars: 6,\n      consts: [[1, \"min-h-screen\", \"flex\", \"items-center\", \"justify-center\", \"bg-gray-50\", \"py-12\", \"px-4\", \"sm:px-6\", \"lg:px-8\"], [1, \"max-w-md\", \"w-full\", \"space-y-8\"], [1, \"mt-6\", \"text-center\", \"text-3xl\", \"font-extrabold\", \"text-gray-900\"], [1, \"mt-2\", \"text-center\", \"text-sm\", \"text-gray-600\"], [\"routerLink\", \"/register\", 1, \"font-medium\", \"text-primary-600\", \"hover:text-primary-500\"], [1, \"mt-8\", \"space-y-6\", 3, \"ngSubmit\", \"formGroup\"], [1, \"bg-red-50\", \"border\", \"border-red-200\", \"text-red-700\", \"px-4\", \"py-3\", \"rounded\"], [1, \"space-y-4\"], [\"for\", \"email\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"email\", \"name\", \"email\", \"type\", \"email\", \"formControlName\", \"email\", \"required\", \"\", \"placeholder\", \"Enter your email\", 1, \"input\", \"mt-1\"], [1, \"mt-1\", \"text-sm\", \"text-red-600\"], [\"for\", \"password\", 1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\"], [\"id\", \"password\", \"name\", \"password\", \"type\", \"password\", \"formControlName\", \"password\", \"required\", \"\", \"placeholder\", \"Enter your password\", 1, \"input\", \"mt-1\"], [\"type\", \"submit\", 1, \"w-full\", \"btn\", \"btn-primary\", \"disabled:opacity-50\", \"disabled:cursor-not-allowed\", 3, \"disabled\"], [1, \"inline-block\", \"animate-spin\", \"rounded-full\", \"h-4\", \"w-4\", \"border-b-2\", \"border-white\", \"mr-2\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\")(3, \"h2\", 2);\n          i0.ɵɵtext(4, \" Sign in to your account \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 3);\n          i0.ɵɵtext(6, \" Or \");\n          i0.ɵɵelementStart(7, \"a\", 4);\n          i0.ɵɵtext(8, \" create a new account \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"form\", 5);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_9_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(10, LoginComponent_Conditional_10_Template, 2, 1, \"div\", 6);\n          i0.ɵɵelementStart(11, \"div\", 7)(12, \"div\")(13, \"label\", 8);\n          i0.ɵɵtext(14, \"Email address\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 9);\n          i0.ɵɵtemplate(16, LoginComponent_Conditional_16_Template, 2, 0, \"p\", 10);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"div\")(18, \"label\", 11);\n          i0.ɵɵtext(19, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(20, \"input\", 12);\n          i0.ɵɵtemplate(21, LoginComponent_Conditional_21_Template, 2, 0, \"p\", 10);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\")(23, \"button\", 13);\n          i0.ɵɵtemplate(24, LoginComponent_Conditional_24_Template, 1, 0, \"span\", 14);\n          i0.ɵɵtext(25, \" Sign in \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          let tmp_2_0;\n          let tmp_3_0;\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.error ? 10 : -1);\n          i0.ɵɵadvance(6);\n          i0.ɵɵconditional(((tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.invalid) && ((tmp_2_0 = ctx.loginForm.get(\"email\")) == null ? null : tmp_2_0.touched) ? 16 : -1);\n          i0.ɵɵadvance(5);\n          i0.ɵɵconditional(((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.invalid) && ((tmp_3_0 = ctx.loginForm.get(\"password\")) == null ? null : tmp_3_0.touched) ? 21 : -1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.loginForm.invalid || ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.loading ? 24 : -1);\n        }\n      },\n      dependencies: [CommonModule, ReactiveFormsModule, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.RequiredValidator, i1.FormGroupDirective, i1.FormControlName, RouterModule, i3.RouterLink],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "Validators", "RouterModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "error", "ɵɵelement", "LoginComponent", "constructor", "fb", "authService", "loading", "loginForm", "group", "email", "required", "password", "onSubmit", "valid", "login", "value", "subscribe", "next", "response", "handleAuthSuccess", "message", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_9_listener", "ɵɵtemplate", "LoginComponent_Conditional_10_Template", "LoginComponent_Conditional_16_Template", "LoginComponent_Conditional_21_Template", "LoginComponent_Conditional_24_Template", "ɵɵproperty", "ɵɵconditional", "tmp_2_0", "get", "invalid", "touched", "tmp_3_0", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "i3", "RouterLink", "encapsulation"], "sources": ["/var/www/html/trading-app/frontend/src/app/pages/login/login.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { AuthService } from '../../services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, RouterModule],\n  template: `\n    <div class=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div class=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 class=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Sign in to your account\n          </h2>\n          <p class=\"mt-2 text-center text-sm text-gray-600\">\n            Or\n            <a routerLink=\"/register\" class=\"font-medium text-primary-600 hover:text-primary-500\">\n              create a new account\n            </a>\n          </p>\n        </div>\n        \n        <form class=\"mt-8 space-y-6\" [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\n          @if (error) {\n            <div class=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded\">\n              {{ error }}\n            </div>\n          }\n          \n          <div class=\"space-y-4\">\n            <div>\n              <label for=\"email\" class=\"block text-sm font-medium text-gray-700\">Email address</label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                formControlName=\"email\"\n                required\n                class=\"input mt-1\"\n                placeholder=\"Enter your email\"\n              />\n              @if (loginForm.get('email')?.invalid && loginForm.get('email')?.touched) {\n                <p class=\"mt-1 text-sm text-red-600\">Please enter a valid email address</p>\n              }\n            </div>\n            \n            <div>\n              <label for=\"password\" class=\"block text-sm font-medium text-gray-700\">Password</label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                formControlName=\"password\"\n                required\n                class=\"input mt-1\"\n                placeholder=\"Enter your password\"\n              />\n              @if (loginForm.get('password')?.invalid && loginForm.get('password')?.touched) {\n                <p class=\"mt-1 text-sm text-red-600\">Password is required</p>\n              }\n            </div>\n          </div>\n\n          <div>\n            <button\n              type=\"submit\"\n              [disabled]=\"loginForm.invalid || loading\"\n              class=\"w-full btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              @if (loading) {\n                <span class=\"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></span>\n              }\n              Sign in\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  `\n})\nexport class LoginComponent {\n  loginForm: FormGroup;\n  loading = false;\n  error = '';\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService\n  ) {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', Validators.required]\n    });\n  }\n\n  onSubmit(): void {\n    if (this.loginForm.valid) {\n      this.loading = true;\n      this.error = '';\n\n      this.authService.login(this.loginForm.value).subscribe({\n        next: (response) => {\n          this.loading = false;\n          this.authService.handleAuthSuccess(response);\n        },\n        error: (error) => {\n          this.loading = false;\n          this.error = error.error?.message || 'Login failed. Please try again.';\n        }\n      });\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAA0BC,UAAU,QAAQ,gBAAgB;AACxF,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;IAwBlCC,EAAA,CAAAC,cAAA,aAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAC,KAAA,MACF;;;;;IAgBIP,EAAA,CAAAC,cAAA,YAAqC;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAgB3EH,EAAA,CAAAC,cAAA,YAAqC;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;IAY7DH,EAAA,CAAAQ,SAAA,eAAiG;;;AAUjH,OAAM,MAAOC,cAAc;EAKzBC,YACUC,EAAe,EACfC,WAAwB;IADxB,KAAAD,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IALrB,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAN,KAAK,GAAG,EAAE;IAMR,IAAI,CAACO,SAAS,GAAG,IAAI,CAACH,EAAE,CAACI,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACkB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAEpB,UAAU,CAACmB,QAAQ;KACnC,CAAC;EACJ;EAEAE,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,SAAS,CAACM,KAAK,EAAE;MACxB,IAAI,CAACP,OAAO,GAAG,IAAI;MACnB,IAAI,CAACN,KAAK,GAAG,EAAE;MAEf,IAAI,CAACK,WAAW,CAACS,KAAK,CAAC,IAAI,CAACP,SAAS,CAACQ,KAAK,CAAC,CAACC,SAAS,CAAC;QACrDC,IAAI,EAAGC,QAAQ,IAAI;UACjB,IAAI,CAACZ,OAAO,GAAG,KAAK;UACpB,IAAI,CAACD,WAAW,CAACc,iBAAiB,CAACD,QAAQ,CAAC;QAC9C,CAAC;QACDlB,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACM,OAAO,GAAG,KAAK;UACpB,IAAI,CAACN,KAAK,GAAGA,KAAK,CAACA,KAAK,EAAEoB,OAAO,IAAI,iCAAiC;QACxE;OACD,CAAC;IACJ;EACF;;;uCA/BWlB,cAAc,EAAAT,EAAA,CAAA4B,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA9B,EAAA,CAAA4B,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAdvB,cAAc;MAAAwB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAnC,EAAA,CAAAoC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArEjB1C,EAHN,CAAAC,cAAA,aAAiG,aACxD,UAChC,YACgE;UACjED,EAAA,CAAAE,MAAA,gCACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAAkD;UAChDD,EAAA,CAAAE,MAAA,WACA;UAAAF,EAAA,CAAAC,cAAA,WAAsF;UACpFD,EAAA,CAAAE,MAAA,6BACF;UAEJF,EAFI,CAAAG,YAAA,EAAI,EACF,EACA;UAENH,EAAA,CAAAC,cAAA,cAA6E;UAAxBD,EAAA,CAAA4C,UAAA,sBAAAC,iDAAA;YAAA,OAAYF,GAAA,CAAAxB,QAAA,EAAU;UAAA,EAAC;UAC1EnB,EAAA,CAAA8C,UAAA,KAAAC,sCAAA,iBAAa;UAQT/C,EAFJ,CAAAC,cAAA,cAAuB,WAChB,gBACgE;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACxFH,EAAA,CAAAQ,SAAA,gBAQE;UACFR,EAAA,CAAA8C,UAAA,KAAAE,sCAAA,gBAA0E;UAG5EhD,EAAA,CAAAG,YAAA,EAAM;UAGJH,EADF,CAAAC,cAAA,WAAK,iBACmE;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UACtFH,EAAA,CAAAQ,SAAA,iBAQE;UACFR,EAAA,CAAA8C,UAAA,KAAAG,sCAAA,gBAAgF;UAIpFjD,EADE,CAAAG,YAAA,EAAM,EACF;UAGJH,EADF,CAAAC,cAAA,WAAK,kBAKF;UACCD,EAAA,CAAA8C,UAAA,KAAAI,sCAAA,mBAAe;UAGflD,EAAA,CAAAE,MAAA,iBACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACD,EACH,EACF;;;;;UAvD2BH,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAmD,UAAA,cAAAR,GAAA,CAAA7B,SAAA,CAAuB;UAClDd,EAAA,CAAAI,SAAA,EAIC;UAJDJ,EAAA,CAAAoD,aAAA,CAAAT,GAAA,CAAApC,KAAA,WAIC;UAcGP,EAAA,CAAAI,SAAA,GAEC;UAFDJ,EAAA,CAAAoD,aAAA,GAAAC,OAAA,GAAAV,GAAA,CAAA7B,SAAA,CAAAwC,GAAA,4BAAAD,OAAA,CAAAE,OAAA,OAAAF,OAAA,GAAAV,GAAA,CAAA7B,SAAA,CAAAwC,GAAA,4BAAAD,OAAA,CAAAG,OAAA,YAEC;UAcDxD,EAAA,CAAAI,SAAA,GAEC;UAFDJ,EAAA,CAAAoD,aAAA,GAAAK,OAAA,GAAAd,GAAA,CAAA7B,SAAA,CAAAwC,GAAA,+BAAAG,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAd,GAAA,CAAA7B,SAAA,CAAAwC,GAAA,+BAAAG,OAAA,CAAAD,OAAA,YAEC;UAODxD,EAAA,CAAAI,SAAA,GAAyC;UAAzCJ,EAAA,CAAAmD,UAAA,aAAAR,GAAA,CAAA7B,SAAA,CAAAyC,OAAA,IAAAZ,GAAA,CAAA9B,OAAA,CAAyC;UAGzCb,EAAA,CAAAI,SAAA,EAEC;UAFDJ,EAAA,CAAAoD,aAAA,CAAAT,GAAA,CAAA9B,OAAA,WAEC;;;qBAjEHjB,YAAY,EAAEC,mBAAmB,EAAAgC,EAAA,CAAA6B,aAAA,EAAA7B,EAAA,CAAA8B,oBAAA,EAAA9B,EAAA,CAAA+B,eAAA,EAAA/B,EAAA,CAAAgC,oBAAA,EAAAhC,EAAA,CAAAiC,iBAAA,EAAAjC,EAAA,CAAAkC,kBAAA,EAAAlC,EAAA,CAAAmC,eAAA,EAAEjE,YAAY,EAAAkE,EAAA,CAAAC,UAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}