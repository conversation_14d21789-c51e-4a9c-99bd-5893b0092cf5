{"ast": null, "code": "import { authGuard } from './guards/auth.guard';\nexport const routes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent)\n}, {\n  path: 'register',\n  loadComponent: () => import('./pages/register/register.component').then(m => m.RegisterComponent)\n}, {\n  path: 'dashboard',\n  loadComponent: () => import('./pages/dashboard/dashboard.component').then(m => m.DashboardComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'markets',\n  loadComponent: () => import('./pages/markets/markets.component').then(m => m.MarketsComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'indian-stocks',\n  loadComponent: () => import('./pages/indian-stocks/indian-stocks.component').then(m => m.IndianStocksComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'trading',\n  loadComponent: () => import('./pages/trading/trading.component').then(m => m.TradingComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'portfolio',\n  loadComponent: () => import('./pages/portfolio/portfolio.component').then(m => m.PortfolioComponent),\n  canActivate: [authGuard]\n}, {\n  path: 'admin',\n  loadComponent: () => import('./pages/admin/admin.component').then(m => m.AdminComponent),\n  canActivate: [authGuard]\n}, {\n  path: '**',\n  redirectTo: '/dashboard'\n}];", "map": {"version": 3, "names": ["<PERSON>th<PERSON><PERSON>", "routes", "path", "redirectTo", "pathMatch", "loadComponent", "then", "m", "LoginComponent", "RegisterComponent", "DashboardComponent", "canActivate", "MarketsComponent", "IndianStocksComponent", "TradingComponent", "PortfolioComponent", "AdminComponent"], "sources": ["/var/www/html/trading-app/frontend/src/app/app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { authGuard } from './guards/auth.guard';\n\nexport const routes: Routes = [\n  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },\n  { \n    path: 'login', \n    loadComponent: () => import('./pages/login/login.component').then(m => m.LoginComponent)\n  },\n  { \n    path: 'register', \n    loadComponent: () => import('./pages/register/register.component').then(m => m.RegisterComponent)\n  },\n  { \n    path: 'dashboard', \n    loadComponent: () => import('./pages/dashboard/dashboard.component').then(m => m.DashboardComponent),\n    canActivate: [authGuard]\n  },\n  {\n    path: 'markets',\n    loadComponent: () => import('./pages/markets/markets.component').then(m => m.MarketsComponent),\n    canActivate: [authGuard]\n  },\n  {\n    path: 'indian-stocks',\n    loadComponent: () => import('./pages/indian-stocks/indian-stocks.component').then(m => m.IndianStocksComponent),\n    canActivate: [authGuard]\n  },\n  { \n    path: 'trading', \n    loadComponent: () => import('./pages/trading/trading.component').then(m => m.TradingComponent),\n    canActivate: [authGuard]\n  },\n  { \n    path: 'portfolio', \n    loadComponent: () => import('./pages/portfolio/portfolio.component').then(m => m.PortfolioComponent),\n    canActivate: [authGuard]\n  },\n  { \n    path: 'admin', \n    loadComponent: () => import('./pages/admin/admin.component').then(m => m.AdminComponent),\n    canActivate: [authGuard]\n  },\n  { path: '**', redirectTo: '/dashboard' }\n];\n"], "mappings": "AACA,SAASA,SAAS,QAAQ,qBAAqB;AAE/C,OAAO,MAAMC,MAAM,GAAW,CAC5B;EAAEC,IAAI,EAAE,EAAE;EAAEC,UAAU,EAAE,YAAY;EAAEC,SAAS,EAAE;AAAM,CAAE,EACzD;EACEF,IAAI,EAAE,OAAO;EACbG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,cAAc;CACxF,EACD;EACEN,IAAI,EAAE,UAAU;EAChBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,iBAAiB;CACjG,EACD;EACEP,IAAI,EAAE,WAAW;EACjBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,kBAAkB,CAAC;EACpGC,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,SAAS;EACfG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,gBAAgB,CAAC;EAC9FD,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,eAAe;EACrBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,qBAAqB,CAAC;EAC/GF,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,SAAS;EACfG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,gBAAgB,CAAC;EAC9FH,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,WAAW;EACjBG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,kBAAkB,CAAC;EACpGJ,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EACEE,IAAI,EAAE,OAAO;EACbG,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+BAA+B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,cAAc,CAAC;EACxFL,WAAW,EAAE,CAACX,SAAS;CACxB,EACD;EAAEE,IAAI,EAAE,IAAI;EAAEC,UAAU,EAAE;AAAY,CAAE,CACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}