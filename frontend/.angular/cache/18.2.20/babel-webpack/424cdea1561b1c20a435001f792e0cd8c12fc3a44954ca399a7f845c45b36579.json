{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport class PortfolioComponent {\n  static {\n    this.ɵfac = function PortfolioComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PortfolioComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PortfolioComponent,\n      selectors: [[\"app-portfolio\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 0,\n      consts: [[1, \"space-y-6\"], [1, \"card\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-900\", \"mb-4\"], [1, \"text-gray-600\"]],\n      template: function PortfolioComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"Portfolio\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 3);\n          i0.ɵɵtext(5, \"Portfolio management will be implemented here.\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [CommonModule],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "PortfolioComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "PortfolioComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "encapsulation"], "sources": ["/var/www/html/trading-app/frontend/src/app/pages/portfolio/portfolio.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-portfolio',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"space-y-6\">\n      <div class=\"card\">\n        <h1 class=\"text-2xl font-bold text-gray-900 mb-4\">Portfolio</h1>\n        <p class=\"text-gray-600\">Portfolio management will be implemented here.</p>\n      </div>\n    </div>\n  `\n})\nexport class PortfolioComponent {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAe9C,OAAM,MAAOC,kBAAkB;;;uCAAlBA,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UANvBP,EAFJ,CAAAS,cAAA,aAAuB,aACH,YACkC;UAAAT,EAAA,CAAAU,MAAA,gBAAS;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAChEX,EAAA,CAAAS,cAAA,WAAyB;UAAAT,EAAA,CAAAU,MAAA,qDAA8C;UAE3EV,EAF2E,CAAAW,YAAA,EAAI,EACvE,EACF;;;qBAPEhB,YAAY;MAAAiB,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}