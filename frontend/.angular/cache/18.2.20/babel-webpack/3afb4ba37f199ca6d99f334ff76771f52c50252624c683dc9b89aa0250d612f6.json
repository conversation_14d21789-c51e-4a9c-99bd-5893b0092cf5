{"ast": null, "code": "import _asyncToGenerator from \"/var/www/html/trading-app/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class IndianStocksService {\n  constructor(http) {\n    this.http = http;\n    // Using NSE India API for real stock data\n    this.NSE_API_BASE = 'https://www.nseindia.com/api';\n    this.CORS_PROXY = 'https://api.allorigins.win/raw?url=';\n    // Alternative APIs for stock data\n    this.ALPHA_VANTAGE_API = 'https://www.alphavantage.co/query';\n    this.FINNHUB_API = 'https://finnhub.io/api/v1';\n    // Sector mapping for Indian stocks\n    this.SECTOR_MAPPING = {\n      'RELIANCE': 'Energy',\n      'TCS': 'Technology',\n      'HDFCBANK': 'Banking',\n      'INFY': 'Technology',\n      'HINDUNILVR': 'Consumer Goods',\n      'ICICIBANK': 'Banking',\n      'KOTAKBANK': 'Banking',\n      'BHARTIARTL': 'Telecommunications',\n      'ITC': 'Consumer Goods',\n      'SBIN': 'Banking',\n      'LT': 'Construction',\n      'HCLTECH': 'Technology',\n      'ASIANPAINT': 'Chemicals',\n      'MARUTI': 'Automotive',\n      'BAJFINANCE': 'Financial Services',\n      'WIPRO': 'Technology',\n      'ULTRACEMCO': 'Cement',\n      'AXISBANK': 'Banking',\n      'TITAN': 'Consumer Goods',\n      'SUNPHARMA': 'Pharmaceuticals'\n    };\n  }\n  getIndianStocks() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Try to fetch real data from NSE API\n        const realData = yield _this.fetchNSEStocks();\n        if (realData && realData.length > 0) {\n          return realData;\n        }\n      } catch (error) {\n        console.warn('Failed to fetch real NSE data, falling back to mock data:', error);\n      }\n      // Fallback to enhanced mock data\n      return _this.getEnhancedMockData();\n    })();\n  }\n  fetchNSEStocks() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Fetch NIFTY 50 stocks from NSE\n        const nifty50Url = `${_this2.NSE_API_BASE}/equity-stockIndices?index=NIFTY%2050`;\n        const proxyUrl = `${_this2.CORS_PROXY}${encodeURIComponent(nifty50Url)}`;\n        const response = yield _this2.http.get(proxyUrl).toPromise();\n        if (response && response.data) {\n          return response.data.map(stock => _this2.mapNSEToIndianStock(stock));\n        }\n        return [];\n      } catch (error) {\n        console.error('Error fetching NSE data:', error);\n        throw error;\n      }\n    })();\n  }\n  mapNSEToIndianStock(nseStock) {\n    return {\n      symbol: nseStock.symbol,\n      name: nseStock.symbol,\n      // NSE API doesn't always provide full company name\n      price: parseFloat(nseStock.lastPrice) || 0,\n      change: parseFloat(nseStock.change) || 0,\n      changePercent: parseFloat(nseStock.pChange) || 0,\n      volume: parseInt(nseStock.totalTradedVolume) || 0,\n      high: parseFloat(nseStock.dayHigh) || 0,\n      low: parseFloat(nseStock.dayLow) || 0,\n      open: parseFloat(nseStock.open) || 0,\n      previousClose: parseFloat(nseStock.previousClose) || 0,\n      sector: this.SECTOR_MAPPING[nseStock.symbol] || 'Others'\n    };\n  }\n  getEnhancedMockData() {\n    // Enhanced mock data with more realistic Indian stock information\n    const stocksData = [{\n      symbol: 'RELIANCE',\n      name: 'Reliance Industries Limited',\n      basePrice: 2450,\n      sector: 'Energy'\n    }, {\n      symbol: 'TCS',\n      name: 'Tata Consultancy Services',\n      basePrice: 3650,\n      sector: 'Technology'\n    }, {\n      symbol: 'HDFCBANK',\n      name: 'HDFC Bank Limited',\n      basePrice: 1580,\n      sector: 'Banking'\n    }, {\n      symbol: 'INFY',\n      name: 'Infosys Limited',\n      basePrice: 1420,\n      sector: 'Technology'\n    }, {\n      symbol: 'HINDUNILVR',\n      name: 'Hindustan Unilever Limited',\n      basePrice: 2380,\n      sector: 'Consumer Goods'\n    }, {\n      symbol: 'ICICIBANK',\n      name: 'ICICI Bank Limited',\n      basePrice: 950,\n      sector: 'Banking'\n    }, {\n      symbol: 'KOTAKBANK',\n      name: 'Kotak Mahindra Bank',\n      basePrice: 1750,\n      sector: 'Banking'\n    }, {\n      symbol: 'BHARTIARTL',\n      name: 'Bharti Airtel Limited',\n      basePrice: 850,\n      sector: 'Telecommunications'\n    }, {\n      symbol: 'ITC',\n      name: 'ITC Limited',\n      basePrice: 420,\n      sector: 'Consumer Goods'\n    }, {\n      symbol: 'SBIN',\n      name: 'State Bank of India',\n      basePrice: 580,\n      sector: 'Banking'\n    }, {\n      symbol: 'LT',\n      name: 'Larsen & Toubro Limited',\n      basePrice: 2150,\n      sector: 'Construction'\n    }, {\n      symbol: 'HCLTECH',\n      name: 'HCL Technologies Limited',\n      basePrice: 1180,\n      sector: 'Technology'\n    }, {\n      symbol: 'ASIANPAINT',\n      name: 'Asian Paints Limited',\n      basePrice: 3250,\n      sector: 'Chemicals'\n    }, {\n      symbol: 'MARUTI',\n      name: 'Maruti Suzuki India Limited',\n      basePrice: 9850,\n      sector: 'Automotive'\n    }, {\n      symbol: 'BAJFINANCE',\n      name: 'Bajaj Finance Limited',\n      basePrice: 6750,\n      sector: 'Financial Services'\n    }, {\n      symbol: 'WIPRO',\n      name: 'Wipro Limited',\n      basePrice: 420,\n      sector: 'Technology'\n    }, {\n      symbol: 'ULTRACEMCO',\n      name: 'UltraTech Cement Limited',\n      basePrice: 8950,\n      sector: 'Cement'\n    }, {\n      symbol: 'AXISBANK',\n      name: 'Axis Bank Limited',\n      basePrice: 1050,\n      sector: 'Banking'\n    }, {\n      symbol: 'TITAN',\n      name: 'Titan Company Limited',\n      basePrice: 2850,\n      sector: 'Consumer Goods'\n    }, {\n      symbol: 'SUNPHARMA',\n      name: 'Sun Pharmaceutical Industries',\n      basePrice: 1150,\n      sector: 'Pharmaceuticals'\n    }, {\n      symbol: 'NESTLEIND',\n      name: 'Nestle India Limited',\n      basePrice: 22500,\n      sector: 'Consumer Goods'\n    }, {\n      symbol: 'POWERGRID',\n      name: 'Power Grid Corporation',\n      basePrice: 220,\n      sector: 'Utilities'\n    }, {\n      symbol: 'NTPC',\n      name: 'NTPC Limited',\n      basePrice: 180,\n      sector: 'Utilities'\n    }, {\n      symbol: 'ONGC',\n      name: 'Oil & Natural Gas Corporation',\n      basePrice: 160,\n      sector: 'Energy'\n    }, {\n      symbol: 'TECHM',\n      name: 'Tech Mahindra Limited',\n      basePrice: 1450,\n      sector: 'Technology'\n    }, {\n      symbol: 'TATAMOTORS',\n      name: 'Tata Motors Limited',\n      basePrice: 750,\n      sector: 'Automotive'\n    }, {\n      symbol: 'TATASTEEL',\n      name: 'Tata Steel Limited',\n      basePrice: 120,\n      sector: 'Steel'\n    }, {\n      symbol: 'JSWSTEEL',\n      name: 'JSW Steel Limited',\n      basePrice: 850,\n      sector: 'Steel'\n    }, {\n      symbol: 'HINDALCO',\n      name: 'Hindalco Industries Limited',\n      basePrice: 450,\n      sector: 'Metals'\n    }, {\n      symbol: 'COALINDIA',\n      name: 'Coal India Limited',\n      basePrice: 280,\n      sector: 'Mining'\n    }];\n    return stocksData.map(stock => {\n      const priceVariation = (Math.random() - 0.5) * 0.1; // ±5% variation\n      const currentPrice = stock.basePrice * (1 + priceVariation);\n      const change = currentPrice - stock.basePrice;\n      const changePercent = change / stock.basePrice * 100;\n      const volume = Math.floor(Math.random() * 50000000) + 1000000; // 1M to 50M volume\n      return {\n        symbol: stock.symbol,\n        name: stock.name,\n        price: Math.round(currentPrice * 100) / 100,\n        change: Math.round(change * 100) / 100,\n        changePercent: Math.round(changePercent * 100) / 100,\n        volume: volume,\n        high: Math.round(currentPrice * 1.02 * 100) / 100,\n        low: Math.round(currentPrice * 0.98 * 100) / 100,\n        open: Math.round(stock.basePrice * (1 + (Math.random() - 0.5) * 0.02) * 100) / 100,\n        previousClose: stock.basePrice,\n        sector: stock.sector,\n        marketCap: Math.floor(currentPrice * Math.random() * 1000000000 / 10000000) // Market cap in crores\n      };\n    });\n  }\n  // Method to get real-time data (would require API key in production)\n  getRealTimePrice(symbol) {\n    return _asyncToGenerator(function* () {\n      try {\n        // This would require an API key in production\n        // const response = await this.http.get(`${this.API_BASE}/price?symbol=${symbol}&apikey=YOUR_API_KEY`).toPromise();\n        // return response;\n        // For demo, return mock data\n        return {\n          price: (Math.random() * 3000 + 100).toFixed(2),\n          change: ((Math.random() - 0.5) * 100).toFixed(2)\n        };\n      } catch (error) {\n        console.error('Error fetching real-time price:', error);\n        return null;\n      }\n    })();\n  }\n  // Get top gainers\n  getTopGainers(stocks) {\n    return stocks.filter(stock => stock.changePercent > 0).sort((a, b) => b.changePercent - a.changePercent).slice(0, 5);\n  }\n  // Get top losers\n  getTopLosers(stocks) {\n    return stocks.filter(stock => stock.changePercent < 0).sort((a, b) => a.changePercent - b.changePercent).slice(0, 5);\n  }\n  // Get stocks by sector\n  getStocksBySector(stocks, sector) {\n    return stocks.filter(stock => stock.sector === sector);\n  }\n  // Get available sectors\n  getAvailableSectors(stocks) {\n    const sectors = stocks.map(stock => stock.sector).filter(Boolean);\n    return [...new Set(sectors)];\n  }\n  static {\n    this.ɵfac = function IndianStocksService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IndianStocksService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: IndianStocksService,\n      factory: IndianStocksService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["IndianStocksService", "constructor", "http", "NSE_API_BASE", "CORS_PROXY", "ALPHA_VANTAGE_API", "FINNHUB_API", "SECTOR_MAPPING", "getIndianStocks", "_this", "_asyncToGenerator", "realData", "fetchNSEStocks", "length", "error", "console", "warn", "getEnhancedMockData", "_this2", "nifty50Url", "proxyUrl", "encodeURIComponent", "response", "get", "to<PERSON>romise", "data", "map", "stock", "mapNSEToIndianStock", "nseStock", "symbol", "name", "price", "parseFloat", "lastPrice", "change", "changePercent", "pChange", "volume", "parseInt", "totalTradedVolume", "high", "dayHigh", "low", "dayLow", "open", "previousClose", "sector", "stocksData", "basePrice", "priceVariation", "Math", "random", "currentPrice", "floor", "round", "marketCap", "getRealTimePrice", "toFixed", "getTopGainers", "stocks", "filter", "sort", "a", "b", "slice", "getTopLosers", "getStocksBySector", "getAvailableSectors", "sectors", "Boolean", "Set", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/var/www/html/trading-app/frontend/src/app/services/indian-stocks.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\n\nexport interface IndianStock {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  marketCap?: number;\n  sector?: string;\n  high?: number;\n  low?: number;\n  open?: number;\n  previousClose?: number;\n}\n\nexport interface NSEStock {\n  symbol: string;\n  companyName: string;\n  lastPrice: number;\n  change: number;\n  pChange: number;\n  totalTradedVolume: number;\n  totalTradedValue: number;\n  dayHigh: number;\n  dayLow: number;\n  open: number;\n  previousClose: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class IndianStocksService {\n  // Using NSE India API for real stock data\n  private readonly NSE_API_BASE = 'https://www.nseindia.com/api';\n  private readonly CORS_PROXY = 'https://api.allorigins.win/raw?url=';\n\n  // Alternative APIs for stock data\n  private readonly ALPHA_VANTAGE_API = 'https://www.alphavantage.co/query';\n  private readonly FINNHUB_API = 'https://finnhub.io/api/v1';\n\n  // Sector mapping for Indian stocks\n  private readonly SECTOR_MAPPING: { [key: string]: string } = {\n    'RELIANCE': 'Energy',\n    'TCS': 'Technology',\n    'HDFCBANK': 'Banking',\n    'INFY': 'Technology',\n    'HINDUNILVR': 'Consumer Goods',\n    'ICICIBANK': 'Banking',\n    'KOTAKBANK': 'Banking',\n    'BHARTIARTL': 'Telecommunications',\n    'ITC': 'Consumer Goods',\n    'SBIN': 'Banking',\n    'LT': 'Construction',\n    'HCLTECH': 'Technology',\n    'ASIANPAINT': 'Chemicals',\n    'MARUTI': 'Automotive',\n    'BAJFINANCE': 'Financial Services',\n    'WIPRO': 'Technology',\n    'ULTRACEMCO': 'Cement',\n    'AXISBANK': 'Banking',\n    'TITAN': 'Consumer Goods',\n    'SUNPHARMA': 'Pharmaceuticals'\n  };\n\n  constructor(private http: HttpClient) {}\n\n  async getIndianStocks(): Promise<IndianStock[]> {\n    try {\n      // Try to fetch real data from NSE API\n      const realData = await this.fetchNSEStocks();\n      if (realData && realData.length > 0) {\n        return realData;\n      }\n    } catch (error) {\n      console.warn('Failed to fetch real NSE data, falling back to mock data:', error);\n    }\n\n    // Fallback to enhanced mock data\n    return this.getEnhancedMockData();\n  }\n\n  private async fetchNSEStocks(): Promise<IndianStock[]> {\n    try {\n      // Fetch NIFTY 50 stocks from NSE\n      const nifty50Url = `${this.NSE_API_BASE}/equity-stockIndices?index=NIFTY%2050`;\n      const proxyUrl = `${this.CORS_PROXY}${encodeURIComponent(nifty50Url)}`;\n\n      const response = await this.http.get<any>(proxyUrl).toPromise();\n\n      if (response && response.data) {\n        return response.data.map((stock: any) => this.mapNSEToIndianStock(stock));\n      }\n\n      return [];\n    } catch (error) {\n      console.error('Error fetching NSE data:', error);\n      throw error;\n    }\n  }\n\n  private mapNSEToIndianStock(nseStock: any): IndianStock {\n    return {\n      symbol: nseStock.symbol,\n      name: nseStock.symbol, // NSE API doesn't always provide full company name\n      price: parseFloat(nseStock.lastPrice) || 0,\n      change: parseFloat(nseStock.change) || 0,\n      changePercent: parseFloat(nseStock.pChange) || 0,\n      volume: parseInt(nseStock.totalTradedVolume) || 0,\n      high: parseFloat(nseStock.dayHigh) || 0,\n      low: parseFloat(nseStock.dayLow) || 0,\n      open: parseFloat(nseStock.open) || 0,\n      previousClose: parseFloat(nseStock.previousClose) || 0,\n      sector: this.SECTOR_MAPPING[nseStock.symbol] || 'Others'\n    };\n  }\n\n  private getEnhancedMockData(): IndianStock[] {\n    // Enhanced mock data with more realistic Indian stock information\n    const stocksData = [\n      { symbol: 'RELIANCE', name: 'Reliance Industries Limited', basePrice: 2450, sector: 'Energy' },\n      { symbol: 'TCS', name: 'Tata Consultancy Services', basePrice: 3650, sector: 'Technology' },\n      { symbol: 'HDFCBANK', name: 'HDFC Bank Limited', basePrice: 1580, sector: 'Banking' },\n      { symbol: 'INFY', name: 'Infosys Limited', basePrice: 1420, sector: 'Technology' },\n      { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Limited', basePrice: 2380, sector: 'Consumer Goods' },\n      { symbol: 'ICICIBANK', name: 'ICICI Bank Limited', basePrice: 950, sector: 'Banking' },\n      { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank', basePrice: 1750, sector: 'Banking' },\n      { symbol: 'BHARTIARTL', name: 'Bharti Airtel Limited', basePrice: 850, sector: 'Telecommunications' },\n      { symbol: 'ITC', name: 'ITC Limited', basePrice: 420, sector: 'Consumer Goods' },\n      { symbol: 'SBIN', name: 'State Bank of India', basePrice: 580, sector: 'Banking' },\n      { symbol: 'LT', name: 'Larsen & Toubro Limited', basePrice: 2150, sector: 'Construction' },\n      { symbol: 'HCLTECH', name: 'HCL Technologies Limited', basePrice: 1180, sector: 'Technology' },\n      { symbol: 'ASIANPAINT', name: 'Asian Paints Limited', basePrice: 3250, sector: 'Chemicals' },\n      { symbol: 'MARUTI', name: 'Maruti Suzuki India Limited', basePrice: 9850, sector: 'Automotive' },\n      { symbol: 'BAJFINANCE', name: 'Bajaj Finance Limited', basePrice: 6750, sector: 'Financial Services' },\n      { symbol: 'WIPRO', name: 'Wipro Limited', basePrice: 420, sector: 'Technology' },\n      { symbol: 'ULTRACEMCO', name: 'UltraTech Cement Limited', basePrice: 8950, sector: 'Cement' },\n      { symbol: 'AXISBANK', name: 'Axis Bank Limited', basePrice: 1050, sector: 'Banking' },\n      { symbol: 'TITAN', name: 'Titan Company Limited', basePrice: 2850, sector: 'Consumer Goods' },\n      { symbol: 'SUNPHARMA', name: 'Sun Pharmaceutical Industries', basePrice: 1150, sector: 'Pharmaceuticals' },\n      { symbol: 'NESTLEIND', name: 'Nestle India Limited', basePrice: 22500, sector: 'Consumer Goods' },\n      { symbol: 'POWERGRID', name: 'Power Grid Corporation', basePrice: 220, sector: 'Utilities' },\n      { symbol: 'NTPC', name: 'NTPC Limited', basePrice: 180, sector: 'Utilities' },\n      { symbol: 'ONGC', name: 'Oil & Natural Gas Corporation', basePrice: 160, sector: 'Energy' },\n      { symbol: 'TECHM', name: 'Tech Mahindra Limited', basePrice: 1450, sector: 'Technology' },\n      { symbol: 'TATAMOTORS', name: 'Tata Motors Limited', basePrice: 750, sector: 'Automotive' },\n      { symbol: 'TATASTEEL', name: 'Tata Steel Limited', basePrice: 120, sector: 'Steel' },\n      { symbol: 'JSWSTEEL', name: 'JSW Steel Limited', basePrice: 850, sector: 'Steel' },\n      { symbol: 'HINDALCO', name: 'Hindalco Industries Limited', basePrice: 450, sector: 'Metals' },\n      { symbol: 'COALINDIA', name: 'Coal India Limited', basePrice: 280, sector: 'Mining' }\n    ];\n\n    return stocksData.map(stock => {\n      const priceVariation = (Math.random() - 0.5) * 0.1; // ±5% variation\n      const currentPrice = stock.basePrice * (1 + priceVariation);\n      const change = currentPrice - stock.basePrice;\n      const changePercent = (change / stock.basePrice) * 100;\n      const volume = Math.floor(Math.random() * 50000000) + 1000000; // 1M to 50M volume\n\n      return {\n        symbol: stock.symbol,\n        name: stock.name,\n        price: Math.round(currentPrice * 100) / 100,\n        change: Math.round(change * 100) / 100,\n        changePercent: Math.round(changePercent * 100) / 100,\n        volume: volume,\n        high: Math.round(currentPrice * 1.02 * 100) / 100,\n        low: Math.round(currentPrice * 0.98 * 100) / 100,\n        open: Math.round(stock.basePrice * (1 + (Math.random() - 0.5) * 0.02) * 100) / 100,\n        previousClose: stock.basePrice,\n        sector: stock.sector,\n        marketCap: Math.floor((currentPrice * Math.random() * 1000000000) / 10000000) // Market cap in crores\n      };\n    });\n  }\n\n  // Method to get real-time data (would require API key in production)\n  async getRealTimePrice(symbol: string): Promise<any> {\n    try {\n      // This would require an API key in production\n      // const response = await this.http.get(`${this.API_BASE}/price?symbol=${symbol}&apikey=YOUR_API_KEY`).toPromise();\n      // return response;\n      \n      // For demo, return mock data\n      return {\n        price: (Math.random() * 3000 + 100).toFixed(2),\n        change: ((Math.random() - 0.5) * 100).toFixed(2)\n      };\n    } catch (error) {\n      console.error('Error fetching real-time price:', error);\n      return null;\n    }\n  }\n\n  // Get top gainers\n  getTopGainers(stocks: IndianStock[]): IndianStock[] {\n    return stocks\n      .filter(stock => stock.changePercent > 0)\n      .sort((a, b) => b.changePercent - a.changePercent)\n      .slice(0, 5);\n  }\n\n  // Get top losers\n  getTopLosers(stocks: IndianStock[]): IndianStock[] {\n    return stocks\n      .filter(stock => stock.changePercent < 0)\n      .sort((a, b) => a.changePercent - b.changePercent)\n      .slice(0, 5);\n  }\n\n  // Get stocks by sector\n  getStocksBySector(stocks: IndianStock[], sector: string): IndianStock[] {\n    return stocks.filter(stock => stock.sector === sector);\n  }\n\n  // Get available sectors\n  getAvailableSectors(stocks: IndianStock[]): string[] {\n    const sectors = stocks.map(stock => stock.sector).filter(Boolean);\n    return [...new Set(sectors)] as string[];\n  }\n}\n"], "mappings": ";;;AAmCA,OAAM,MAAOA,mBAAmB;EAiC9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAhCxB;IACiB,KAAAC,YAAY,GAAG,8BAA8B;IAC7C,KAAAC,UAAU,GAAG,qCAAqC;IAEnE;IACiB,KAAAC,iBAAiB,GAAG,mCAAmC;IACvD,KAAAC,WAAW,GAAG,2BAA2B;IAE1D;IACiB,KAAAC,cAAc,GAA8B;MAC3D,UAAU,EAAE,QAAQ;MACpB,KAAK,EAAE,YAAY;MACnB,UAAU,EAAE,SAAS;MACrB,MAAM,EAAE,YAAY;MACpB,YAAY,EAAE,gBAAgB;MAC9B,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE,SAAS;MACtB,YAAY,EAAE,oBAAoB;MAClC,KAAK,EAAE,gBAAgB;MACvB,MAAM,EAAE,SAAS;MACjB,IAAI,EAAE,cAAc;MACpB,SAAS,EAAE,YAAY;MACvB,YAAY,EAAE,WAAW;MACzB,QAAQ,EAAE,YAAY;MACtB,YAAY,EAAE,oBAAoB;MAClC,OAAO,EAAE,YAAY;MACrB,YAAY,EAAE,QAAQ;MACtB,UAAU,EAAE,SAAS;MACrB,OAAO,EAAE,gBAAgB;MACzB,WAAW,EAAE;KACd;EAEsC;EAEjCC,eAAeA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnB,IAAI;QACF;QACA,MAAMC,QAAQ,SAASF,KAAI,CAACG,cAAc,EAAE;QAC5C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;UACnC,OAAOF,QAAQ;QACjB;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,2DAA2D,EAAEF,KAAK,CAAC;MAClF;MAEA;MACA,OAAOL,KAAI,CAACQ,mBAAmB,EAAE;IAAC;EACpC;EAEcL,cAAcA,CAAA;IAAA,IAAAM,MAAA;IAAA,OAAAR,iBAAA;MAC1B,IAAI;QACF;QACA,MAAMS,UAAU,GAAG,GAAGD,MAAI,CAACf,YAAY,uCAAuC;QAC9E,MAAMiB,QAAQ,GAAG,GAAGF,MAAI,CAACd,UAAU,GAAGiB,kBAAkB,CAACF,UAAU,CAAC,EAAE;QAEtE,MAAMG,QAAQ,SAASJ,MAAI,CAAChB,IAAI,CAACqB,GAAG,CAAMH,QAAQ,CAAC,CAACI,SAAS,EAAE;QAE/D,IAAIF,QAAQ,IAAIA,QAAQ,CAACG,IAAI,EAAE;UAC7B,OAAOH,QAAQ,CAACG,IAAI,CAACC,GAAG,CAAEC,KAAU,IAAKT,MAAI,CAACU,mBAAmB,CAACD,KAAK,CAAC,CAAC;QAC3E;QAEA,OAAO,EAAE;MACX,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK;MACb;IAAC;EACH;EAEQc,mBAAmBA,CAACC,QAAa;IACvC,OAAO;MACLC,MAAM,EAAED,QAAQ,CAACC,MAAM;MACvBC,IAAI,EAAEF,QAAQ,CAACC,MAAM;MAAE;MACvBE,KAAK,EAAEC,UAAU,CAACJ,QAAQ,CAACK,SAAS,CAAC,IAAI,CAAC;MAC1CC,MAAM,EAAEF,UAAU,CAACJ,QAAQ,CAACM,MAAM,CAAC,IAAI,CAAC;MACxCC,aAAa,EAAEH,UAAU,CAACJ,QAAQ,CAACQ,OAAO,CAAC,IAAI,CAAC;MAChDC,MAAM,EAAEC,QAAQ,CAACV,QAAQ,CAACW,iBAAiB,CAAC,IAAI,CAAC;MACjDC,IAAI,EAAER,UAAU,CAACJ,QAAQ,CAACa,OAAO,CAAC,IAAI,CAAC;MACvCC,GAAG,EAAEV,UAAU,CAACJ,QAAQ,CAACe,MAAM,CAAC,IAAI,CAAC;MACrCC,IAAI,EAAEZ,UAAU,CAACJ,QAAQ,CAACgB,IAAI,CAAC,IAAI,CAAC;MACpCC,aAAa,EAAEb,UAAU,CAACJ,QAAQ,CAACiB,aAAa,CAAC,IAAI,CAAC;MACtDC,MAAM,EAAE,IAAI,CAACxC,cAAc,CAACsB,QAAQ,CAACC,MAAM,CAAC,IAAI;KACjD;EACH;EAEQb,mBAAmBA,CAAA;IACzB;IACA,MAAM+B,UAAU,GAAG,CACjB;MAAElB,MAAM,EAAE,UAAU;MAAEC,IAAI,EAAE,6BAA6B;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAQ,CAAE,EAC9F;MAAEjB,MAAM,EAAE,KAAK;MAAEC,IAAI,EAAE,2BAA2B;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAY,CAAE,EAC3F;MAAEjB,MAAM,EAAE,UAAU;MAAEC,IAAI,EAAE,mBAAmB;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAS,CAAE,EACrF;MAAEjB,MAAM,EAAE,MAAM;MAAEC,IAAI,EAAE,iBAAiB;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAY,CAAE,EAClF;MAAEjB,MAAM,EAAE,YAAY;MAAEC,IAAI,EAAE,4BAA4B;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAgB,CAAE,EACvG;MAAEjB,MAAM,EAAE,WAAW;MAAEC,IAAI,EAAE,oBAAoB;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAS,CAAE,EACtF;MAAEjB,MAAM,EAAE,WAAW;MAAEC,IAAI,EAAE,qBAAqB;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAS,CAAE,EACxF;MAAEjB,MAAM,EAAE,YAAY;MAAEC,IAAI,EAAE,uBAAuB;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAoB,CAAE,EACrG;MAAEjB,MAAM,EAAE,KAAK;MAAEC,IAAI,EAAE,aAAa;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAgB,CAAE,EAChF;MAAEjB,MAAM,EAAE,MAAM;MAAEC,IAAI,EAAE,qBAAqB;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAS,CAAE,EAClF;MAAEjB,MAAM,EAAE,IAAI;MAAEC,IAAI,EAAE,yBAAyB;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAc,CAAE,EAC1F;MAAEjB,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE,0BAA0B;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAY,CAAE,EAC9F;MAAEjB,MAAM,EAAE,YAAY;MAAEC,IAAI,EAAE,sBAAsB;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAW,CAAE,EAC5F;MAAEjB,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE,6BAA6B;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAY,CAAE,EAChG;MAAEjB,MAAM,EAAE,YAAY;MAAEC,IAAI,EAAE,uBAAuB;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAoB,CAAE,EACtG;MAAEjB,MAAM,EAAE,OAAO;MAAEC,IAAI,EAAE,eAAe;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAY,CAAE,EAChF;MAAEjB,MAAM,EAAE,YAAY;MAAEC,IAAI,EAAE,0BAA0B;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAQ,CAAE,EAC7F;MAAEjB,MAAM,EAAE,UAAU;MAAEC,IAAI,EAAE,mBAAmB;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAS,CAAE,EACrF;MAAEjB,MAAM,EAAE,OAAO;MAAEC,IAAI,EAAE,uBAAuB;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAgB,CAAE,EAC7F;MAAEjB,MAAM,EAAE,WAAW;MAAEC,IAAI,EAAE,+BAA+B;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAiB,CAAE,EAC1G;MAAEjB,MAAM,EAAE,WAAW;MAAEC,IAAI,EAAE,sBAAsB;MAAEkB,SAAS,EAAE,KAAK;MAAEF,MAAM,EAAE;IAAgB,CAAE,EACjG;MAAEjB,MAAM,EAAE,WAAW;MAAEC,IAAI,EAAE,wBAAwB;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAW,CAAE,EAC5F;MAAEjB,MAAM,EAAE,MAAM;MAAEC,IAAI,EAAE,cAAc;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAW,CAAE,EAC7E;MAAEjB,MAAM,EAAE,MAAM;MAAEC,IAAI,EAAE,+BAA+B;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAQ,CAAE,EAC3F;MAAEjB,MAAM,EAAE,OAAO;MAAEC,IAAI,EAAE,uBAAuB;MAAEkB,SAAS,EAAE,IAAI;MAAEF,MAAM,EAAE;IAAY,CAAE,EACzF;MAAEjB,MAAM,EAAE,YAAY;MAAEC,IAAI,EAAE,qBAAqB;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAY,CAAE,EAC3F;MAAEjB,MAAM,EAAE,WAAW;MAAEC,IAAI,EAAE,oBAAoB;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAO,CAAE,EACpF;MAAEjB,MAAM,EAAE,UAAU;MAAEC,IAAI,EAAE,mBAAmB;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAO,CAAE,EAClF;MAAEjB,MAAM,EAAE,UAAU;MAAEC,IAAI,EAAE,6BAA6B;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAQ,CAAE,EAC7F;MAAEjB,MAAM,EAAE,WAAW;MAAEC,IAAI,EAAE,oBAAoB;MAAEkB,SAAS,EAAE,GAAG;MAAEF,MAAM,EAAE;IAAQ,CAAE,CACtF;IAED,OAAOC,UAAU,CAACtB,GAAG,CAACC,KAAK,IAAG;MAC5B,MAAMuB,cAAc,GAAG,CAACC,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;MACpD,MAAMC,YAAY,GAAG1B,KAAK,CAACsB,SAAS,IAAI,CAAC,GAAGC,cAAc,CAAC;MAC3D,MAAMf,MAAM,GAAGkB,YAAY,GAAG1B,KAAK,CAACsB,SAAS;MAC7C,MAAMb,aAAa,GAAID,MAAM,GAAGR,KAAK,CAACsB,SAAS,GAAI,GAAG;MACtD,MAAMX,MAAM,GAAGa,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,OAAO,CAAC,CAAC;MAE/D,OAAO;QACLtB,MAAM,EAAEH,KAAK,CAACG,MAAM;QACpBC,IAAI,EAAEJ,KAAK,CAACI,IAAI;QAChBC,KAAK,EAAEmB,IAAI,CAACI,KAAK,CAACF,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG;QAC3ClB,MAAM,EAAEgB,IAAI,CAACI,KAAK,CAACpB,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;QACtCC,aAAa,EAAEe,IAAI,CAACI,KAAK,CAACnB,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG;QACpDE,MAAM,EAAEA,MAAM;QACdG,IAAI,EAAEU,IAAI,CAACI,KAAK,CAACF,YAAY,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;QACjDV,GAAG,EAAEQ,IAAI,CAACI,KAAK,CAACF,YAAY,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;QAChDR,IAAI,EAAEM,IAAI,CAACI,KAAK,CAAC5B,KAAK,CAACsB,SAAS,IAAI,CAAC,GAAG,CAACE,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;QAClFN,aAAa,EAAEnB,KAAK,CAACsB,SAAS;QAC9BF,MAAM,EAAEpB,KAAK,CAACoB,MAAM;QACpBS,SAAS,EAAEL,IAAI,CAACG,KAAK,CAAED,YAAY,GAAGF,IAAI,CAACC,MAAM,EAAE,GAAG,UAAU,GAAI,QAAQ,CAAC,CAAC;OAC/E;IACH,CAAC,CAAC;EACJ;EAEA;EACMK,gBAAgBA,CAAC3B,MAAc;IAAA,OAAApB,iBAAA;MACnC,IAAI;QACF;QACA;QACA;QAEA;QACA,OAAO;UACLsB,KAAK,EAAE,CAACmB,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,EAAEM,OAAO,CAAC,CAAC,CAAC;UAC9CvB,MAAM,EAAE,CAAC,CAACgB,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,EAAEM,OAAO,CAAC,CAAC;SAChD;MACH,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,OAAO,IAAI;MACb;IAAC;EACH;EAEA;EACA6C,aAAaA,CAACC,MAAqB;IACjC,OAAOA,MAAM,CACVC,MAAM,CAAClC,KAAK,IAAIA,KAAK,CAACS,aAAa,GAAG,CAAC,CAAC,CACxC0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC5B,aAAa,GAAG2B,CAAC,CAAC3B,aAAa,CAAC,CACjD6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA;EACAC,YAAYA,CAACN,MAAqB;IAChC,OAAOA,MAAM,CACVC,MAAM,CAAClC,KAAK,IAAIA,KAAK,CAACS,aAAa,GAAG,CAAC,CAAC,CACxC0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3B,aAAa,GAAG4B,CAAC,CAAC5B,aAAa,CAAC,CACjD6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA;EACAE,iBAAiBA,CAACP,MAAqB,EAAEb,MAAc;IACrD,OAAOa,MAAM,CAACC,MAAM,CAAClC,KAAK,IAAIA,KAAK,CAACoB,MAAM,KAAKA,MAAM,CAAC;EACxD;EAEA;EACAqB,mBAAmBA,CAACR,MAAqB;IACvC,MAAMS,OAAO,GAAGT,MAAM,CAAClC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACoB,MAAM,CAAC,CAACc,MAAM,CAACS,OAAO,CAAC;IACjE,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACF,OAAO,CAAC,CAAa;EAC1C;;;uCA3LWrE,mBAAmB,EAAAwE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnB3E,mBAAmB;MAAA4E,OAAA,EAAnB5E,mBAAmB,CAAA6E,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}