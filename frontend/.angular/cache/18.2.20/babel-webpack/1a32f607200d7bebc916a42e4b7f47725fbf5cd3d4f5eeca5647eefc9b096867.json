{"ast": null, "code": "import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function timeInterval(scheduler = asyncScheduler) {\n  return operate((source, subscriber) => {\n    let last = scheduler.now();\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const now = scheduler.now();\n      const interval = now - last;\n      last = now;\n      subscriber.next(new TimeInterval(value, interval));\n    }));\n  });\n}\nexport class TimeInterval {\n  constructor(value, interval) {\n    this.value = value;\n    this.interval = interval;\n  }\n}", "map": {"version": 3, "names": ["asyncScheduler", "operate", "createOperatorSubscriber", "timeInterval", "scheduler", "source", "subscriber", "last", "now", "subscribe", "value", "interval", "next", "TimeInterval", "constructor"], "sources": ["/var/www/html/trading-app/node_modules/rxjs/dist/esm/internal/operators/timeInterval.js"], "sourcesContent": ["import { asyncScheduler } from '../scheduler/async';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function timeInterval(scheduler = asyncScheduler) {\n    return operate((source, subscriber) => {\n        let last = scheduler.now();\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const now = scheduler.now();\n            const interval = now - last;\n            last = now;\n            subscriber.next(new TimeInterval(value, interval));\n        }));\n    });\n}\nexport class TimeInterval {\n    constructor(value, interval) {\n        this.value = value;\n        this.interval = interval;\n    }\n}\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,oBAAoB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,YAAYA,CAACC,SAAS,GAAGJ,cAAc,EAAE;EACrD,OAAOC,OAAO,CAAC,CAACI,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,IAAI,GAAGH,SAAS,CAACI,GAAG,CAAC,CAAC;IAC1BH,MAAM,CAACI,SAAS,CAACP,wBAAwB,CAACI,UAAU,EAAGI,KAAK,IAAK;MAC7D,MAAMF,GAAG,GAAGJ,SAAS,CAACI,GAAG,CAAC,CAAC;MAC3B,MAAMG,QAAQ,GAAGH,GAAG,GAAGD,IAAI;MAC3BA,IAAI,GAAGC,GAAG;MACVF,UAAU,CAACM,IAAI,CAAC,IAAIC,YAAY,CAACH,KAAK,EAAEC,QAAQ,CAAC,CAAC;IACtD,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN;AACA,OAAO,MAAME,YAAY,CAAC;EACtBC,WAAWA,CAACJ,KAAK,EAAEC,QAAQ,EAAE;IACzB,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}