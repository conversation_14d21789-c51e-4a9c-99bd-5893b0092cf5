{"ast": null, "code": "import { BehaviorSubject, tap } from 'rxjs';\nimport { environment } from '../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class AuthService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.API_URL = environment.apiUrl || 'http://localhost:3000';\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    // Check if user is already logged in\n    this.loadUserFromStorage();\n  }\n  register(registerData) {\n    return this.http.post(`${this.API_URL}/auth/register`, registerData).pipe(tap(response => this.handleAuthSuccess(response)));\n  }\n  login(loginData) {\n    return this.http.post(`${this.API_URL}/auth/login`, loginData).pipe(tap(response => this.handleAuthSuccess(response)));\n  }\n  logout() {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('current_user');\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/login']);\n  }\n  getProfile() {\n    return this.http.get(`${this.API_URL}/auth/profile`);\n  }\n  isAuthenticated() {\n    const token = this.getToken();\n    if (!token) return false;\n    // Check if token is expired\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      return payload.exp > currentTime;\n    } catch {\n      return false;\n    }\n  }\n  getToken() {\n    return localStorage.getItem('access_token');\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  isAdmin() {\n    const user = this.getCurrentUser();\n    return user?.role === 'admin';\n  }\n  handleAuthSuccess(response) {\n    localStorage.setItem('access_token', response.access_token);\n    localStorage.setItem('current_user', JSON.stringify(response.user));\n    this.currentUserSubject.next(response.user);\n    this.router.navigate(['/dashboard']);\n  }\n  loadUserFromStorage() {\n    const token = this.getToken();\n    const userStr = localStorage.getItem('current_user');\n    if (token && userStr && this.isAuthenticated()) {\n      try {\n        const user = JSON.parse(userStr);\n        this.currentUserSubject.next(user);\n      } catch {\n        this.logout();\n      }\n    } else {\n      this.logout();\n    }\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AuthService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "tap", "environment", "AuthService", "constructor", "http", "router", "API_URL", "apiUrl", "currentUserSubject", "currentUser$", "asObservable", "loadUserFromStorage", "register", "registerData", "post", "pipe", "response", "handleAuthSuccess", "login", "loginData", "logout", "localStorage", "removeItem", "next", "navigate", "getProfile", "get", "isAuthenticated", "token", "getToken", "payload", "JSON", "parse", "atob", "split", "currentTime", "Math", "floor", "Date", "now", "exp", "getItem", "getCurrentUser", "value", "isAdmin", "user", "role", "setItem", "access_token", "stringify", "userStr", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "factory", "ɵfac", "providedIn"], "sources": ["/var/www/html/trading-app/frontend/src/app/services/auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { BehaviorSubject, Observable, tap } from 'rxjs';\nimport { Router } from '@angular/router';\nimport { User, AuthResponse, LoginRequest, RegisterRequest } from '../models/user.model';\nimport { environment } from '../../environments/environment';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private readonly API_URL = environment.apiUrl || 'http://localhost:3000';\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  public currentUser$ = this.currentUserSubject.asObservable();\n\n  constructor(\n    private http: HttpClient,\n    private router: Router\n  ) {\n    // Check if user is already logged in\n    this.loadUserFromStorage();\n  }\n\n  register(registerData: RegisterRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/auth/register`, registerData)\n      .pipe(\n        tap((response: AuthResponse) => this.handleAuthSuccess(response))\n      );\n  }\n\n  login(loginData: LoginRequest): Observable<AuthResponse> {\n    return this.http.post<AuthResponse>(`${this.API_URL}/auth/login`, loginData)\n      .pipe(\n        tap((response: AuthResponse) => this.handleAuthSuccess(response))\n      );\n  }\n\n  logout(): void {\n    localStorage.removeItem('access_token');\n    localStorage.removeItem('current_user');\n    this.currentUserSubject.next(null);\n    this.router.navigate(['/login']);\n  }\n\n  getProfile(): Observable<User> {\n    return this.http.get<User>(`${this.API_URL}/auth/profile`);\n  }\n\n  isAuthenticated(): boolean {\n    const token = this.getToken();\n    if (!token) return false;\n    \n    // Check if token is expired\n    try {\n      const payload = JSON.parse(atob(token.split('.')[1]));\n      const currentTime = Math.floor(Date.now() / 1000);\n      return payload.exp > currentTime;\n    } catch {\n      return false;\n    }\n  }\n\n  getToken(): string | null {\n    return localStorage.getItem('access_token');\n  }\n\n  getCurrentUser(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  isAdmin(): boolean {\n    const user = this.getCurrentUser();\n    return user?.role === 'admin';\n  }\n\n  private handleAuthSuccess(response: AuthResponse): void {\n    localStorage.setItem('access_token', response.access_token);\n    localStorage.setItem('current_user', JSON.stringify(response.user));\n    this.currentUserSubject.next(response.user);\n    this.router.navigate(['/dashboard']);\n  }\n\n  private loadUserFromStorage(): void {\n    const token = this.getToken();\n    const userStr = localStorage.getItem('current_user');\n    \n    if (token && userStr && this.isAuthenticated()) {\n      try {\n        const user = JSON.parse(userStr);\n        this.currentUserSubject.next(user);\n      } catch {\n        this.logout();\n      }\n    } else {\n      this.logout();\n    }\n  }\n}\n"], "mappings": "AAEA,SAASA,eAAe,EAAcC,GAAG,QAAQ,MAAM;AAGvD,SAASC,WAAW,QAAQ,gCAAgC;;;;AAK5D,OAAM,MAAOC,WAAW;EAKtBC,YACUC,IAAgB,EAChBC,MAAc;IADd,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,MAAM,GAANA,MAAM;IANC,KAAAC,OAAO,GAAGL,WAAW,CAACM,MAAM,IAAI,uBAAuB;IAChE,KAAAC,kBAAkB,GAAG,IAAIT,eAAe,CAAc,IAAI,CAAC;IAC5D,KAAAU,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAM1D;IACA,IAAI,CAACC,mBAAmB,EAAE;EAC5B;EAEAC,QAAQA,CAACC,YAA6B;IACpC,OAAO,IAAI,CAACT,IAAI,CAACU,IAAI,CAAe,GAAG,IAAI,CAACR,OAAO,gBAAgB,EAAEO,YAAY,CAAC,CAC/EE,IAAI,CACHf,GAAG,CAAEgB,QAAsB,IAAK,IAAI,CAACC,iBAAiB,CAACD,QAAQ,CAAC,CAAC,CAClE;EACL;EAEAE,KAAKA,CAACC,SAAuB;IAC3B,OAAO,IAAI,CAACf,IAAI,CAACU,IAAI,CAAe,GAAG,IAAI,CAACR,OAAO,aAAa,EAAEa,SAAS,CAAC,CACzEJ,IAAI,CACHf,GAAG,CAAEgB,QAAsB,IAAK,IAAI,CAACC,iBAAiB,CAACD,QAAQ,CAAC,CAAC,CAClE;EACL;EAEAI,MAAMA,CAAA;IACJC,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;IACvCD,YAAY,CAACC,UAAU,CAAC,cAAc,CAAC;IACvC,IAAI,CAACd,kBAAkB,CAACe,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAAClB,MAAM,CAACmB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;EAClC;EAEAC,UAAUA,CAAA;IACR,OAAO,IAAI,CAACrB,IAAI,CAACsB,GAAG,CAAO,GAAG,IAAI,CAACpB,OAAO,eAAe,CAAC;EAC5D;EAEAqB,eAAeA,CAAA;IACb,MAAMC,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,IAAI,CAACD,KAAK,EAAE,OAAO,KAAK;IAExB;IACA,IAAI;MACF,MAAME,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACL,KAAK,CAACM,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrD,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAAC;MACjD,OAAOT,OAAO,CAACU,GAAG,GAAGL,WAAW;IAClC,CAAC,CAAC,MAAM;MACN,OAAO,KAAK;IACd;EACF;EAEAN,QAAQA,CAAA;IACN,OAAOR,YAAY,CAACoB,OAAO,CAAC,cAAc,CAAC;EAC7C;EAEAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClC,kBAAkB,CAACmC,KAAK;EACtC;EAEAC,OAAOA,CAAA;IACL,MAAMC,IAAI,GAAG,IAAI,CAACH,cAAc,EAAE;IAClC,OAAOG,IAAI,EAAEC,IAAI,KAAK,OAAO;EAC/B;EAEQ7B,iBAAiBA,CAACD,QAAsB;IAC9CK,YAAY,CAAC0B,OAAO,CAAC,cAAc,EAAE/B,QAAQ,CAACgC,YAAY,CAAC;IAC3D3B,YAAY,CAAC0B,OAAO,CAAC,cAAc,EAAEhB,IAAI,CAACkB,SAAS,CAACjC,QAAQ,CAAC6B,IAAI,CAAC,CAAC;IACnE,IAAI,CAACrC,kBAAkB,CAACe,IAAI,CAACP,QAAQ,CAAC6B,IAAI,CAAC;IAC3C,IAAI,CAACxC,MAAM,CAACmB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;EACtC;EAEQb,mBAAmBA,CAAA;IACzB,MAAMiB,KAAK,GAAG,IAAI,CAACC,QAAQ,EAAE;IAC7B,MAAMqB,OAAO,GAAG7B,YAAY,CAACoB,OAAO,CAAC,cAAc,CAAC;IAEpD,IAAIb,KAAK,IAAIsB,OAAO,IAAI,IAAI,CAACvB,eAAe,EAAE,EAAE;MAC9C,IAAI;QACF,MAAMkB,IAAI,GAAGd,IAAI,CAACC,KAAK,CAACkB,OAAO,CAAC;QAChC,IAAI,CAAC1C,kBAAkB,CAACe,IAAI,CAACsB,IAAI,CAAC;MACpC,CAAC,CAAC,MAAM;QACN,IAAI,CAACzB,MAAM,EAAE;MACf;IACF,CAAC,MAAM;MACL,IAAI,CAACA,MAAM,EAAE;IACf;EACF;;;uCAtFWlB,WAAW,EAAAiD,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAXtD,WAAW;MAAAuD,OAAA,EAAXvD,WAAW,CAAAwD,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}