{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport class AdminComponent {\n  static {\n    this.ɵfac = function AdminComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AdminComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminComponent,\n      selectors: [[\"app-admin\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 0,\n      consts: [[1, \"space-y-6\"], [1, \"card\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-900\", \"mb-4\"], [1, \"text-gray-600\"]],\n      template: function AdminComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"Admin Panel\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 3);\n          i0.ɵɵtext(5, \"Admin functionality will be implemented here.\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [CommonModule],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "AdminComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AdminComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "encapsulation"], "sources": ["/var/www/html/trading-app/frontend/src/app/pages/admin/admin.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-admin',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"space-y-6\">\n      <div class=\"card\">\n        <h1 class=\"text-2xl font-bold text-gray-900 mb-4\">Admin Panel</h1>\n        <p class=\"text-gray-600\">Admin functionality will be implemented here.</p>\n      </div>\n    </div>\n  `\n})\nexport class AdminComponent {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAe9C,OAAM,MAAOC,cAAc;;;uCAAdA,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UANnBP,EAFJ,CAAAS,cAAA,aAAuB,aACH,YACkC;UAAAT,EAAA,CAAAU,MAAA,kBAAW;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAClEX,EAAA,CAAAS,cAAA,WAAyB;UAAAT,EAAA,CAAAU,MAAA,oDAA6C;UAE1EV,EAF0E,CAAAW,YAAA,EAAI,EACtE,EACF;;;qBAPEhB,YAAY;MAAAiB,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}