{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule } from '@angular/router';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"@angular/router\";\nfunction AppComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵelement(1, \"app-sidebar\");\n    i0.ɵɵelementStart(2, \"main\", 2);\n    i0.ɵɵelement(3, \"router-outlet\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction AppComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"nav\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"div\", 6)(5, \"h1\", 7);\n    i0.ɵɵtext(6, \"Trading App\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 8)(8, \"a\", 9);\n    i0.ɵɵtext(9, \"Login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"a\", 10);\n    i0.ɵɵtext(11, \"Register\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(12, \"main\", 11);\n    i0.ɵɵelement(13, \"router-outlet\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AppComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 1,\n      consts: [[1, \"min-h-screen\", \"bg-gray-50\"], [1, \"flex\"], [1, \"flex-1\", \"ml-64\", \"p-6\"], [1, \"bg-white\", \"shadow-sm\", \"border-b\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\"], [1, \"flex\", \"justify-between\", \"h-16\"], [1, \"flex\", \"items-center\"], [1, \"text-xl\", \"font-bold\", \"text-gray-900\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [\"routerLink\", \"/login\", 1, \"btn\", \"btn-outline\"], [\"routerLink\", \"/register\", 1, \"btn\", \"btn-primary\"], [1, \"max-w-7xl\", \"mx-auto\", \"py-6\", \"sm:px-6\", \"lg:px-8\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtemplate(1, AppComponent_Conditional_1_Template, 4, 0, \"div\", 1)(2, AppComponent_Conditional_2_Template, 14, 0, \"div\", 0);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.authService.isAuthenticated() ? 1 : 2);\n        }\n      },\n      dependencies: [CommonModule, RouterOutlet, RouterModule, i2.RouterLink, SidebarComponent],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "RouterModule", "SidebarComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "AppComponent", "constructor", "authService", "router", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵtemplate", "AppComponent_Conditional_1_Template", "AppComponent_Conditional_2_Template", "ɵɵadvance", "ɵɵconditional", "isAuthenticated", "RouterLink", "encapsulation"], "sources": ["/var/www/html/trading-app/frontend/src/app/app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule, Router } from '@angular/router';\nimport { AuthService } from './services/auth.service';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet, RouterModule, SidebarComponent],\n  template: `\n    <div class=\"min-h-screen bg-gray-50\">\n      @if (authService.isAuthenticated()) {\n        <!-- Authenticated Layout with Sidebar -->\n        <div class=\"flex\">\n          <app-sidebar></app-sidebar>\n          <main class=\"flex-1 ml-64 p-6\">\n            <router-outlet></router-outlet>\n          </main>\n        </div>\n      } @else {\n        <!-- Unauthenticated Layout -->\n        <div class=\"min-h-screen bg-gray-50\">\n          <!-- Navigation -->\n          <nav class=\"bg-white shadow-sm border-b\">\n            <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              <div class=\"flex justify-between h-16\">\n                <div class=\"flex items-center\">\n                  <h1 class=\"text-xl font-bold text-gray-900\">Trading App</h1>\n                </div>\n                <div class=\"flex items-center space-x-4\">\n                  <a routerLink=\"/login\" class=\"btn btn-outline\">Login</a>\n                  <a routerLink=\"/register\" class=\"btn btn-primary\">Register</a>\n                </div>\n              </div>\n            </div>\n          </nav>\n\n          <!-- Main Content -->\n          <main class=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n            <router-outlet></router-outlet>\n          </main>\n        </div>\n      }\n    </div>\n  `\n})\nexport class AppComponent {\n  constructor(\n    public authService: AuthService,\n    private router: Router\n  ) {}\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAEC,YAAY,QAAgB,iBAAiB;AAEpE,SAASC,gBAAgB,QAAQ,wCAAwC;;;;;;IAUjEC,EAAA,CAAAC,cAAA,aAAkB;IAChBD,EAAA,CAAAE,SAAA,kBAA2B;IAC3BF,EAAA,CAAAC,cAAA,cAA+B;IAC7BD,EAAA,CAAAE,SAAA,oBAA+B;IAEnCF,EADE,CAAAG,YAAA,EAAO,EACH;;;;;IASIH,EANV,CAAAC,cAAA,aAAqC,aAEM,aACa,aACX,aACN,YACe;IAAAD,EAAA,CAAAI,MAAA,kBAAW;IACzDJ,EADyD,CAAAG,YAAA,EAAK,EACxD;IAEJH,EADF,CAAAC,cAAA,aAAyC,WACQ;IAAAD,EAAA,CAAAI,MAAA,YAAK;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACxDH,EAAA,CAAAC,cAAA,aAAkD;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAIlEJ,EAJkE,CAAAG,YAAA,EAAI,EAC1D,EACF,EACF,EACF;IAGNH,EAAA,CAAAC,cAAA,gBAAqD;IACnDD,EAAA,CAAAE,SAAA,qBAA+B;IAEnCF,EADE,CAAAG,YAAA,EAAO,EACH;;;AAKd,OAAM,MAAOE,YAAY;EACvBC,YACSC,WAAwB,EACvBC,MAAc;IADf,KAAAD,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;EACb;;;uCAJQH,YAAY,EAAAL,EAAA,CAAAS,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAX,EAAA,CAAAS,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZR,YAAY;MAAAS,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhB,EAAA,CAAAiB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApCrBvB,EAAA,CAAAC,cAAA,aAAqC;UASjCD,EARF,CAAAyB,UAAA,IAAAC,mCAAA,iBAAqC,IAAAC,mCAAA,kBAQ5B;UAwBX3B,EAAA,CAAAG,YAAA,EAAM;;;UAhCJH,EAAA,CAAA4B,SAAA,EA+BC;UA/BD5B,EAAA,CAAA6B,aAAA,CAAAL,GAAA,CAAAjB,WAAA,CAAAuB,eAAA,WA+BC;;;qBAlCKlC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAAc,EAAA,CAAAmB,UAAA,EAAEhC,gBAAgB;MAAAiC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}