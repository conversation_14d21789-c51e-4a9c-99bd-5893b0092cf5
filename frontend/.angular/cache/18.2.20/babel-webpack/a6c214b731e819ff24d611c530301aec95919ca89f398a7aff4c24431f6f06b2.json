{"ast": null, "code": "import { isFunction } from './isFunction';\nimport { isScheduler } from './isScheduler';\nfunction last(arr) {\n  return arr[arr.length - 1];\n}\nexport function popResultSelector(args) {\n  return isFunction(last(args)) ? args.pop() : undefined;\n}\nexport function popScheduler(args) {\n  return isScheduler(last(args)) ? args.pop() : undefined;\n}\nexport function popNumber(args, defaultValue) {\n  return typeof last(args) === 'number' ? args.pop() : defaultValue;\n}", "map": {"version": 3, "names": ["isFunction", "isScheduler", "last", "arr", "length", "popResultSelector", "args", "pop", "undefined", "popScheduler", "popNumber", "defaultValue"], "sources": ["/var/www/html/trading-app/frontend/node_modules/rxjs/dist/esm/internal/util/args.js"], "sourcesContent": ["import { isFunction } from './isFunction';\nimport { isScheduler } from './isScheduler';\nfunction last(arr) {\n    return arr[arr.length - 1];\n}\nexport function popResultSelector(args) {\n    return isFunction(last(args)) ? args.pop() : undefined;\n}\nexport function popScheduler(args) {\n    return isScheduler(last(args)) ? args.pop() : undefined;\n}\nexport function popNumber(args, defaultValue) {\n    return typeof last(args) === 'number' ? args.pop() : defaultValue;\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,IAAIA,CAACC,GAAG,EAAE;EACf,OAAOA,GAAG,CAACA,GAAG,CAACC,MAAM,GAAG,CAAC,CAAC;AAC9B;AACA,OAAO,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EACpC,OAAON,UAAU,CAACE,IAAI,CAACI,IAAI,CAAC,CAAC,GAAGA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,SAAS;AAC1D;AACA,OAAO,SAASC,YAAYA,CAACH,IAAI,EAAE;EAC/B,OAAOL,WAAW,CAACC,IAAI,CAACI,IAAI,CAAC,CAAC,GAAGA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,SAAS;AAC3D;AACA,OAAO,SAASE,SAASA,CAACJ,IAAI,EAAEK,YAAY,EAAE;EAC1C,OAAO,OAAOT,IAAI,CAACI,IAAI,CAAC,KAAK,QAAQ,GAAGA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGI,YAAY;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}