{"ast": null, "code": "import _asyncToGenerator from \"/var/www/html/trading-app/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class IndianStocksService {\n  constructor(http) {\n    this.http = http;\n    // Using NSE India API for real stock data\n    this.NSE_API_BASE = 'https://www.nseindia.com/api';\n    this.CORS_PROXY = 'https://api.allorigins.win/raw?url=';\n    // Alternative APIs for stock data\n    this.ALPHA_VANTAGE_API = 'https://www.alphavantage.co/query';\n    this.FINNHUB_API = 'https://finnhub.io/api/v1';\n    // Sector mapping for Indian stocks\n    this.SECTOR_MAPPING = {\n      'RELIANCE': 'Energy',\n      'TCS': 'Technology',\n      'HDFCBANK': 'Banking',\n      'INFY': 'Technology',\n      'HINDUNILVR': 'Consumer Goods',\n      'ICICIBANK': 'Banking',\n      'KOTAKBANK': 'Banking',\n      'BHARTIARTL': 'Telecommunications',\n      'ITC': 'Consumer Goods',\n      'SBIN': 'Banking',\n      'LT': 'Construction',\n      'HCLTECH': 'Technology',\n      'ASIANPAINT': 'Chemicals',\n      'MARUTI': 'Automotive',\n      'BAJFINANCE': 'Financial Services',\n      'WIPRO': 'Technology',\n      'ULTRACEMCO': 'Cement',\n      'AXISBANK': 'Banking',\n      'TITAN': 'Consumer Goods',\n      'SUNPHARMA': 'Pharmaceuticals'\n    };\n  }\n  getIndianStocks() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // For demo purposes, we'll create mock data with realistic Indian stock information\n      // In a real application, you would fetch from a proper API\n      return _this.getMockIndianStocks();\n    })();\n  }\n  getMockIndianStocks() {\n    return this.POPULAR_INDIAN_STOCKS.map(stock => {\n      const basePrice = Math.random() * 3000 + 100; // Random price between 100-3100\n      const change = (Math.random() - 0.5) * 100; // Random change between -50 to +50\n      const changePercent = change / basePrice * 100;\n      return {\n        symbol: stock.symbol.replace('.BSE', ''),\n        name: stock.name,\n        price: Math.round(basePrice * 100) / 100,\n        change: Math.round(change * 100) / 100,\n        changePercent: Math.round(changePercent * 100) / 100,\n        volume: Math.floor(Math.random() * 10000000) + 100000,\n        // Random volume\n        sector: stock.sector,\n        marketCap: Math.floor(Math.random() * 500000) + 10000 // Random market cap in crores\n      };\n    });\n  }\n  // Method to get real-time data (would require API key in production)\n  getRealTimePrice(symbol) {\n    return _asyncToGenerator(function* () {\n      try {\n        // This would require an API key in production\n        // const response = await this.http.get(`${this.API_BASE}/price?symbol=${symbol}&apikey=YOUR_API_KEY`).toPromise();\n        // return response;\n        // For demo, return mock data\n        return {\n          price: (Math.random() * 3000 + 100).toFixed(2),\n          change: ((Math.random() - 0.5) * 100).toFixed(2)\n        };\n      } catch (error) {\n        console.error('Error fetching real-time price:', error);\n        return null;\n      }\n    })();\n  }\n  // Get top gainers\n  getTopGainers(stocks) {\n    return stocks.filter(stock => stock.changePercent > 0).sort((a, b) => b.changePercent - a.changePercent).slice(0, 5);\n  }\n  // Get top losers\n  getTopLosers(stocks) {\n    return stocks.filter(stock => stock.changePercent < 0).sort((a, b) => a.changePercent - b.changePercent).slice(0, 5);\n  }\n  // Get stocks by sector\n  getStocksBySector(stocks, sector) {\n    return stocks.filter(stock => stock.sector === sector);\n  }\n  // Get available sectors\n  getAvailableSectors(stocks) {\n    const sectors = stocks.map(stock => stock.sector).filter(Boolean);\n    return [...new Set(sectors)];\n  }\n  static {\n    this.ɵfac = function IndianStocksService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IndianStocksService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: IndianStocksService,\n      factory: IndianStocksService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["IndianStocksService", "constructor", "http", "NSE_API_BASE", "CORS_PROXY", "ALPHA_VANTAGE_API", "FINNHUB_API", "SECTOR_MAPPING", "getIndianStocks", "_this", "_asyncToGenerator", "getMockIndianStocks", "POPULAR_INDIAN_STOCKS", "map", "stock", "basePrice", "Math", "random", "change", "changePercent", "symbol", "replace", "name", "price", "round", "volume", "floor", "sector", "marketCap", "getRealTimePrice", "toFixed", "error", "console", "getTopGainers", "stocks", "filter", "sort", "a", "b", "slice", "getTopLosers", "getStocksBySector", "getAvailableSectors", "sectors", "Boolean", "Set", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/var/www/html/trading-app/frontend/src/app/services/indian-stocks.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\n\nexport interface IndianStock {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  marketCap?: number;\n  sector?: string;\n  high?: number;\n  low?: number;\n  open?: number;\n  previousClose?: number;\n}\n\nexport interface NSEStock {\n  symbol: string;\n  companyName: string;\n  lastPrice: number;\n  change: number;\n  pChange: number;\n  totalTradedVolume: number;\n  totalTradedValue: number;\n  dayHigh: number;\n  dayLow: number;\n  open: number;\n  previousClose: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class IndianStocksService {\n  // Using NSE India API for real stock data\n  private readonly NSE_API_BASE = 'https://www.nseindia.com/api';\n  private readonly CORS_PROXY = 'https://api.allorigins.win/raw?url=';\n\n  // Alternative APIs for stock data\n  private readonly ALPHA_VANTAGE_API = 'https://www.alphavantage.co/query';\n  private readonly FINNHUB_API = 'https://finnhub.io/api/v1';\n\n  // Sector mapping for Indian stocks\n  private readonly SECTOR_MAPPING: { [key: string]: string } = {\n    'RELIANCE': 'Energy',\n    'TCS': 'Technology',\n    'HDFCBANK': 'Banking',\n    'INFY': 'Technology',\n    'HINDUNILVR': 'Consumer Goods',\n    'ICICIBANK': 'Banking',\n    'KOTAKBANK': 'Banking',\n    'BHARTIARTL': 'Telecommunications',\n    'ITC': 'Consumer Goods',\n    'SBIN': 'Banking',\n    'LT': 'Construction',\n    'HCLTECH': 'Technology',\n    'ASIANPAINT': 'Chemicals',\n    'MARUTI': 'Automotive',\n    'BAJFINANCE': 'Financial Services',\n    'WIPRO': 'Technology',\n    'ULTRACEMCO': 'Cement',\n    'AXISBANK': 'Banking',\n    'TITAN': 'Consumer Goods',\n    'SUNPHARMA': 'Pharmaceuticals'\n  };\n\n  constructor(private http: HttpClient) {}\n\n  async getIndianStocks(): Promise<IndianStock[]> {\n    // For demo purposes, we'll create mock data with realistic Indian stock information\n    // In a real application, you would fetch from a proper API\n    return this.getMockIndianStocks();\n  }\n\n  private getMockIndianStocks(): IndianStock[] {\n    return this.POPULAR_INDIAN_STOCKS.map(stock => {\n      const basePrice = Math.random() * 3000 + 100; // Random price between 100-3100\n      const change = (Math.random() - 0.5) * 100; // Random change between -50 to +50\n      const changePercent = (change / basePrice) * 100;\n      \n      return {\n        symbol: stock.symbol.replace('.BSE', ''),\n        name: stock.name,\n        price: Math.round(basePrice * 100) / 100,\n        change: Math.round(change * 100) / 100,\n        changePercent: Math.round(changePercent * 100) / 100,\n        volume: Math.floor(Math.random() * 10000000) + 100000, // Random volume\n        sector: stock.sector,\n        marketCap: Math.floor(Math.random() * 500000) + 10000 // Random market cap in crores\n      };\n    });\n  }\n\n  // Method to get real-time data (would require API key in production)\n  async getRealTimePrice(symbol: string): Promise<any> {\n    try {\n      // This would require an API key in production\n      // const response = await this.http.get(`${this.API_BASE}/price?symbol=${symbol}&apikey=YOUR_API_KEY`).toPromise();\n      // return response;\n      \n      // For demo, return mock data\n      return {\n        price: (Math.random() * 3000 + 100).toFixed(2),\n        change: ((Math.random() - 0.5) * 100).toFixed(2)\n      };\n    } catch (error) {\n      console.error('Error fetching real-time price:', error);\n      return null;\n    }\n  }\n\n  // Get top gainers\n  getTopGainers(stocks: IndianStock[]): IndianStock[] {\n    return stocks\n      .filter(stock => stock.changePercent > 0)\n      .sort((a, b) => b.changePercent - a.changePercent)\n      .slice(0, 5);\n  }\n\n  // Get top losers\n  getTopLosers(stocks: IndianStock[]): IndianStock[] {\n    return stocks\n      .filter(stock => stock.changePercent < 0)\n      .sort((a, b) => a.changePercent - b.changePercent)\n      .slice(0, 5);\n  }\n\n  // Get stocks by sector\n  getStocksBySector(stocks: IndianStock[], sector: string): IndianStock[] {\n    return stocks.filter(stock => stock.sector === sector);\n  }\n\n  // Get available sectors\n  getAvailableSectors(stocks: IndianStock[]): string[] {\n    const sectors = stocks.map(stock => stock.sector).filter(Boolean);\n    return [...new Set(sectors)] as string[];\n  }\n}\n"], "mappings": ";;;AAmCA,OAAM,MAAOA,mBAAmB;EAiC9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAhCxB;IACiB,KAAAC,YAAY,GAAG,8BAA8B;IAC7C,KAAAC,UAAU,GAAG,qCAAqC;IAEnE;IACiB,KAAAC,iBAAiB,GAAG,mCAAmC;IACvD,KAAAC,WAAW,GAAG,2BAA2B;IAE1D;IACiB,KAAAC,cAAc,GAA8B;MAC3D,UAAU,EAAE,QAAQ;MACpB,KAAK,EAAE,YAAY;MACnB,UAAU,EAAE,SAAS;MACrB,MAAM,EAAE,YAAY;MACpB,YAAY,EAAE,gBAAgB;MAC9B,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE,SAAS;MACtB,YAAY,EAAE,oBAAoB;MAClC,KAAK,EAAE,gBAAgB;MACvB,MAAM,EAAE,SAAS;MACjB,IAAI,EAAE,cAAc;MACpB,SAAS,EAAE,YAAY;MACvB,YAAY,EAAE,WAAW;MACzB,QAAQ,EAAE,YAAY;MACtB,YAAY,EAAE,oBAAoB;MAClC,OAAO,EAAE,YAAY;MACrB,YAAY,EAAE,QAAQ;MACtB,UAAU,EAAE,SAAS;MACrB,OAAO,EAAE,gBAAgB;MACzB,WAAW,EAAE;KACd;EAEsC;EAEjCC,eAAeA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnB;MACA;MACA,OAAOD,KAAI,CAACE,mBAAmB,EAAE;IAAC;EACpC;EAEQA,mBAAmBA,CAAA;IACzB,OAAO,IAAI,CAACC,qBAAqB,CAACC,GAAG,CAACC,KAAK,IAAG;MAC5C,MAAMC,SAAS,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;MAC9C,MAAMC,MAAM,GAAG,CAACF,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;MAC5C,MAAME,aAAa,GAAID,MAAM,GAAGH,SAAS,GAAI,GAAG;MAEhD,OAAO;QACLK,MAAM,EAAEN,KAAK,CAACM,MAAM,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QACxCC,IAAI,EAAER,KAAK,CAACQ,IAAI;QAChBC,KAAK,EAAEP,IAAI,CAACQ,KAAK,CAACT,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG;QACxCG,MAAM,EAAEF,IAAI,CAACQ,KAAK,CAACN,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;QACtCC,aAAa,EAAEH,IAAI,CAACQ,KAAK,CAACL,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG;QACpDM,MAAM,EAAET,IAAI,CAACU,KAAK,CAACV,IAAI,CAACC,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,MAAM;QAAE;QACvDU,MAAM,EAAEb,KAAK,CAACa,MAAM;QACpBC,SAAS,EAAEZ,IAAI,CAACU,KAAK,CAACV,IAAI,CAACC,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC;OACvD;IACH,CAAC,CAAC;EACJ;EAEA;EACMY,gBAAgBA,CAACT,MAAc;IAAA,OAAAV,iBAAA;MACnC,IAAI;QACF;QACA;QACA;QAEA;QACA,OAAO;UACLa,KAAK,EAAE,CAACP,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,EAAEa,OAAO,CAAC,CAAC,CAAC;UAC9CZ,MAAM,EAAE,CAAC,CAACF,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,EAAEa,OAAO,CAAC,CAAC;SAChD;MACH,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,OAAO,IAAI;MACb;IAAC;EACH;EAEA;EACAE,aAAaA,CAACC,MAAqB;IACjC,OAAOA,MAAM,CACVC,MAAM,CAACrB,KAAK,IAAIA,KAAK,CAACK,aAAa,GAAG,CAAC,CAAC,CACxCiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACnB,aAAa,GAAGkB,CAAC,CAAClB,aAAa,CAAC,CACjDoB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA;EACAC,YAAYA,CAACN,MAAqB;IAChC,OAAOA,MAAM,CACVC,MAAM,CAACrB,KAAK,IAAIA,KAAK,CAACK,aAAa,GAAG,CAAC,CAAC,CACxCiB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAClB,aAAa,GAAGmB,CAAC,CAACnB,aAAa,CAAC,CACjDoB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA;EACAE,iBAAiBA,CAACP,MAAqB,EAAEP,MAAc;IACrD,OAAOO,MAAM,CAACC,MAAM,CAACrB,KAAK,IAAIA,KAAK,CAACa,MAAM,KAAKA,MAAM,CAAC;EACxD;EAEA;EACAe,mBAAmBA,CAACR,MAAqB;IACvC,MAAMS,OAAO,GAAGT,MAAM,CAACrB,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACa,MAAM,CAAC,CAACQ,MAAM,CAACS,OAAO,CAAC;IACjE,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACF,OAAO,CAAC,CAAa;EAC1C;;;uCAvGW3C,mBAAmB,EAAA8C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnBjD,mBAAmB;MAAAkD,OAAA,EAAnBlD,mBAAmB,CAAAmD,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}