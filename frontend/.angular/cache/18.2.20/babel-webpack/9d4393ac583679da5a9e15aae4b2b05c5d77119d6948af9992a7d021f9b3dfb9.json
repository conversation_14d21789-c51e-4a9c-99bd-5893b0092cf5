{"ast": null, "code": "import _asyncToGenerator from \"/var/www/html/trading-app/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class IndianStocksService {\n  constructor(http) {\n    this.http = http;\n    // Using NSE India API for real stock data\n    this.NSE_API_BASE = 'https://www.nseindia.com/api';\n    this.CORS_PROXY = 'https://api.allorigins.win/raw?url=';\n    // Alternative APIs for stock data\n    this.ALPHA_VANTAGE_API = 'https://www.alphavantage.co/query';\n    this.FINNHUB_API = 'https://finnhub.io/api/v1';\n    // Sector mapping for Indian stocks\n    this.SECTOR_MAPPING = {\n      'RELIANCE': 'Energy',\n      'TCS': 'Technology',\n      'HDFCBANK': 'Banking',\n      'INFY': 'Technology',\n      'HINDUNILVR': 'Consumer Goods',\n      'ICICIBANK': 'Banking',\n      'KOTAKBANK': 'Banking',\n      'BHARTIARTL': 'Telecommunications',\n      'ITC': 'Consumer Goods',\n      'SBIN': 'Banking',\n      'LT': 'Construction',\n      'HCLTECH': 'Technology',\n      'ASIANPAINT': 'Chemicals',\n      'MARUTI': 'Automotive',\n      'BAJFINANCE': 'Financial Services',\n      'WIPRO': 'Technology',\n      'ULTRACEMCO': 'Cement',\n      'AXISBANK': 'Banking',\n      'TITAN': 'Consumer Goods',\n      'SUNPHARMA': 'Pharmaceuticals'\n    };\n  }\n  getIndianStocks() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Try to fetch real data from NSE API\n        const realData = yield _this.fetchNSEStocks();\n        if (realData && realData.length > 0) {\n          return realData;\n        }\n      } catch (error) {\n        console.warn('Failed to fetch real NSE data, falling back to mock data:', error);\n      }\n      // Fallback to enhanced mock data\n      return _this.getEnhancedMockData();\n    })();\n  }\n  fetchNSEStocks() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Fetch NIFTY 50 stocks from NSE\n        const nifty50Url = `${_this2.NSE_API_BASE}/equity-stockIndices?index=NIFTY%2050`;\n        const proxyUrl = `${_this2.CORS_PROXY}${encodeURIComponent(nifty50Url)}`;\n        const response = yield _this2.http.get(proxyUrl).toPromise();\n        if (response && response.data) {\n          return response.data.map(stock => _this2.mapNSEToIndianStock(stock));\n        }\n        return [];\n      } catch (error) {\n        console.error('Error fetching NSE data:', error);\n        throw error;\n      }\n    })();\n  }\n  mapNSEToIndianStock(nseStock) {\n    return {\n      symbol: nseStock.symbol,\n      name: nseStock.symbol,\n      // NSE API doesn't always provide full company name\n      price: parseFloat(nseStock.lastPrice) || 0,\n      change: parseFloat(nseStock.change) || 0,\n      changePercent: parseFloat(nseStock.pChange) || 0,\n      volume: parseInt(nseStock.totalTradedVolume) || 0,\n      high: parseFloat(nseStock.dayHigh) || 0,\n      low: parseFloat(nseStock.dayLow) || 0,\n      open: parseFloat(nseStock.open) || 0,\n      previousClose: parseFloat(nseStock.previousClose) || 0,\n      sector: this.SECTOR_MAPPING[nseStock.symbol] || 'Others'\n    };\n  }\n  getMockIndianStocks() {\n    return this.POPULAR_INDIAN_STOCKS.map(stock => {\n      const basePrice = Math.random() * 3000 + 100; // Random price between 100-3100\n      const change = (Math.random() - 0.5) * 100; // Random change between -50 to +50\n      const changePercent = change / basePrice * 100;\n      return {\n        symbol: stock.symbol.replace('.BSE', ''),\n        name: stock.name,\n        price: Math.round(basePrice * 100) / 100,\n        change: Math.round(change * 100) / 100,\n        changePercent: Math.round(changePercent * 100) / 100,\n        volume: Math.floor(Math.random() * 10000000) + 100000,\n        // Random volume\n        sector: stock.sector,\n        marketCap: Math.floor(Math.random() * 500000) + 10000 // Random market cap in crores\n      };\n    });\n  }\n  // Method to get real-time data (would require API key in production)\n  getRealTimePrice(symbol) {\n    return _asyncToGenerator(function* () {\n      try {\n        // This would require an API key in production\n        // const response = await this.http.get(`${this.API_BASE}/price?symbol=${symbol}&apikey=YOUR_API_KEY`).toPromise();\n        // return response;\n        // For demo, return mock data\n        return {\n          price: (Math.random() * 3000 + 100).toFixed(2),\n          change: ((Math.random() - 0.5) * 100).toFixed(2)\n        };\n      } catch (error) {\n        console.error('Error fetching real-time price:', error);\n        return null;\n      }\n    })();\n  }\n  // Get top gainers\n  getTopGainers(stocks) {\n    return stocks.filter(stock => stock.changePercent > 0).sort((a, b) => b.changePercent - a.changePercent).slice(0, 5);\n  }\n  // Get top losers\n  getTopLosers(stocks) {\n    return stocks.filter(stock => stock.changePercent < 0).sort((a, b) => a.changePercent - b.changePercent).slice(0, 5);\n  }\n  // Get stocks by sector\n  getStocksBySector(stocks, sector) {\n    return stocks.filter(stock => stock.sector === sector);\n  }\n  // Get available sectors\n  getAvailableSectors(stocks) {\n    const sectors = stocks.map(stock => stock.sector).filter(Boolean);\n    return [...new Set(sectors)];\n  }\n  static {\n    this.ɵfac = function IndianStocksService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IndianStocksService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: IndianStocksService,\n      factory: IndianStocksService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["IndianStocksService", "constructor", "http", "NSE_API_BASE", "CORS_PROXY", "ALPHA_VANTAGE_API", "FINNHUB_API", "SECTOR_MAPPING", "getIndianStocks", "_this", "_asyncToGenerator", "realData", "fetchNSEStocks", "length", "error", "console", "warn", "getEnhancedMockData", "_this2", "nifty50Url", "proxyUrl", "encodeURIComponent", "response", "get", "to<PERSON>romise", "data", "map", "stock", "mapNSEToIndianStock", "nseStock", "symbol", "name", "price", "parseFloat", "lastPrice", "change", "changePercent", "pChange", "volume", "parseInt", "totalTradedVolume", "high", "dayHigh", "low", "dayLow", "open", "previousClose", "sector", "getMockIndianStocks", "POPULAR_INDIAN_STOCKS", "basePrice", "Math", "random", "replace", "round", "floor", "marketCap", "getRealTimePrice", "toFixed", "getTopGainers", "stocks", "filter", "sort", "a", "b", "slice", "getTopLosers", "getStocksBySector", "getAvailableSectors", "sectors", "Boolean", "Set", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/var/www/html/trading-app/frontend/src/app/services/indian-stocks.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\n\nexport interface IndianStock {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  marketCap?: number;\n  sector?: string;\n  high?: number;\n  low?: number;\n  open?: number;\n  previousClose?: number;\n}\n\nexport interface NSEStock {\n  symbol: string;\n  companyName: string;\n  lastPrice: number;\n  change: number;\n  pChange: number;\n  totalTradedVolume: number;\n  totalTradedValue: number;\n  dayHigh: number;\n  dayLow: number;\n  open: number;\n  previousClose: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class IndianStocksService {\n  // Using NSE India API for real stock data\n  private readonly NSE_API_BASE = 'https://www.nseindia.com/api';\n  private readonly CORS_PROXY = 'https://api.allorigins.win/raw?url=';\n\n  // Alternative APIs for stock data\n  private readonly ALPHA_VANTAGE_API = 'https://www.alphavantage.co/query';\n  private readonly FINNHUB_API = 'https://finnhub.io/api/v1';\n\n  // Sector mapping for Indian stocks\n  private readonly SECTOR_MAPPING: { [key: string]: string } = {\n    'RELIANCE': 'Energy',\n    'TCS': 'Technology',\n    'HDFCBANK': 'Banking',\n    'INFY': 'Technology',\n    'HINDUNILVR': 'Consumer Goods',\n    'ICICIBANK': 'Banking',\n    'KOTAKBANK': 'Banking',\n    'BHARTIARTL': 'Telecommunications',\n    'ITC': 'Consumer Goods',\n    'SBIN': 'Banking',\n    'LT': 'Construction',\n    'HCLTECH': 'Technology',\n    'ASIANPAINT': 'Chemicals',\n    'MARUTI': 'Automotive',\n    'BAJFINANCE': 'Financial Services',\n    'WIPRO': 'Technology',\n    'ULTRACEMCO': 'Cement',\n    'AXISBANK': 'Banking',\n    'TITAN': 'Consumer Goods',\n    'SUNPHARMA': 'Pharmaceuticals'\n  };\n\n  constructor(private http: HttpClient) {}\n\n  async getIndianStocks(): Promise<IndianStock[]> {\n    try {\n      // Try to fetch real data from NSE API\n      const realData = await this.fetchNSEStocks();\n      if (realData && realData.length > 0) {\n        return realData;\n      }\n    } catch (error) {\n      console.warn('Failed to fetch real NSE data, falling back to mock data:', error);\n    }\n\n    // Fallback to enhanced mock data\n    return this.getEnhancedMockData();\n  }\n\n  private async fetchNSEStocks(): Promise<IndianStock[]> {\n    try {\n      // Fetch NIFTY 50 stocks from NSE\n      const nifty50Url = `${this.NSE_API_BASE}/equity-stockIndices?index=NIFTY%2050`;\n      const proxyUrl = `${this.CORS_PROXY}${encodeURIComponent(nifty50Url)}`;\n\n      const response = await this.http.get<any>(proxyUrl).toPromise();\n\n      if (response && response.data) {\n        return response.data.map((stock: any) => this.mapNSEToIndianStock(stock));\n      }\n\n      return [];\n    } catch (error) {\n      console.error('Error fetching NSE data:', error);\n      throw error;\n    }\n  }\n\n  private mapNSEToIndianStock(nseStock: any): IndianStock {\n    return {\n      symbol: nseStock.symbol,\n      name: nseStock.symbol, // NSE API doesn't always provide full company name\n      price: parseFloat(nseStock.lastPrice) || 0,\n      change: parseFloat(nseStock.change) || 0,\n      changePercent: parseFloat(nseStock.pChange) || 0,\n      volume: parseInt(nseStock.totalTradedVolume) || 0,\n      high: parseFloat(nseStock.dayHigh) || 0,\n      low: parseFloat(nseStock.dayLow) || 0,\n      open: parseFloat(nseStock.open) || 0,\n      previousClose: parseFloat(nseStock.previousClose) || 0,\n      sector: this.SECTOR_MAPPING[nseStock.symbol] || 'Others'\n    };\n  }\n\n  private getMockIndianStocks(): IndianStock[] {\n    return this.POPULAR_INDIAN_STOCKS.map(stock => {\n      const basePrice = Math.random() * 3000 + 100; // Random price between 100-3100\n      const change = (Math.random() - 0.5) * 100; // Random change between -50 to +50\n      const changePercent = (change / basePrice) * 100;\n      \n      return {\n        symbol: stock.symbol.replace('.BSE', ''),\n        name: stock.name,\n        price: Math.round(basePrice * 100) / 100,\n        change: Math.round(change * 100) / 100,\n        changePercent: Math.round(changePercent * 100) / 100,\n        volume: Math.floor(Math.random() * 10000000) + 100000, // Random volume\n        sector: stock.sector,\n        marketCap: Math.floor(Math.random() * 500000) + 10000 // Random market cap in crores\n      };\n    });\n  }\n\n  // Method to get real-time data (would require API key in production)\n  async getRealTimePrice(symbol: string): Promise<any> {\n    try {\n      // This would require an API key in production\n      // const response = await this.http.get(`${this.API_BASE}/price?symbol=${symbol}&apikey=YOUR_API_KEY`).toPromise();\n      // return response;\n      \n      // For demo, return mock data\n      return {\n        price: (Math.random() * 3000 + 100).toFixed(2),\n        change: ((Math.random() - 0.5) * 100).toFixed(2)\n      };\n    } catch (error) {\n      console.error('Error fetching real-time price:', error);\n      return null;\n    }\n  }\n\n  // Get top gainers\n  getTopGainers(stocks: IndianStock[]): IndianStock[] {\n    return stocks\n      .filter(stock => stock.changePercent > 0)\n      .sort((a, b) => b.changePercent - a.changePercent)\n      .slice(0, 5);\n  }\n\n  // Get top losers\n  getTopLosers(stocks: IndianStock[]): IndianStock[] {\n    return stocks\n      .filter(stock => stock.changePercent < 0)\n      .sort((a, b) => a.changePercent - b.changePercent)\n      .slice(0, 5);\n  }\n\n  // Get stocks by sector\n  getStocksBySector(stocks: IndianStock[], sector: string): IndianStock[] {\n    return stocks.filter(stock => stock.sector === sector);\n  }\n\n  // Get available sectors\n  getAvailableSectors(stocks: IndianStock[]): string[] {\n    const sectors = stocks.map(stock => stock.sector).filter(Boolean);\n    return [...new Set(sectors)] as string[];\n  }\n}\n"], "mappings": ";;;AAmCA,OAAM,MAAOA,mBAAmB;EAiC9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAhCxB;IACiB,KAAAC,YAAY,GAAG,8BAA8B;IAC7C,KAAAC,UAAU,GAAG,qCAAqC;IAEnE;IACiB,KAAAC,iBAAiB,GAAG,mCAAmC;IACvD,KAAAC,WAAW,GAAG,2BAA2B;IAE1D;IACiB,KAAAC,cAAc,GAA8B;MAC3D,UAAU,EAAE,QAAQ;MACpB,KAAK,EAAE,YAAY;MACnB,UAAU,EAAE,SAAS;MACrB,MAAM,EAAE,YAAY;MACpB,YAAY,EAAE,gBAAgB;MAC9B,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE,SAAS;MACtB,YAAY,EAAE,oBAAoB;MAClC,KAAK,EAAE,gBAAgB;MACvB,MAAM,EAAE,SAAS;MACjB,IAAI,EAAE,cAAc;MACpB,SAAS,EAAE,YAAY;MACvB,YAAY,EAAE,WAAW;MACzB,QAAQ,EAAE,YAAY;MACtB,YAAY,EAAE,oBAAoB;MAClC,OAAO,EAAE,YAAY;MACrB,YAAY,EAAE,QAAQ;MACtB,UAAU,EAAE,SAAS;MACrB,OAAO,EAAE,gBAAgB;MACzB,WAAW,EAAE;KACd;EAEsC;EAEjCC,eAAeA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnB,IAAI;QACF;QACA,MAAMC,QAAQ,SAASF,KAAI,CAACG,cAAc,EAAE;QAC5C,IAAID,QAAQ,IAAIA,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;UACnC,OAAOF,QAAQ;QACjB;MACF,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACC,IAAI,CAAC,2DAA2D,EAAEF,KAAK,CAAC;MAClF;MAEA;MACA,OAAOL,KAAI,CAACQ,mBAAmB,EAAE;IAAC;EACpC;EAEcL,cAAcA,CAAA;IAAA,IAAAM,MAAA;IAAA,OAAAR,iBAAA;MAC1B,IAAI;QACF;QACA,MAAMS,UAAU,GAAG,GAAGD,MAAI,CAACf,YAAY,uCAAuC;QAC9E,MAAMiB,QAAQ,GAAG,GAAGF,MAAI,CAACd,UAAU,GAAGiB,kBAAkB,CAACF,UAAU,CAAC,EAAE;QAEtE,MAAMG,QAAQ,SAASJ,MAAI,CAAChB,IAAI,CAACqB,GAAG,CAAMH,QAAQ,CAAC,CAACI,SAAS,EAAE;QAE/D,IAAIF,QAAQ,IAAIA,QAAQ,CAACG,IAAI,EAAE;UAC7B,OAAOH,QAAQ,CAACG,IAAI,CAACC,GAAG,CAAEC,KAAU,IAAKT,MAAI,CAACU,mBAAmB,CAACD,KAAK,CAAC,CAAC;QAC3E;QAEA,OAAO,EAAE;MACX,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK;MACb;IAAC;EACH;EAEQc,mBAAmBA,CAACC,QAAa;IACvC,OAAO;MACLC,MAAM,EAAED,QAAQ,CAACC,MAAM;MACvBC,IAAI,EAAEF,QAAQ,CAACC,MAAM;MAAE;MACvBE,KAAK,EAAEC,UAAU,CAACJ,QAAQ,CAACK,SAAS,CAAC,IAAI,CAAC;MAC1CC,MAAM,EAAEF,UAAU,CAACJ,QAAQ,CAACM,MAAM,CAAC,IAAI,CAAC;MACxCC,aAAa,EAAEH,UAAU,CAACJ,QAAQ,CAACQ,OAAO,CAAC,IAAI,CAAC;MAChDC,MAAM,EAAEC,QAAQ,CAACV,QAAQ,CAACW,iBAAiB,CAAC,IAAI,CAAC;MACjDC,IAAI,EAAER,UAAU,CAACJ,QAAQ,CAACa,OAAO,CAAC,IAAI,CAAC;MACvCC,GAAG,EAAEV,UAAU,CAACJ,QAAQ,CAACe,MAAM,CAAC,IAAI,CAAC;MACrCC,IAAI,EAAEZ,UAAU,CAACJ,QAAQ,CAACgB,IAAI,CAAC,IAAI,CAAC;MACpCC,aAAa,EAAEb,UAAU,CAACJ,QAAQ,CAACiB,aAAa,CAAC,IAAI,CAAC;MACtDC,MAAM,EAAE,IAAI,CAACxC,cAAc,CAACsB,QAAQ,CAACC,MAAM,CAAC,IAAI;KACjD;EACH;EAEQkB,mBAAmBA,CAAA;IACzB,OAAO,IAAI,CAACC,qBAAqB,CAACvB,GAAG,CAACC,KAAK,IAAG;MAC5C,MAAMuB,SAAS,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;MAC9C,MAAMjB,MAAM,GAAG,CAACgB,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;MAC5C,MAAMhB,aAAa,GAAID,MAAM,GAAGe,SAAS,GAAI,GAAG;MAEhD,OAAO;QACLpB,MAAM,EAAEH,KAAK,CAACG,MAAM,CAACuB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QACxCtB,IAAI,EAAEJ,KAAK,CAACI,IAAI;QAChBC,KAAK,EAAEmB,IAAI,CAACG,KAAK,CAACJ,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG;QACxCf,MAAM,EAAEgB,IAAI,CAACG,KAAK,CAACnB,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;QACtCC,aAAa,EAAEe,IAAI,CAACG,KAAK,CAAClB,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG;QACpDE,MAAM,EAAEa,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,MAAM;QAAE;QACvDL,MAAM,EAAEpB,KAAK,CAACoB,MAAM;QACpBS,SAAS,EAAEL,IAAI,CAACI,KAAK,CAACJ,IAAI,CAACC,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC;OACvD;IACH,CAAC,CAAC;EACJ;EAEA;EACMK,gBAAgBA,CAAC3B,MAAc;IAAA,OAAApB,iBAAA;MACnC,IAAI;QACF;QACA;QACA;QAEA;QACA,OAAO;UACLsB,KAAK,EAAE,CAACmB,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,EAAEM,OAAO,CAAC,CAAC,CAAC;UAC9CvB,MAAM,EAAE,CAAC,CAACgB,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,EAAEM,OAAO,CAAC,CAAC;SAChD;MACH,CAAC,CAAC,OAAO5C,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,OAAO,IAAI;MACb;IAAC;EACH;EAEA;EACA6C,aAAaA,CAACC,MAAqB;IACjC,OAAOA,MAAM,CACVC,MAAM,CAAClC,KAAK,IAAIA,KAAK,CAACS,aAAa,GAAG,CAAC,CAAC,CACxC0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC5B,aAAa,GAAG2B,CAAC,CAAC3B,aAAa,CAAC,CACjD6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA;EACAC,YAAYA,CAACN,MAAqB;IAChC,OAAOA,MAAM,CACVC,MAAM,CAAClC,KAAK,IAAIA,KAAK,CAACS,aAAa,GAAG,CAAC,CAAC,CACxC0B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC3B,aAAa,GAAG4B,CAAC,CAAC5B,aAAa,CAAC,CACjD6B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA;EACAE,iBAAiBA,CAACP,MAAqB,EAAEb,MAAc;IACrD,OAAOa,MAAM,CAACC,MAAM,CAAClC,KAAK,IAAIA,KAAK,CAACoB,MAAM,KAAKA,MAAM,CAAC;EACxD;EAEA;EACAqB,mBAAmBA,CAACR,MAAqB;IACvC,MAAMS,OAAO,GAAGT,MAAM,CAAClC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACoB,MAAM,CAAC,CAACc,MAAM,CAACS,OAAO,CAAC;IACjE,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACF,OAAO,CAAC,CAAa;EAC1C;;;uCAnJWrE,mBAAmB,EAAAwE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnB3E,mBAAmB;MAAA4E,OAAA,EAAnB5E,mBAAmB,CAAA6E,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}