{"ast": null, "code": "import _asyncToGenerator from \"/var/www/html/trading-app/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class IndianStocksService {\n  constructor(http) {\n    this.http = http;\n    // Using multiple APIs for real stock data\n    this.YAHOO_FINANCE_API = 'https://query1.finance.yahoo.com/v8/finance/chart';\n    this.CORS_PROXY = 'https://api.allorigins.win/get?url=';\n    this.CORS_PROXY_RAW = 'https://api.allorigins.win/raw?url=';\n    // Alternative free APIs\n    this.ALPHA_VANTAGE_API = 'https://www.alphavantage.co/query';\n    this.TWELVE_DATA_API = 'https://api.twelvedata.com';\n    // Indian stock symbols with Yahoo Finance suffixes\n    this.INDIAN_STOCKS_YAHOO = ['RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS', 'HINDUNILVR.NS', 'ICICIBANK.NS', 'KOTAKBANK.NS', 'BHARTIARTL.NS', 'ITC.NS', 'SBIN.NS', 'LT.NS', 'HCLTECH.NS', 'ASIANPAINT.NS', 'MARUTI.NS', 'BAJFINANCE.NS', 'WIPRO.NS', 'ULTRACEMCO.NS', 'AXISBANK.NS', 'TITAN.NS', 'SUNPHARMA.NS', 'NESTLEIND.NS', 'POWERGRID.NS', 'NTPC.NS', 'ONGC.NS', 'TECHM.NS', 'TATAMOTORS.NS', 'TATASTEEL.NS', 'JSWSTEEL.NS', 'HINDALCO.NS', 'COALINDIA.NS'];\n    // Sector mapping for Indian stocks\n    this.SECTOR_MAPPING = {\n      'RELIANCE': 'Energy',\n      'TCS': 'Technology',\n      'HDFCBANK': 'Banking',\n      'INFY': 'Technology',\n      'HINDUNILVR': 'Consumer Goods',\n      'ICICIBANK': 'Banking',\n      'KOTAKBANK': 'Banking',\n      'BHARTIARTL': 'Telecommunications',\n      'ITC': 'Consumer Goods',\n      'SBIN': 'Banking',\n      'LT': 'Construction',\n      'HCLTECH': 'Technology',\n      'ASIANPAINT': 'Chemicals',\n      'MARUTI': 'Automotive',\n      'BAJFINANCE': 'Financial Services',\n      'WIPRO': 'Technology',\n      'ULTRACEMCO': 'Cement',\n      'AXISBANK': 'Banking',\n      'TITAN': 'Consumer Goods',\n      'SUNPHARMA': 'Pharmaceuticals'\n    };\n  }\n  getIndianStocks() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Try to fetch real data from Yahoo Finance API\n        const realData = yield _this.fetchYahooFinanceStocks();\n        if (realData && realData.length > 0) {\n          console.log('Successfully fetched real stock data from Yahoo Finance');\n          return realData;\n        }\n      } catch (error) {\n        console.warn('Failed to fetch real Yahoo Finance data:', error);\n      }\n      try {\n        // Try alternative API\n        const alternativeData = yield _this.fetchAlternativeStocks();\n        if (alternativeData && alternativeData.length > 0) {\n          console.log('Successfully fetched real stock data from alternative API');\n          return alternativeData;\n        }\n      } catch (error) {\n        console.warn('Failed to fetch alternative data:', error);\n      }\n      console.log('Falling back to enhanced mock data');\n      // Fallback to enhanced mock data\n      return _this.getEnhancedMockData();\n    })();\n  }\n  fetchYahooFinanceStocks() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const stocks = [];\n        // Fetch data for popular Indian stocks from Yahoo Finance\n        const popularStocks = _this2.INDIAN_STOCKS_YAHOO.slice(0, 15); // Limit to avoid too many requests\n        for (const symbol of popularStocks) {\n          try {\n            const stockData = yield _this2.fetchSingleYahooStock(symbol);\n            if (stockData) {\n              stocks.push(stockData);\n            }\n            // Add small delay to avoid rate limiting\n            yield _this2.delay(100);\n          } catch (error) {\n            console.warn(`Failed to fetch data for ${symbol}:`, error);\n          }\n        }\n        return stocks;\n      } catch (error) {\n        console.error('Error fetching Yahoo Finance data:', error);\n        throw error;\n      }\n    })();\n  }\n  fetchSingleYahooStock(symbol) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const url = `${_this3.YAHOO_FINANCE_API}/${symbol}`;\n        const proxyUrl = `${_this3.CORS_PROXY}${encodeURIComponent(url)}`;\n        const response = yield _this3.http.get(proxyUrl).toPromise();\n        if (response && response.contents) {\n          const data = JSON.parse(response.contents);\n          if (data.chart && data.chart.result && data.chart.result[0]) {\n            return _this3.mapYahooToIndianStock(data.chart.result[0], symbol);\n          }\n        }\n        return null;\n      } catch (error) {\n        console.error(`Error fetching Yahoo data for ${symbol}:`, error);\n        return null;\n      }\n    })();\n  }\n  mapYahooToIndianStock(yahooData, symbol) {\n    const meta = yahooData.meta;\n    const quote = yahooData.indicators?.quote?.[0];\n    const cleanSymbol = symbol.replace('.NS', '');\n    const currentPrice = meta.regularMarketPrice || 0;\n    const previousClose = meta.previousClose || 0;\n    const change = currentPrice - previousClose;\n    const changePercent = previousClose > 0 ? change / previousClose * 100 : 0;\n    return {\n      symbol: cleanSymbol,\n      name: this.getCompanyName(cleanSymbol),\n      price: Math.round(currentPrice * 100) / 100,\n      change: Math.round(change * 100) / 100,\n      changePercent: Math.round(changePercent * 100) / 100,\n      volume: quote?.volume?.[quote.volume.length - 1] || 0,\n      high: meta.regularMarketDayHigh || 0,\n      low: meta.regularMarketDayLow || 0,\n      open: quote?.open?.[0] || 0,\n      previousClose: previousClose,\n      sector: this.SECTOR_MAPPING[cleanSymbol] || 'Others'\n    };\n  }\n  fetchAlternativeStocks() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Using a free API that provides Indian stock data\n        const url = 'https://latest-stock-price.p.rapidapi.com/price?Indices=NIFTY%2050';\n        const proxyUrl = `${_this4.CORS_PROXY_RAW}${encodeURIComponent(url)}`;\n        const response = yield _this4.http.get(proxyUrl).toPromise();\n        if (response && Array.isArray(response)) {\n          return response.slice(0, 20).map(stock => _this4.mapAlternativeToIndianStock(stock));\n        }\n        return [];\n      } catch (error) {\n        console.error('Error fetching alternative stock data:', error);\n        throw error;\n      }\n    })();\n  }\n  mapAlternativeToIndianStock(stockData) {\n    const change = (stockData.lastPrice || 0) - (stockData.previousClose || 0);\n    const changePercent = stockData.previousClose > 0 ? change / stockData.previousClose * 100 : 0;\n    return {\n      symbol: stockData.symbol || '',\n      name: this.getCompanyName(stockData.symbol || ''),\n      price: stockData.lastPrice || 0,\n      change: Math.round(change * 100) / 100,\n      changePercent: Math.round(changePercent * 100) / 100,\n      volume: stockData.totalTradedVolume || 0,\n      high: stockData.dayHigh || 0,\n      low: stockData.dayLow || 0,\n      open: stockData.open || 0,\n      previousClose: stockData.previousClose || 0,\n      sector: this.SECTOR_MAPPING[stockData.symbol] || 'Others'\n    };\n  }\n  getCompanyName(symbol) {\n    const companyNames = {\n      'RELIANCE': 'Reliance Industries Limited',\n      'TCS': 'Tata Consultancy Services',\n      'HDFCBANK': 'HDFC Bank Limited',\n      'INFY': 'Infosys Limited',\n      'HINDUNILVR': 'Hindustan Unilever Limited',\n      'ICICIBANK': 'ICICI Bank Limited',\n      'KOTAKBANK': 'Kotak Mahindra Bank',\n      'BHARTIARTL': 'Bharti Airtel Limited',\n      'ITC': 'ITC Limited',\n      'SBIN': 'State Bank of India',\n      'LT': 'Larsen & Toubro Limited',\n      'HCLTECH': 'HCL Technologies Limited',\n      'ASIANPAINT': 'Asian Paints Limited',\n      'MARUTI': 'Maruti Suzuki India Limited',\n      'BAJFINANCE': 'Bajaj Finance Limited',\n      'WIPRO': 'Wipro Limited',\n      'ULTRACEMCO': 'UltraTech Cement Limited',\n      'AXISBANK': 'Axis Bank Limited',\n      'TITAN': 'Titan Company Limited',\n      'SUNPHARMA': 'Sun Pharmaceutical Industries'\n    };\n    return companyNames[symbol] || symbol;\n  }\n  delay(ms) {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n  getEnhancedMockData() {\n    // Enhanced mock data with more realistic Indian stock information\n    const stocksData = [{\n      symbol: 'RELIANCE',\n      name: 'Reliance Industries Limited',\n      basePrice: 2450,\n      sector: 'Energy'\n    }, {\n      symbol: 'TCS',\n      name: 'Tata Consultancy Services',\n      basePrice: 3650,\n      sector: 'Technology'\n    }, {\n      symbol: 'HDFCBANK',\n      name: 'HDFC Bank Limited',\n      basePrice: 1580,\n      sector: 'Banking'\n    }, {\n      symbol: 'INFY',\n      name: 'Infosys Limited',\n      basePrice: 1420,\n      sector: 'Technology'\n    }, {\n      symbol: 'HINDUNILVR',\n      name: 'Hindustan Unilever Limited',\n      basePrice: 2380,\n      sector: 'Consumer Goods'\n    }, {\n      symbol: 'ICICIBANK',\n      name: 'ICICI Bank Limited',\n      basePrice: 950,\n      sector: 'Banking'\n    }, {\n      symbol: 'KOTAKBANK',\n      name: 'Kotak Mahindra Bank',\n      basePrice: 1750,\n      sector: 'Banking'\n    }, {\n      symbol: 'BHARTIARTL',\n      name: 'Bharti Airtel Limited',\n      basePrice: 850,\n      sector: 'Telecommunications'\n    }, {\n      symbol: 'ITC',\n      name: 'ITC Limited',\n      basePrice: 420,\n      sector: 'Consumer Goods'\n    }, {\n      symbol: 'SBIN',\n      name: 'State Bank of India',\n      basePrice: 580,\n      sector: 'Banking'\n    }, {\n      symbol: 'LT',\n      name: 'Larsen & Toubro Limited',\n      basePrice: 2150,\n      sector: 'Construction'\n    }, {\n      symbol: 'HCLTECH',\n      name: 'HCL Technologies Limited',\n      basePrice: 1180,\n      sector: 'Technology'\n    }, {\n      symbol: 'ASIANPAINT',\n      name: 'Asian Paints Limited',\n      basePrice: 3250,\n      sector: 'Chemicals'\n    }, {\n      symbol: 'MARUTI',\n      name: 'Maruti Suzuki India Limited',\n      basePrice: 9850,\n      sector: 'Automotive'\n    }, {\n      symbol: 'BAJFINANCE',\n      name: 'Bajaj Finance Limited',\n      basePrice: 6750,\n      sector: 'Financial Services'\n    }, {\n      symbol: 'WIPRO',\n      name: 'Wipro Limited',\n      basePrice: 420,\n      sector: 'Technology'\n    }, {\n      symbol: 'ULTRACEMCO',\n      name: 'UltraTech Cement Limited',\n      basePrice: 8950,\n      sector: 'Cement'\n    }, {\n      symbol: 'AXISBANK',\n      name: 'Axis Bank Limited',\n      basePrice: 1050,\n      sector: 'Banking'\n    }, {\n      symbol: 'TITAN',\n      name: 'Titan Company Limited',\n      basePrice: 2850,\n      sector: 'Consumer Goods'\n    }, {\n      symbol: 'SUNPHARMA',\n      name: 'Sun Pharmaceutical Industries',\n      basePrice: 1150,\n      sector: 'Pharmaceuticals'\n    }, {\n      symbol: 'NESTLEIND',\n      name: 'Nestle India Limited',\n      basePrice: 22500,\n      sector: 'Consumer Goods'\n    }, {\n      symbol: 'POWERGRID',\n      name: 'Power Grid Corporation',\n      basePrice: 220,\n      sector: 'Utilities'\n    }, {\n      symbol: 'NTPC',\n      name: 'NTPC Limited',\n      basePrice: 180,\n      sector: 'Utilities'\n    }, {\n      symbol: 'ONGC',\n      name: 'Oil & Natural Gas Corporation',\n      basePrice: 160,\n      sector: 'Energy'\n    }, {\n      symbol: 'TECHM',\n      name: 'Tech Mahindra Limited',\n      basePrice: 1450,\n      sector: 'Technology'\n    }, {\n      symbol: 'TATAMOTORS',\n      name: 'Tata Motors Limited',\n      basePrice: 750,\n      sector: 'Automotive'\n    }, {\n      symbol: 'TATASTEEL',\n      name: 'Tata Steel Limited',\n      basePrice: 120,\n      sector: 'Steel'\n    }, {\n      symbol: 'JSWSTEEL',\n      name: 'JSW Steel Limited',\n      basePrice: 850,\n      sector: 'Steel'\n    }, {\n      symbol: 'HINDALCO',\n      name: 'Hindalco Industries Limited',\n      basePrice: 450,\n      sector: 'Metals'\n    }, {\n      symbol: 'COALINDIA',\n      name: 'Coal India Limited',\n      basePrice: 280,\n      sector: 'Mining'\n    }];\n    return stocksData.map(stock => {\n      const priceVariation = (Math.random() - 0.5) * 0.1; // ±5% variation\n      const currentPrice = stock.basePrice * (1 + priceVariation);\n      const change = currentPrice - stock.basePrice;\n      const changePercent = change / stock.basePrice * 100;\n      const volume = Math.floor(Math.random() * 50000000) + 1000000; // 1M to 50M volume\n      return {\n        symbol: stock.symbol,\n        name: stock.name,\n        price: Math.round(currentPrice * 100) / 100,\n        change: Math.round(change * 100) / 100,\n        changePercent: Math.round(changePercent * 100) / 100,\n        volume: volume,\n        high: Math.round(currentPrice * 1.02 * 100) / 100,\n        low: Math.round(currentPrice * 0.98 * 100) / 100,\n        open: Math.round(stock.basePrice * (1 + (Math.random() - 0.5) * 0.02) * 100) / 100,\n        previousClose: stock.basePrice,\n        sector: stock.sector,\n        marketCap: Math.floor(currentPrice * Math.random() * 1000000000 / 10000000) // Market cap in crores\n      };\n    });\n  }\n  // Fetch all available stocks from multiple sources\n  getAllIndianStocks() {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const allStocks = [];\n        // Fetch all stocks from Yahoo Finance\n        console.log('Fetching all Indian stocks from Yahoo Finance...');\n        const yahooStocks = yield _this5.fetchAllYahooStocks();\n        allStocks.push(...yahooStocks);\n        // If we don't have enough real data, try alternative sources\n        if (allStocks.length < 20) {\n          try {\n            const alternativeStocks = yield _this5.fetchAlternativeStocks();\n            allStocks.push(...alternativeStocks);\n          } catch (error) {\n            console.warn('Failed to fetch alternative stocks:', error);\n          }\n        }\n        // Remove duplicates based on symbol\n        const uniqueStocks = allStocks.filter((stock, index, self) => index === self.findIndex(s => s.symbol === stock.symbol));\n        if (uniqueStocks.length > 0) {\n          console.log(`Successfully fetched ${uniqueStocks.length} real stocks`);\n          return uniqueStocks;\n        }\n        console.log('No real data available, using enhanced mock data');\n        return _this5.getEnhancedMockData();\n      } catch (error) {\n        console.error('Error fetching all Indian stocks:', error);\n        return _this5.getEnhancedMockData();\n      }\n    })();\n  }\n  fetchAllYahooStocks() {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const stocks = [];\n        // Fetch data for all Indian stocks from Yahoo Finance\n        for (const symbol of _this6.INDIAN_STOCKS_YAHOO) {\n          try {\n            const stockData = yield _this6.fetchSingleYahooStock(symbol);\n            if (stockData) {\n              stocks.push(stockData);\n            }\n            // Add small delay to avoid rate limiting\n            yield _this6.delay(50);\n          } catch (error) {\n            console.warn(`Failed to fetch data for ${symbol}:`, error);\n          }\n        }\n        return stocks;\n      } catch (error) {\n        console.error('Error fetching all Yahoo Finance stocks:', error);\n        return [];\n      }\n    })();\n  }\n  // Method to get real-time data\n  getRealTimePrice(symbol) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Try to fetch real-time data from NSE\n        const url = `${_this7.NSE_API_BASE}/quote-equity?symbol=${symbol}`;\n        const proxyUrl = `${_this7.CORS_PROXY}${encodeURIComponent(url)}`;\n        const response = yield _this7.http.get(proxyUrl).toPromise();\n        if (response && response.priceInfo) {\n          return {\n            price: response.priceInfo.lastPrice,\n            change: response.priceInfo.change,\n            changePercent: response.priceInfo.pChange\n          };\n        }\n        // Fallback to mock data\n        return {\n          price: (Math.random() * 3000 + 100).toFixed(2),\n          change: ((Math.random() - 0.5) * 100).toFixed(2)\n        };\n      } catch (error) {\n        console.error('Error fetching real-time price:', error);\n        return {\n          price: (Math.random() * 3000 + 100).toFixed(2),\n          change: ((Math.random() - 0.5) * 100).toFixed(2)\n        };\n      }\n    })();\n  }\n  // Get top gainers\n  getTopGainers(stocks) {\n    return stocks.filter(stock => stock.changePercent > 0).sort((a, b) => b.changePercent - a.changePercent).slice(0, 5);\n  }\n  // Get top losers\n  getTopLosers(stocks) {\n    return stocks.filter(stock => stock.changePercent < 0).sort((a, b) => a.changePercent - b.changePercent).slice(0, 5);\n  }\n  // Get stocks by sector\n  getStocksBySector(stocks, sector) {\n    return stocks.filter(stock => stock.sector === sector);\n  }\n  // Get available sectors\n  getAvailableSectors(stocks) {\n    const sectors = stocks.map(stock => stock.sector).filter(Boolean);\n    return [...new Set(sectors)];\n  }\n  static {\n    this.ɵfac = function IndianStocksService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IndianStocksService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: IndianStocksService,\n      factory: IndianStocksService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["IndianStocksService", "constructor", "http", "YAHOO_FINANCE_API", "CORS_PROXY", "CORS_PROXY_RAW", "ALPHA_VANTAGE_API", "TWELVE_DATA_API", "INDIAN_STOCKS_YAHOO", "SECTOR_MAPPING", "getIndianStocks", "_this", "_asyncToGenerator", "realData", "fetchYahooFinanceStocks", "length", "console", "log", "error", "warn", "alternativeData", "fetchAlternativeStocks", "getEnhancedMockData", "_this2", "stocks", "popularStocks", "slice", "symbol", "stockData", "fetchSingleYahooStock", "push", "delay", "_this3", "url", "proxyUrl", "encodeURIComponent", "response", "get", "to<PERSON>romise", "contents", "data", "JSON", "parse", "chart", "result", "mapYahooToIndianStock", "yahooData", "meta", "quote", "indicators", "cleanSymbol", "replace", "currentPrice", "regularMarketPrice", "previousClose", "change", "changePercent", "name", "getCompanyName", "price", "Math", "round", "volume", "high", "regularMarketDayHigh", "low", "regularMarketDayLow", "open", "sector", "_this4", "Array", "isArray", "map", "stock", "mapAlternativeToIndianStock", "lastPrice", "totalTradedVolume", "dayHigh", "dayLow", "companyNames", "ms", "Promise", "resolve", "setTimeout", "stocksData", "basePrice", "priceVariation", "random", "floor", "marketCap", "getAllIndianStocks", "_this5", "allStocks", "yahooStocks", "fetchAllYahooStocks", "alternativeStocks", "uniqueStocks", "filter", "index", "self", "findIndex", "s", "_this6", "getRealTimePrice", "_this7", "NSE_API_BASE", "priceInfo", "pChange", "toFixed", "getTopGainers", "sort", "a", "b", "getTopLosers", "getStocksBySector", "getAvailableSectors", "sectors", "Boolean", "Set", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/var/www/html/trading-app/frontend/src/app/services/indian-stocks.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\n\nexport interface IndianStock {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  marketCap?: number;\n  sector?: string;\n  high?: number;\n  low?: number;\n  open?: number;\n  previousClose?: number;\n}\n\nexport interface NSEStock {\n  symbol: string;\n  companyName: string;\n  lastPrice: number;\n  change: number;\n  pChange: number;\n  totalTradedVolume: number;\n  totalTradedValue: number;\n  dayHigh: number;\n  dayLow: number;\n  open: number;\n  previousClose: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class IndianStocksService {\n  // Using multiple APIs for real stock data\n  private readonly YAHOO_FINANCE_API = 'https://query1.finance.yahoo.com/v8/finance/chart';\n  private readonly CORS_PROXY = 'https://api.allorigins.win/get?url=';\n  private readonly CORS_PROXY_RAW = 'https://api.allorigins.win/raw?url=';\n\n  // Alternative free APIs\n  private readonly ALPHA_VANTAGE_API = 'https://www.alphavantage.co/query';\n  private readonly TWELVE_DATA_API = 'https://api.twelvedata.com';\n\n  // Indian stock symbols with Yahoo Finance suffixes\n  private readonly INDIAN_STOCKS_YAHOO = [\n    'RELIANCE.NS', 'TCS.NS', 'HDFCBANK.NS', 'INFY.NS', 'HINDUNILVR.NS',\n    'ICICIBANK.NS', 'KOTAKBANK.NS', 'BHARTIARTL.NS', 'ITC.NS', 'SBIN.NS',\n    'LT.NS', 'HCLTECH.NS', 'ASIANPAINT.NS', 'MARUTI.NS', 'BAJFINANCE.NS',\n    'WIPRO.NS', 'ULTRACEMCO.NS', 'AXISBANK.NS', 'TITAN.NS', 'SUNPHARMA.NS',\n    'NESTLEIND.NS', 'POWERGRID.NS', 'NTPC.NS', 'ONGC.NS', 'TECHM.NS',\n    'TATAMOTORS.NS', 'TATASTEEL.NS', 'JSWSTEEL.NS', 'HINDALCO.NS', 'COALINDIA.NS'\n  ];\n\n  // Sector mapping for Indian stocks\n  private readonly SECTOR_MAPPING: { [key: string]: string } = {\n    'RELIANCE': 'Energy',\n    'TCS': 'Technology',\n    'HDFCBANK': 'Banking',\n    'INFY': 'Technology',\n    'HINDUNILVR': 'Consumer Goods',\n    'ICICIBANK': 'Banking',\n    'KOTAKBANK': 'Banking',\n    'BHARTIARTL': 'Telecommunications',\n    'ITC': 'Consumer Goods',\n    'SBIN': 'Banking',\n    'LT': 'Construction',\n    'HCLTECH': 'Technology',\n    'ASIANPAINT': 'Chemicals',\n    'MARUTI': 'Automotive',\n    'BAJFINANCE': 'Financial Services',\n    'WIPRO': 'Technology',\n    'ULTRACEMCO': 'Cement',\n    'AXISBANK': 'Banking',\n    'TITAN': 'Consumer Goods',\n    'SUNPHARMA': 'Pharmaceuticals'\n  };\n\n  constructor(private http: HttpClient) {}\n\n  async getIndianStocks(): Promise<IndianStock[]> {\n    try {\n      // Try to fetch real data from Yahoo Finance API\n      const realData = await this.fetchYahooFinanceStocks();\n      if (realData && realData.length > 0) {\n        console.log('Successfully fetched real stock data from Yahoo Finance');\n        return realData;\n      }\n    } catch (error) {\n      console.warn('Failed to fetch real Yahoo Finance data:', error);\n    }\n\n    try {\n      // Try alternative API\n      const alternativeData = await this.fetchAlternativeStocks();\n      if (alternativeData && alternativeData.length > 0) {\n        console.log('Successfully fetched real stock data from alternative API');\n        return alternativeData;\n      }\n    } catch (error) {\n      console.warn('Failed to fetch alternative data:', error);\n    }\n\n    console.log('Falling back to enhanced mock data');\n    // Fallback to enhanced mock data\n    return this.getEnhancedMockData();\n  }\n\n  private async fetchYahooFinanceStocks(): Promise<IndianStock[]> {\n    try {\n      const stocks: IndianStock[] = [];\n\n      // Fetch data for popular Indian stocks from Yahoo Finance\n      const popularStocks = this.INDIAN_STOCKS_YAHOO.slice(0, 15); // Limit to avoid too many requests\n\n      for (const symbol of popularStocks) {\n        try {\n          const stockData = await this.fetchSingleYahooStock(symbol);\n          if (stockData) {\n            stocks.push(stockData);\n          }\n          // Add small delay to avoid rate limiting\n          await this.delay(100);\n        } catch (error) {\n          console.warn(`Failed to fetch data for ${symbol}:`, error);\n        }\n      }\n\n      return stocks;\n    } catch (error) {\n      console.error('Error fetching Yahoo Finance data:', error);\n      throw error;\n    }\n  }\n\n  private async fetchSingleYahooStock(symbol: string): Promise<IndianStock | null> {\n    try {\n      const url = `${this.YAHOO_FINANCE_API}/${symbol}`;\n      const proxyUrl = `${this.CORS_PROXY}${encodeURIComponent(url)}`;\n\n      const response = await this.http.get<any>(proxyUrl).toPromise();\n\n      if (response && response.contents) {\n        const data = JSON.parse(response.contents);\n        if (data.chart && data.chart.result && data.chart.result[0]) {\n          return this.mapYahooToIndianStock(data.chart.result[0], symbol);\n        }\n      }\n\n      return null;\n    } catch (error) {\n      console.error(`Error fetching Yahoo data for ${symbol}:`, error);\n      return null;\n    }\n  }\n\n  private mapYahooToIndianStock(yahooData: any, symbol: string): IndianStock {\n    const meta = yahooData.meta;\n    const quote = yahooData.indicators?.quote?.[0];\n\n    const cleanSymbol = symbol.replace('.NS', '');\n    const currentPrice = meta.regularMarketPrice || 0;\n    const previousClose = meta.previousClose || 0;\n    const change = currentPrice - previousClose;\n    const changePercent = previousClose > 0 ? (change / previousClose) * 100 : 0;\n\n    return {\n      symbol: cleanSymbol,\n      name: this.getCompanyName(cleanSymbol),\n      price: Math.round(currentPrice * 100) / 100,\n      change: Math.round(change * 100) / 100,\n      changePercent: Math.round(changePercent * 100) / 100,\n      volume: quote?.volume?.[quote.volume.length - 1] || 0,\n      high: meta.regularMarketDayHigh || 0,\n      low: meta.regularMarketDayLow || 0,\n      open: quote?.open?.[0] || 0,\n      previousClose: previousClose,\n      sector: this.SECTOR_MAPPING[cleanSymbol] || 'Others'\n    };\n  }\n\n  private async fetchAlternativeStocks(): Promise<IndianStock[]> {\n    try {\n      // Using a free API that provides Indian stock data\n      const url = 'https://latest-stock-price.p.rapidapi.com/price?Indices=NIFTY%2050';\n      const proxyUrl = `${this.CORS_PROXY_RAW}${encodeURIComponent(url)}`;\n\n      const response = await this.http.get<any>(proxyUrl).toPromise();\n\n      if (response && Array.isArray(response)) {\n        return response.slice(0, 20).map((stock: any) => this.mapAlternativeToIndianStock(stock));\n      }\n\n      return [];\n    } catch (error) {\n      console.error('Error fetching alternative stock data:', error);\n      throw error;\n    }\n  }\n\n  private mapAlternativeToIndianStock(stockData: any): IndianStock {\n    const change = (stockData.lastPrice || 0) - (stockData.previousClose || 0);\n    const changePercent = stockData.previousClose > 0 ? (change / stockData.previousClose) * 100 : 0;\n\n    return {\n      symbol: stockData.symbol || '',\n      name: this.getCompanyName(stockData.symbol || ''),\n      price: stockData.lastPrice || 0,\n      change: Math.round(change * 100) / 100,\n      changePercent: Math.round(changePercent * 100) / 100,\n      volume: stockData.totalTradedVolume || 0,\n      high: stockData.dayHigh || 0,\n      low: stockData.dayLow || 0,\n      open: stockData.open || 0,\n      previousClose: stockData.previousClose || 0,\n      sector: this.SECTOR_MAPPING[stockData.symbol] || 'Others'\n    };\n  }\n\n  private getCompanyName(symbol: string): string {\n    const companyNames: { [key: string]: string } = {\n      'RELIANCE': 'Reliance Industries Limited',\n      'TCS': 'Tata Consultancy Services',\n      'HDFCBANK': 'HDFC Bank Limited',\n      'INFY': 'Infosys Limited',\n      'HINDUNILVR': 'Hindustan Unilever Limited',\n      'ICICIBANK': 'ICICI Bank Limited',\n      'KOTAKBANK': 'Kotak Mahindra Bank',\n      'BHARTIARTL': 'Bharti Airtel Limited',\n      'ITC': 'ITC Limited',\n      'SBIN': 'State Bank of India',\n      'LT': 'Larsen & Toubro Limited',\n      'HCLTECH': 'HCL Technologies Limited',\n      'ASIANPAINT': 'Asian Paints Limited',\n      'MARUTI': 'Maruti Suzuki India Limited',\n      'BAJFINANCE': 'Bajaj Finance Limited',\n      'WIPRO': 'Wipro Limited',\n      'ULTRACEMCO': 'UltraTech Cement Limited',\n      'AXISBANK': 'Axis Bank Limited',\n      'TITAN': 'Titan Company Limited',\n      'SUNPHARMA': 'Sun Pharmaceutical Industries'\n    };\n\n    return companyNames[symbol] || symbol;\n  }\n\n  private delay(ms: number): Promise<void> {\n    return new Promise(resolve => setTimeout(resolve, ms));\n  }\n\n  private getEnhancedMockData(): IndianStock[] {\n    // Enhanced mock data with more realistic Indian stock information\n    const stocksData = [\n      { symbol: 'RELIANCE', name: 'Reliance Industries Limited', basePrice: 2450, sector: 'Energy' },\n      { symbol: 'TCS', name: 'Tata Consultancy Services', basePrice: 3650, sector: 'Technology' },\n      { symbol: 'HDFCBANK', name: 'HDFC Bank Limited', basePrice: 1580, sector: 'Banking' },\n      { symbol: 'INFY', name: 'Infosys Limited', basePrice: 1420, sector: 'Technology' },\n      { symbol: 'HINDUNILVR', name: 'Hindustan Unilever Limited', basePrice: 2380, sector: 'Consumer Goods' },\n      { symbol: 'ICICIBANK', name: 'ICICI Bank Limited', basePrice: 950, sector: 'Banking' },\n      { symbol: 'KOTAKBANK', name: 'Kotak Mahindra Bank', basePrice: 1750, sector: 'Banking' },\n      { symbol: 'BHARTIARTL', name: 'Bharti Airtel Limited', basePrice: 850, sector: 'Telecommunications' },\n      { symbol: 'ITC', name: 'ITC Limited', basePrice: 420, sector: 'Consumer Goods' },\n      { symbol: 'SBIN', name: 'State Bank of India', basePrice: 580, sector: 'Banking' },\n      { symbol: 'LT', name: 'Larsen & Toubro Limited', basePrice: 2150, sector: 'Construction' },\n      { symbol: 'HCLTECH', name: 'HCL Technologies Limited', basePrice: 1180, sector: 'Technology' },\n      { symbol: 'ASIANPAINT', name: 'Asian Paints Limited', basePrice: 3250, sector: 'Chemicals' },\n      { symbol: 'MARUTI', name: 'Maruti Suzuki India Limited', basePrice: 9850, sector: 'Automotive' },\n      { symbol: 'BAJFINANCE', name: 'Bajaj Finance Limited', basePrice: 6750, sector: 'Financial Services' },\n      { symbol: 'WIPRO', name: 'Wipro Limited', basePrice: 420, sector: 'Technology' },\n      { symbol: 'ULTRACEMCO', name: 'UltraTech Cement Limited', basePrice: 8950, sector: 'Cement' },\n      { symbol: 'AXISBANK', name: 'Axis Bank Limited', basePrice: 1050, sector: 'Banking' },\n      { symbol: 'TITAN', name: 'Titan Company Limited', basePrice: 2850, sector: 'Consumer Goods' },\n      { symbol: 'SUNPHARMA', name: 'Sun Pharmaceutical Industries', basePrice: 1150, sector: 'Pharmaceuticals' },\n      { symbol: 'NESTLEIND', name: 'Nestle India Limited', basePrice: 22500, sector: 'Consumer Goods' },\n      { symbol: 'POWERGRID', name: 'Power Grid Corporation', basePrice: 220, sector: 'Utilities' },\n      { symbol: 'NTPC', name: 'NTPC Limited', basePrice: 180, sector: 'Utilities' },\n      { symbol: 'ONGC', name: 'Oil & Natural Gas Corporation', basePrice: 160, sector: 'Energy' },\n      { symbol: 'TECHM', name: 'Tech Mahindra Limited', basePrice: 1450, sector: 'Technology' },\n      { symbol: 'TATAMOTORS', name: 'Tata Motors Limited', basePrice: 750, sector: 'Automotive' },\n      { symbol: 'TATASTEEL', name: 'Tata Steel Limited', basePrice: 120, sector: 'Steel' },\n      { symbol: 'JSWSTEEL', name: 'JSW Steel Limited', basePrice: 850, sector: 'Steel' },\n      { symbol: 'HINDALCO', name: 'Hindalco Industries Limited', basePrice: 450, sector: 'Metals' },\n      { symbol: 'COALINDIA', name: 'Coal India Limited', basePrice: 280, sector: 'Mining' }\n    ];\n\n    return stocksData.map(stock => {\n      const priceVariation = (Math.random() - 0.5) * 0.1; // ±5% variation\n      const currentPrice = stock.basePrice * (1 + priceVariation);\n      const change = currentPrice - stock.basePrice;\n      const changePercent = (change / stock.basePrice) * 100;\n      const volume = Math.floor(Math.random() * 50000000) + 1000000; // 1M to 50M volume\n\n      return {\n        symbol: stock.symbol,\n        name: stock.name,\n        price: Math.round(currentPrice * 100) / 100,\n        change: Math.round(change * 100) / 100,\n        changePercent: Math.round(changePercent * 100) / 100,\n        volume: volume,\n        high: Math.round(currentPrice * 1.02 * 100) / 100,\n        low: Math.round(currentPrice * 0.98 * 100) / 100,\n        open: Math.round(stock.basePrice * (1 + (Math.random() - 0.5) * 0.02) * 100) / 100,\n        previousClose: stock.basePrice,\n        sector: stock.sector,\n        marketCap: Math.floor((currentPrice * Math.random() * 1000000000) / 10000000) // Market cap in crores\n      };\n    });\n  }\n\n  // Fetch all available stocks from multiple sources\n  async getAllIndianStocks(): Promise<IndianStock[]> {\n    try {\n      const allStocks: IndianStock[] = [];\n\n      // Fetch all stocks from Yahoo Finance\n      console.log('Fetching all Indian stocks from Yahoo Finance...');\n      const yahooStocks = await this.fetchAllYahooStocks();\n      allStocks.push(...yahooStocks);\n\n      // If we don't have enough real data, try alternative sources\n      if (allStocks.length < 20) {\n        try {\n          const alternativeStocks = await this.fetchAlternativeStocks();\n          allStocks.push(...alternativeStocks);\n        } catch (error) {\n          console.warn('Failed to fetch alternative stocks:', error);\n        }\n      }\n\n      // Remove duplicates based on symbol\n      const uniqueStocks = allStocks.filter((stock, index, self) =>\n        index === self.findIndex(s => s.symbol === stock.symbol)\n      );\n\n      if (uniqueStocks.length > 0) {\n        console.log(`Successfully fetched ${uniqueStocks.length} real stocks`);\n        return uniqueStocks;\n      }\n\n      console.log('No real data available, using enhanced mock data');\n      return this.getEnhancedMockData();\n    } catch (error) {\n      console.error('Error fetching all Indian stocks:', error);\n      return this.getEnhancedMockData();\n    }\n  }\n\n  private async fetchAllYahooStocks(): Promise<IndianStock[]> {\n    try {\n      const stocks: IndianStock[] = [];\n\n      // Fetch data for all Indian stocks from Yahoo Finance\n      for (const symbol of this.INDIAN_STOCKS_YAHOO) {\n        try {\n          const stockData = await this.fetchSingleYahooStock(symbol);\n          if (stockData) {\n            stocks.push(stockData);\n          }\n          // Add small delay to avoid rate limiting\n          await this.delay(50);\n        } catch (error) {\n          console.warn(`Failed to fetch data for ${symbol}:`, error);\n        }\n      }\n\n      return stocks;\n    } catch (error) {\n      console.error('Error fetching all Yahoo Finance stocks:', error);\n      return [];\n    }\n  }\n\n  // Method to get real-time data\n  async getRealTimePrice(symbol: string): Promise<any> {\n    try {\n      // Try to fetch real-time data from NSE\n      const url = `${this.NSE_API_BASE}/quote-equity?symbol=${symbol}`;\n      const proxyUrl = `${this.CORS_PROXY}${encodeURIComponent(url)}`;\n\n      const response = await this.http.get<any>(proxyUrl).toPromise();\n\n      if (response && response.priceInfo) {\n        return {\n          price: response.priceInfo.lastPrice,\n          change: response.priceInfo.change,\n          changePercent: response.priceInfo.pChange\n        };\n      }\n\n      // Fallback to mock data\n      return {\n        price: (Math.random() * 3000 + 100).toFixed(2),\n        change: ((Math.random() - 0.5) * 100).toFixed(2)\n      };\n    } catch (error) {\n      console.error('Error fetching real-time price:', error);\n      return {\n        price: (Math.random() * 3000 + 100).toFixed(2),\n        change: ((Math.random() - 0.5) * 100).toFixed(2)\n      };\n    }\n  }\n\n  // Get top gainers\n  getTopGainers(stocks: IndianStock[]): IndianStock[] {\n    return stocks\n      .filter(stock => stock.changePercent > 0)\n      .sort((a, b) => b.changePercent - a.changePercent)\n      .slice(0, 5);\n  }\n\n  // Get top losers\n  getTopLosers(stocks: IndianStock[]): IndianStock[] {\n    return stocks\n      .filter(stock => stock.changePercent < 0)\n      .sort((a, b) => a.changePercent - b.changePercent)\n      .slice(0, 5);\n  }\n\n  // Get stocks by sector\n  getStocksBySector(stocks: IndianStock[], sector: string): IndianStock[] {\n    return stocks.filter(stock => stock.sector === sector);\n  }\n\n  // Get available sectors\n  getAvailableSectors(stocks: IndianStock[]): string[] {\n    const sectors = stocks.map(stock => stock.sector).filter(Boolean);\n    return [...new Set(sectors)] as string[];\n  }\n}\n"], "mappings": ";;;AAmCA,OAAM,MAAOA,mBAAmB;EA4C9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IA3CxB;IACiB,KAAAC,iBAAiB,GAAG,mDAAmD;IACvE,KAAAC,UAAU,GAAG,qCAAqC;IAClD,KAAAC,cAAc,GAAG,qCAAqC;IAEvE;IACiB,KAAAC,iBAAiB,GAAG,mCAAmC;IACvD,KAAAC,eAAe,GAAG,4BAA4B;IAE/D;IACiB,KAAAC,mBAAmB,GAAG,CACrC,aAAa,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAE,eAAe,EAClE,cAAc,EAAE,cAAc,EAAE,eAAe,EAAE,QAAQ,EAAE,SAAS,EACpE,OAAO,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,eAAe,EACpE,UAAU,EAAE,eAAe,EAAE,aAAa,EAAE,UAAU,EAAE,cAAc,EACtE,cAAc,EAAE,cAAc,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAChE,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,CAC9E;IAED;IACiB,KAAAC,cAAc,GAA8B;MAC3D,UAAU,EAAE,QAAQ;MACpB,KAAK,EAAE,YAAY;MACnB,UAAU,EAAE,SAAS;MACrB,MAAM,EAAE,YAAY;MACpB,YAAY,EAAE,gBAAgB;MAC9B,WAAW,EAAE,SAAS;MACtB,WAAW,EAAE,SAAS;MACtB,YAAY,EAAE,oBAAoB;MAClC,KAAK,EAAE,gBAAgB;MACvB,MAAM,EAAE,SAAS;MACjB,IAAI,EAAE,cAAc;MACpB,SAAS,EAAE,YAAY;MACvB,YAAY,EAAE,WAAW;MACzB,QAAQ,EAAE,YAAY;MACtB,YAAY,EAAE,oBAAoB;MAClC,OAAO,EAAE,YAAY;MACrB,YAAY,EAAE,QAAQ;MACtB,UAAU,EAAE,SAAS;MACrB,OAAO,EAAE,gBAAgB;MACzB,WAAW,EAAE;KACd;EAEsC;EAEjCC,eAAeA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnB,IAAI;QACF;QACA,MAAMC,QAAQ,SAASF,KAAI,CAACG,uBAAuB,EAAE;QACrD,IAAID,QAAQ,IAAIA,QAAQ,CAACE,MAAM,GAAG,CAAC,EAAE;UACnCC,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;UACtE,OAAOJ,QAAQ;QACjB;MACF,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdF,OAAO,CAACG,IAAI,CAAC,0CAA0C,EAAED,KAAK,CAAC;MACjE;MAEA,IAAI;QACF;QACA,MAAME,eAAe,SAAST,KAAI,CAACU,sBAAsB,EAAE;QAC3D,IAAID,eAAe,IAAIA,eAAe,CAACL,MAAM,GAAG,CAAC,EAAE;UACjDC,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;UACxE,OAAOG,eAAe;QACxB;MACF,CAAC,CAAC,OAAOF,KAAK,EAAE;QACdF,OAAO,CAACG,IAAI,CAAC,mCAAmC,EAAED,KAAK,CAAC;MAC1D;MAEAF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD;MACA,OAAON,KAAI,CAACW,mBAAmB,EAAE;IAAC;EACpC;EAEcR,uBAAuBA,CAAA;IAAA,IAAAS,MAAA;IAAA,OAAAX,iBAAA;MACnC,IAAI;QACF,MAAMY,MAAM,GAAkB,EAAE;QAEhC;QACA,MAAMC,aAAa,GAAGF,MAAI,CAACf,mBAAmB,CAACkB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;QAE7D,KAAK,MAAMC,MAAM,IAAIF,aAAa,EAAE;UAClC,IAAI;YACF,MAAMG,SAAS,SAASL,MAAI,CAACM,qBAAqB,CAACF,MAAM,CAAC;YAC1D,IAAIC,SAAS,EAAE;cACbJ,MAAM,CAACM,IAAI,CAACF,SAAS,CAAC;YACxB;YACA;YACA,MAAML,MAAI,CAACQ,KAAK,CAAC,GAAG,CAAC;UACvB,CAAC,CAAC,OAAOb,KAAK,EAAE;YACdF,OAAO,CAACG,IAAI,CAAC,4BAA4BQ,MAAM,GAAG,EAAET,KAAK,CAAC;UAC5D;QACF;QAEA,OAAOM,MAAM;MACf,CAAC,CAAC,OAAON,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC1D,MAAMA,KAAK;MACb;IAAC;EACH;EAEcW,qBAAqBA,CAACF,MAAc;IAAA,IAAAK,MAAA;IAAA,OAAApB,iBAAA;MAChD,IAAI;QACF,MAAMqB,GAAG,GAAG,GAAGD,MAAI,CAAC7B,iBAAiB,IAAIwB,MAAM,EAAE;QACjD,MAAMO,QAAQ,GAAG,GAAGF,MAAI,CAAC5B,UAAU,GAAG+B,kBAAkB,CAACF,GAAG,CAAC,EAAE;QAE/D,MAAMG,QAAQ,SAASJ,MAAI,CAAC9B,IAAI,CAACmC,GAAG,CAAMH,QAAQ,CAAC,CAACI,SAAS,EAAE;QAE/D,IAAIF,QAAQ,IAAIA,QAAQ,CAACG,QAAQ,EAAE;UACjC,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACN,QAAQ,CAACG,QAAQ,CAAC;UAC1C,IAAIC,IAAI,CAACG,KAAK,IAAIH,IAAI,CAACG,KAAK,CAACC,MAAM,IAAIJ,IAAI,CAACG,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE;YAC3D,OAAOZ,MAAI,CAACa,qBAAqB,CAACL,IAAI,CAACG,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEjB,MAAM,CAAC;UACjE;QACF;QAEA,OAAO,IAAI;MACb,CAAC,CAAC,OAAOT,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,iCAAiCS,MAAM,GAAG,EAAET,KAAK,CAAC;QAChE,OAAO,IAAI;MACb;IAAC;EACH;EAEQ2B,qBAAqBA,CAACC,SAAc,EAAEnB,MAAc;IAC1D,MAAMoB,IAAI,GAAGD,SAAS,CAACC,IAAI;IAC3B,MAAMC,KAAK,GAAGF,SAAS,CAACG,UAAU,EAAED,KAAK,GAAG,CAAC,CAAC;IAE9C,MAAME,WAAW,GAAGvB,MAAM,CAACwB,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;IAC7C,MAAMC,YAAY,GAAGL,IAAI,CAACM,kBAAkB,IAAI,CAAC;IACjD,MAAMC,aAAa,GAAGP,IAAI,CAACO,aAAa,IAAI,CAAC;IAC7C,MAAMC,MAAM,GAAGH,YAAY,GAAGE,aAAa;IAC3C,MAAME,aAAa,GAAGF,aAAa,GAAG,CAAC,GAAIC,MAAM,GAAGD,aAAa,GAAI,GAAG,GAAG,CAAC;IAE5E,OAAO;MACL3B,MAAM,EAAEuB,WAAW;MACnBO,IAAI,EAAE,IAAI,CAACC,cAAc,CAACR,WAAW,CAAC;MACtCS,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACT,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG;MAC3CG,MAAM,EAAEK,IAAI,CAACC,KAAK,CAACN,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;MACtCC,aAAa,EAAEI,IAAI,CAACC,KAAK,CAACL,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG;MACpDM,MAAM,EAAEd,KAAK,EAAEc,MAAM,GAAGd,KAAK,CAACc,MAAM,CAAC/C,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC;MACrDgD,IAAI,EAAEhB,IAAI,CAACiB,oBAAoB,IAAI,CAAC;MACpCC,GAAG,EAAElB,IAAI,CAACmB,mBAAmB,IAAI,CAAC;MAClCC,IAAI,EAAEnB,KAAK,EAAEmB,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;MAC3Bb,aAAa,EAAEA,aAAa;MAC5Bc,MAAM,EAAE,IAAI,CAAC3D,cAAc,CAACyC,WAAW,CAAC,IAAI;KAC7C;EACH;EAEc7B,sBAAsBA,CAAA;IAAA,IAAAgD,MAAA;IAAA,OAAAzD,iBAAA;MAClC,IAAI;QACF;QACA,MAAMqB,GAAG,GAAG,oEAAoE;QAChF,MAAMC,QAAQ,GAAG,GAAGmC,MAAI,CAAChE,cAAc,GAAG8B,kBAAkB,CAACF,GAAG,CAAC,EAAE;QAEnE,MAAMG,QAAQ,SAASiC,MAAI,CAACnE,IAAI,CAACmC,GAAG,CAAMH,QAAQ,CAAC,CAACI,SAAS,EAAE;QAE/D,IAAIF,QAAQ,IAAIkC,KAAK,CAACC,OAAO,CAACnC,QAAQ,CAAC,EAAE;UACvC,OAAOA,QAAQ,CAACV,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC8C,GAAG,CAAEC,KAAU,IAAKJ,MAAI,CAACK,2BAA2B,CAACD,KAAK,CAAC,CAAC;QAC3F;QAEA,OAAO,EAAE;MACX,CAAC,CAAC,OAAOvD,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,MAAMA,KAAK;MACb;IAAC;EACH;EAEQwD,2BAA2BA,CAAC9C,SAAc;IAChD,MAAM2B,MAAM,GAAG,CAAC3B,SAAS,CAAC+C,SAAS,IAAI,CAAC,KAAK/C,SAAS,CAAC0B,aAAa,IAAI,CAAC,CAAC;IAC1E,MAAME,aAAa,GAAG5B,SAAS,CAAC0B,aAAa,GAAG,CAAC,GAAIC,MAAM,GAAG3B,SAAS,CAAC0B,aAAa,GAAI,GAAG,GAAG,CAAC;IAEhG,OAAO;MACL3B,MAAM,EAAEC,SAAS,CAACD,MAAM,IAAI,EAAE;MAC9B8B,IAAI,EAAE,IAAI,CAACC,cAAc,CAAC9B,SAAS,CAACD,MAAM,IAAI,EAAE,CAAC;MACjDgC,KAAK,EAAE/B,SAAS,CAAC+C,SAAS,IAAI,CAAC;MAC/BpB,MAAM,EAAEK,IAAI,CAACC,KAAK,CAACN,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;MACtCC,aAAa,EAAEI,IAAI,CAACC,KAAK,CAACL,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG;MACpDM,MAAM,EAAElC,SAAS,CAACgD,iBAAiB,IAAI,CAAC;MACxCb,IAAI,EAAEnC,SAAS,CAACiD,OAAO,IAAI,CAAC;MAC5BZ,GAAG,EAAErC,SAAS,CAACkD,MAAM,IAAI,CAAC;MAC1BX,IAAI,EAAEvC,SAAS,CAACuC,IAAI,IAAI,CAAC;MACzBb,aAAa,EAAE1B,SAAS,CAAC0B,aAAa,IAAI,CAAC;MAC3Cc,MAAM,EAAE,IAAI,CAAC3D,cAAc,CAACmB,SAAS,CAACD,MAAM,CAAC,IAAI;KAClD;EACH;EAEQ+B,cAAcA,CAAC/B,MAAc;IACnC,MAAMoD,YAAY,GAA8B;MAC9C,UAAU,EAAE,6BAA6B;MACzC,KAAK,EAAE,2BAA2B;MAClC,UAAU,EAAE,mBAAmB;MAC/B,MAAM,EAAE,iBAAiB;MACzB,YAAY,EAAE,4BAA4B;MAC1C,WAAW,EAAE,oBAAoB;MACjC,WAAW,EAAE,qBAAqB;MAClC,YAAY,EAAE,uBAAuB;MACrC,KAAK,EAAE,aAAa;MACpB,MAAM,EAAE,qBAAqB;MAC7B,IAAI,EAAE,yBAAyB;MAC/B,SAAS,EAAE,0BAA0B;MACrC,YAAY,EAAE,sBAAsB;MACpC,QAAQ,EAAE,6BAA6B;MACvC,YAAY,EAAE,uBAAuB;MACrC,OAAO,EAAE,eAAe;MACxB,YAAY,EAAE,0BAA0B;MACxC,UAAU,EAAE,mBAAmB;MAC/B,OAAO,EAAE,uBAAuB;MAChC,WAAW,EAAE;KACd;IAED,OAAOA,YAAY,CAACpD,MAAM,CAAC,IAAIA,MAAM;EACvC;EAEQI,KAAKA,CAACiD,EAAU;IACtB,OAAO,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEF,EAAE,CAAC,CAAC;EACxD;EAEQ1D,mBAAmBA,CAAA;IACzB;IACA,MAAM8D,UAAU,GAAG,CACjB;MAAEzD,MAAM,EAAE,UAAU;MAAE8B,IAAI,EAAE,6BAA6B;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAQ,CAAE,EAC9F;MAAEzC,MAAM,EAAE,KAAK;MAAE8B,IAAI,EAAE,2BAA2B;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAY,CAAE,EAC3F;MAAEzC,MAAM,EAAE,UAAU;MAAE8B,IAAI,EAAE,mBAAmB;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAS,CAAE,EACrF;MAAEzC,MAAM,EAAE,MAAM;MAAE8B,IAAI,EAAE,iBAAiB;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAY,CAAE,EAClF;MAAEzC,MAAM,EAAE,YAAY;MAAE8B,IAAI,EAAE,4BAA4B;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAgB,CAAE,EACvG;MAAEzC,MAAM,EAAE,WAAW;MAAE8B,IAAI,EAAE,oBAAoB;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAS,CAAE,EACtF;MAAEzC,MAAM,EAAE,WAAW;MAAE8B,IAAI,EAAE,qBAAqB;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAS,CAAE,EACxF;MAAEzC,MAAM,EAAE,YAAY;MAAE8B,IAAI,EAAE,uBAAuB;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAoB,CAAE,EACrG;MAAEzC,MAAM,EAAE,KAAK;MAAE8B,IAAI,EAAE,aAAa;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAgB,CAAE,EAChF;MAAEzC,MAAM,EAAE,MAAM;MAAE8B,IAAI,EAAE,qBAAqB;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAS,CAAE,EAClF;MAAEzC,MAAM,EAAE,IAAI;MAAE8B,IAAI,EAAE,yBAAyB;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAc,CAAE,EAC1F;MAAEzC,MAAM,EAAE,SAAS;MAAE8B,IAAI,EAAE,0BAA0B;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAY,CAAE,EAC9F;MAAEzC,MAAM,EAAE,YAAY;MAAE8B,IAAI,EAAE,sBAAsB;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAW,CAAE,EAC5F;MAAEzC,MAAM,EAAE,QAAQ;MAAE8B,IAAI,EAAE,6BAA6B;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAY,CAAE,EAChG;MAAEzC,MAAM,EAAE,YAAY;MAAE8B,IAAI,EAAE,uBAAuB;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAoB,CAAE,EACtG;MAAEzC,MAAM,EAAE,OAAO;MAAE8B,IAAI,EAAE,eAAe;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAY,CAAE,EAChF;MAAEzC,MAAM,EAAE,YAAY;MAAE8B,IAAI,EAAE,0BAA0B;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAQ,CAAE,EAC7F;MAAEzC,MAAM,EAAE,UAAU;MAAE8B,IAAI,EAAE,mBAAmB;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAS,CAAE,EACrF;MAAEzC,MAAM,EAAE,OAAO;MAAE8B,IAAI,EAAE,uBAAuB;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAgB,CAAE,EAC7F;MAAEzC,MAAM,EAAE,WAAW;MAAE8B,IAAI,EAAE,+BAA+B;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAiB,CAAE,EAC1G;MAAEzC,MAAM,EAAE,WAAW;MAAE8B,IAAI,EAAE,sBAAsB;MAAE4B,SAAS,EAAE,KAAK;MAAEjB,MAAM,EAAE;IAAgB,CAAE,EACjG;MAAEzC,MAAM,EAAE,WAAW;MAAE8B,IAAI,EAAE,wBAAwB;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAW,CAAE,EAC5F;MAAEzC,MAAM,EAAE,MAAM;MAAE8B,IAAI,EAAE,cAAc;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAW,CAAE,EAC7E;MAAEzC,MAAM,EAAE,MAAM;MAAE8B,IAAI,EAAE,+BAA+B;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAQ,CAAE,EAC3F;MAAEzC,MAAM,EAAE,OAAO;MAAE8B,IAAI,EAAE,uBAAuB;MAAE4B,SAAS,EAAE,IAAI;MAAEjB,MAAM,EAAE;IAAY,CAAE,EACzF;MAAEzC,MAAM,EAAE,YAAY;MAAE8B,IAAI,EAAE,qBAAqB;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAY,CAAE,EAC3F;MAAEzC,MAAM,EAAE,WAAW;MAAE8B,IAAI,EAAE,oBAAoB;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAO,CAAE,EACpF;MAAEzC,MAAM,EAAE,UAAU;MAAE8B,IAAI,EAAE,mBAAmB;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAO,CAAE,EAClF;MAAEzC,MAAM,EAAE,UAAU;MAAE8B,IAAI,EAAE,6BAA6B;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAQ,CAAE,EAC7F;MAAEzC,MAAM,EAAE,WAAW;MAAE8B,IAAI,EAAE,oBAAoB;MAAE4B,SAAS,EAAE,GAAG;MAAEjB,MAAM,EAAE;IAAQ,CAAE,CACtF;IAED,OAAOgB,UAAU,CAACZ,GAAG,CAACC,KAAK,IAAG;MAC5B,MAAMa,cAAc,GAAG,CAAC1B,IAAI,CAAC2B,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;MACpD,MAAMnC,YAAY,GAAGqB,KAAK,CAACY,SAAS,IAAI,CAAC,GAAGC,cAAc,CAAC;MAC3D,MAAM/B,MAAM,GAAGH,YAAY,GAAGqB,KAAK,CAACY,SAAS;MAC7C,MAAM7B,aAAa,GAAID,MAAM,GAAGkB,KAAK,CAACY,SAAS,GAAI,GAAG;MACtD,MAAMvB,MAAM,GAAGF,IAAI,CAAC4B,KAAK,CAAC5B,IAAI,CAAC2B,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,OAAO,CAAC,CAAC;MAE/D,OAAO;QACL5D,MAAM,EAAE8C,KAAK,CAAC9C,MAAM;QACpB8B,IAAI,EAAEgB,KAAK,CAAChB,IAAI;QAChBE,KAAK,EAAEC,IAAI,CAACC,KAAK,CAACT,YAAY,GAAG,GAAG,CAAC,GAAG,GAAG;QAC3CG,MAAM,EAAEK,IAAI,CAACC,KAAK,CAACN,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;QACtCC,aAAa,EAAEI,IAAI,CAACC,KAAK,CAACL,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG;QACpDM,MAAM,EAAEA,MAAM;QACdC,IAAI,EAAEH,IAAI,CAACC,KAAK,CAACT,YAAY,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;QACjDa,GAAG,EAAEL,IAAI,CAACC,KAAK,CAACT,YAAY,GAAG,IAAI,GAAG,GAAG,CAAC,GAAG,GAAG;QAChDe,IAAI,EAAEP,IAAI,CAACC,KAAK,CAACY,KAAK,CAACY,SAAS,IAAI,CAAC,GAAG,CAACzB,IAAI,CAAC2B,MAAM,EAAE,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;QAClFjC,aAAa,EAAEmB,KAAK,CAACY,SAAS;QAC9BjB,MAAM,EAAEK,KAAK,CAACL,MAAM;QACpBqB,SAAS,EAAE7B,IAAI,CAAC4B,KAAK,CAAEpC,YAAY,GAAGQ,IAAI,CAAC2B,MAAM,EAAE,GAAG,UAAU,GAAI,QAAQ,CAAC,CAAC;OAC/E;IACH,CAAC,CAAC;EACJ;EAEA;EACMG,kBAAkBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA/E,iBAAA;MACtB,IAAI;QACF,MAAMgF,SAAS,GAAkB,EAAE;QAEnC;QACA5E,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAC/D,MAAM4E,WAAW,SAASF,MAAI,CAACG,mBAAmB,EAAE;QACpDF,SAAS,CAAC9D,IAAI,CAAC,GAAG+D,WAAW,CAAC;QAE9B;QACA,IAAID,SAAS,CAAC7E,MAAM,GAAG,EAAE,EAAE;UACzB,IAAI;YACF,MAAMgF,iBAAiB,SAASJ,MAAI,CAACtE,sBAAsB,EAAE;YAC7DuE,SAAS,CAAC9D,IAAI,CAAC,GAAGiE,iBAAiB,CAAC;UACtC,CAAC,CAAC,OAAO7E,KAAK,EAAE;YACdF,OAAO,CAACG,IAAI,CAAC,qCAAqC,EAAED,KAAK,CAAC;UAC5D;QACF;QAEA;QACA,MAAM8E,YAAY,GAAGJ,SAAS,CAACK,MAAM,CAAC,CAACxB,KAAK,EAAEyB,KAAK,EAAEC,IAAI,KACvDD,KAAK,KAAKC,IAAI,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC1E,MAAM,KAAK8C,KAAK,CAAC9C,MAAM,CAAC,CACzD;QAED,IAAIqE,YAAY,CAACjF,MAAM,GAAG,CAAC,EAAE;UAC3BC,OAAO,CAACC,GAAG,CAAC,wBAAwB+E,YAAY,CAACjF,MAAM,cAAc,CAAC;UACtE,OAAOiF,YAAY;QACrB;QAEAhF,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QAC/D,OAAO0E,MAAI,CAACrE,mBAAmB,EAAE;MACnC,CAAC,CAAC,OAAOJ,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD,OAAOyE,MAAI,CAACrE,mBAAmB,EAAE;MACnC;IAAC;EACH;EAEcwE,mBAAmBA,CAAA;IAAA,IAAAQ,MAAA;IAAA,OAAA1F,iBAAA;MAC/B,IAAI;QACF,MAAMY,MAAM,GAAkB,EAAE;QAEhC;QACA,KAAK,MAAMG,MAAM,IAAI2E,MAAI,CAAC9F,mBAAmB,EAAE;UAC7C,IAAI;YACF,MAAMoB,SAAS,SAAS0E,MAAI,CAACzE,qBAAqB,CAACF,MAAM,CAAC;YAC1D,IAAIC,SAAS,EAAE;cACbJ,MAAM,CAACM,IAAI,CAACF,SAAS,CAAC;YACxB;YACA;YACA,MAAM0E,MAAI,CAACvE,KAAK,CAAC,EAAE,CAAC;UACtB,CAAC,CAAC,OAAOb,KAAK,EAAE;YACdF,OAAO,CAACG,IAAI,CAAC,4BAA4BQ,MAAM,GAAG,EAAET,KAAK,CAAC;UAC5D;QACF;QAEA,OAAOM,MAAM;MACf,CAAC,CAAC,OAAON,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,OAAO,EAAE;MACX;IAAC;EACH;EAEA;EACMqF,gBAAgBA,CAAC5E,MAAc;IAAA,IAAA6E,MAAA;IAAA,OAAA5F,iBAAA;MACnC,IAAI;QACF;QACA,MAAMqB,GAAG,GAAG,GAAGuE,MAAI,CAACC,YAAY,wBAAwB9E,MAAM,EAAE;QAChE,MAAMO,QAAQ,GAAG,GAAGsE,MAAI,CAACpG,UAAU,GAAG+B,kBAAkB,CAACF,GAAG,CAAC,EAAE;QAE/D,MAAMG,QAAQ,SAASoE,MAAI,CAACtG,IAAI,CAACmC,GAAG,CAAMH,QAAQ,CAAC,CAACI,SAAS,EAAE;QAE/D,IAAIF,QAAQ,IAAIA,QAAQ,CAACsE,SAAS,EAAE;UAClC,OAAO;YACL/C,KAAK,EAAEvB,QAAQ,CAACsE,SAAS,CAAC/B,SAAS;YACnCpB,MAAM,EAAEnB,QAAQ,CAACsE,SAAS,CAACnD,MAAM;YACjCC,aAAa,EAAEpB,QAAQ,CAACsE,SAAS,CAACC;WACnC;QACH;QAEA;QACA,OAAO;UACLhD,KAAK,EAAE,CAACC,IAAI,CAAC2B,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC;UAC9CrD,MAAM,EAAE,CAAC,CAACK,IAAI,CAAC2B,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,EAAEqB,OAAO,CAAC,CAAC;SAChD;MACH,CAAC,CAAC,OAAO1F,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,OAAO;UACLyC,KAAK,EAAE,CAACC,IAAI,CAAC2B,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC;UAC9CrD,MAAM,EAAE,CAAC,CAACK,IAAI,CAAC2B,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,EAAEqB,OAAO,CAAC,CAAC;SAChD;MACH;IAAC;EACH;EAEA;EACAC,aAAaA,CAACrF,MAAqB;IACjC,OAAOA,MAAM,CACVyE,MAAM,CAACxB,KAAK,IAAIA,KAAK,CAACjB,aAAa,GAAG,CAAC,CAAC,CACxCsD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACxD,aAAa,GAAGuD,CAAC,CAACvD,aAAa,CAAC,CACjD9B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA;EACAuF,YAAYA,CAACzF,MAAqB;IAChC,OAAOA,MAAM,CACVyE,MAAM,CAACxB,KAAK,IAAIA,KAAK,CAACjB,aAAa,GAAG,CAAC,CAAC,CACxCsD,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACvD,aAAa,GAAGwD,CAAC,CAACxD,aAAa,CAAC,CACjD9B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA;EACAwF,iBAAiBA,CAAC1F,MAAqB,EAAE4C,MAAc;IACrD,OAAO5C,MAAM,CAACyE,MAAM,CAACxB,KAAK,IAAIA,KAAK,CAACL,MAAM,KAAKA,MAAM,CAAC;EACxD;EAEA;EACA+C,mBAAmBA,CAAC3F,MAAqB;IACvC,MAAM4F,OAAO,GAAG5F,MAAM,CAACgD,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACL,MAAM,CAAC,CAAC6B,MAAM,CAACoB,OAAO,CAAC;IACjE,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACF,OAAO,CAAC,CAAa;EAC1C;;;uCA1YWpH,mBAAmB,EAAAuH,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnB1H,mBAAmB;MAAA2H,OAAA,EAAnB3H,mBAAmB,CAAA4H,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}