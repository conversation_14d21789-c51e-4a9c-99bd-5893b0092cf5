{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./services/auth.service\";\nimport * as i2 from \"@angular/router\";\nfunction AppComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function AppComponent_Conditional_8_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵtext(3, \"Logout\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Welcome, \", (tmp_1_0 = ctx_r1.authService.getCurrentUser()) == null ? null : tmp_1_0.email, \"\");\n  }\n}\nfunction AppComponent_Conditional_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 10);\n    i0.ɵɵtext(1, \"Login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"a\", 11);\n    i0.ɵɵtext(3, \"Register\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class AppComponent {\n  constructor(authService) {\n    this.authService = authService;\n  }\n  logout() {\n    this.authService.logout();\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 12,\n      vars: 1,\n      consts: [[1, \"min-h-screen\", \"bg-gray-50\"], [1, \"bg-white\", \"shadow-sm\", \"border-b\"], [1, \"max-w-7xl\", \"mx-auto\", \"px-4\", \"sm:px-6\", \"lg:px-8\"], [1, \"flex\", \"justify-between\", \"h-16\"], [1, \"flex\", \"items-center\"], [1, \"text-xl\", \"font-bold\", \"text-gray-900\"], [1, \"flex\", \"items-center\", \"space-x-4\"], [1, \"max-w-7xl\", \"mx-auto\", \"py-6\", \"sm:px-6\", \"lg:px-8\"], [1, \"text-gray-700\"], [1, \"btn\", \"btn-outline\", 3, \"click\"], [\"routerLink\", \"/login\", 1, \"btn\", \"btn-outline\"], [\"routerLink\", \"/register\", 1, \"btn\", \"btn-primary\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"nav\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"h1\", 5);\n          i0.ɵɵtext(6, \"Trading App\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 6);\n          i0.ɵɵtemplate(8, AppComponent_Conditional_8_Template, 4, 1)(9, AppComponent_Conditional_9_Template, 4, 0);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(10, \"main\", 7);\n          i0.ɵɵelement(11, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵconditional(ctx.authService.isAuthenticated() ? 8 : 9);\n        }\n      },\n      dependencies: [CommonModule, RouterOutlet, RouterModule, i2.RouterLink],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "RouterModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "AppComponent_Conditional_8_Template_button_click_2_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵadvance", "ɵɵtextInterpolate1", "tmp_1_0", "authService", "getCurrentUser", "email", "AppComponent", "constructor", "ɵɵdirectiveInject", "i1", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵtemplate", "AppComponent_Conditional_8_Template", "AppComponent_Conditional_9_Template", "ɵɵelement", "ɵɵconditional", "isAuthenticated", "i2", "RouterLink", "encapsulation"], "sources": ["/var/www/html/trading-app/frontend/src/app/app.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterOutlet, RouterModule, Router } from '@angular/router';\nimport { AuthService } from './services/auth.service';\nimport { SidebarComponent } from './components/sidebar/sidebar.component';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet, RouterModule],\n  template: `\n    <div class=\"min-h-screen bg-gray-50\">\n      <!-- Navigation -->\n      <nav class=\"bg-white shadow-sm border-b\">\n        <div class=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div class=\"flex justify-between h-16\">\n            <div class=\"flex items-center\">\n              <h1 class=\"text-xl font-bold text-gray-900\">Trading App</h1>\n            </div>\n            <div class=\"flex items-center space-x-4\">\n              @if (authService.isAuthenticated()) {\n                <span class=\"text-gray-700\">Welcome, {{ authService.getCurrentUser()?.email }}</span>\n                <button (click)=\"logout()\" class=\"btn btn-outline\">Logout</button>\n              } @else {\n                <a routerLink=\"/login\" class=\"btn btn-outline\">Login</a>\n                <a routerLink=\"/register\" class=\"btn btn-primary\">Register</a>\n              }\n            </div>\n          </div>\n        </div>\n      </nav>\n\n      <!-- Main Content -->\n      <main class=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <router-outlet></router-outlet>\n      </main>\n    </div>\n  `\n})\nexport class AppComponent {\n  constructor(public authService: AuthService) {}\n\n  logout() {\n    this.authService.logout();\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAEC,YAAY,QAAgB,iBAAiB;;;;;;;IAmBpDC,EAAA,CAAAC,cAAA,cAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAkD;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACrFH,EAAA,CAAAC,cAAA,gBAAmD;IAA3CD,EAAA,CAAAI,UAAA,mBAAAC,4DAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,MAAA,EAAQ;IAAA,EAAC;IAAyBX,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IADtCH,EAAA,CAAAY,SAAA,EAAkD;IAAlDZ,EAAA,CAAAa,kBAAA,eAAAC,OAAA,GAAAN,MAAA,CAAAO,WAAA,CAAAC,cAAA,qBAAAF,OAAA,CAAAG,KAAA,KAAkD;;;;;IAG9EjB,EAAA,CAAAC,cAAA,YAA+C;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxDH,EAAA,CAAAC,cAAA,YAAkD;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;AAc9E,OAAM,MAAOe,YAAY;EACvBC,YAAmBJ,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;EAAgB;EAE9CJ,MAAMA,CAAA;IACJ,IAAI,CAACI,WAAW,CAACJ,MAAM,EAAE;EAC3B;;;uCALWO,YAAY,EAAAlB,EAAA,CAAAoB,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAZJ,YAAY;MAAAK,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAzB,EAAA,CAAA0B,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtBXhC,EANV,CAAAC,cAAA,aAAqC,aAEM,aACa,aACX,aACN,YACe;UAAAD,EAAA,CAAAE,MAAA,kBAAW;UACzDF,EADyD,CAAAG,YAAA,EAAK,EACxD;UACNH,EAAA,CAAAC,cAAA,aAAyC;UAIrCD,EAHF,CAAAkC,UAAA,IAAAC,mCAAA,OAAqC,IAAAC,mCAAA,OAG5B;UAOjBpC,EAHM,CAAAG,YAAA,EAAM,EACF,EACF,EACF;UAGNH,EAAA,CAAAC,cAAA,eAAqD;UACnDD,EAAA,CAAAqC,SAAA,qBAA+B;UAEnCrC,EADE,CAAAG,YAAA,EAAO,EACH;;;UAhBIH,EAAA,CAAAY,SAAA,GAMC;UANDZ,EAAA,CAAAsC,aAAA,CAAAL,GAAA,CAAAlB,WAAA,CAAAwB,eAAA,WAMC;;;qBAjBH1C,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAAyC,EAAA,CAAAC,UAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}