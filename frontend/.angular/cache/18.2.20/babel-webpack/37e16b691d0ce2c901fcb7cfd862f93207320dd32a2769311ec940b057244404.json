{"ast": null, "code": "import _asyncToGenerator from \"/var/www/html/trading-app/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class IndianStocksService {\n  constructor(http) {\n    this.http = http;\n    // Using a free API that doesn't require API key for demo\n    this.API_BASE = 'https://api.twelvedata.com';\n    // Popular Indian stocks for demo\n    this.POPULAR_INDIAN_STOCKS = [{\n      symbol: 'RELIANCE.BSE',\n      name: 'Reliance Industries Limited',\n      sector: 'Energy'\n    }, {\n      symbol: 'TCS.BSE',\n      name: 'Tata Consultancy Services',\n      sector: 'Technology'\n    }, {\n      symbol: 'HDFCBANK.BSE',\n      name: 'HDFC Bank Limited',\n      sector: 'Banking'\n    }, {\n      symbol: 'INFY.BSE',\n      name: 'Infosys Limited',\n      sector: 'Technology'\n    }, {\n      symbol: 'HINDUNILVR.BSE',\n      name: 'Hindustan Unilever Limited',\n      sector: 'Consumer Goods'\n    }, {\n      symbol: 'ICICIBANK.BSE',\n      name: 'ICICI Bank Limited',\n      sector: 'Banking'\n    }, {\n      symbol: 'KOTAKBANK.BSE',\n      name: 'Kotak Mahindra Bank',\n      sector: 'Banking'\n    }, {\n      symbol: 'BHARTIARTL.BSE',\n      name: 'Bharti Airtel Limited',\n      sector: 'Telecommunications'\n    }, {\n      symbol: 'ITC.BSE',\n      name: 'ITC Limited',\n      sector: 'Consumer Goods'\n    }, {\n      symbol: 'SBIN.BSE',\n      name: 'State Bank of India',\n      sector: 'Banking'\n    }, {\n      symbol: 'LT.BSE',\n      name: 'Larsen & Toubro Limited',\n      sector: 'Construction'\n    }, {\n      symbol: 'HCLTECH.BSE',\n      name: 'HCL Technologies Limited',\n      sector: 'Technology'\n    }, {\n      symbol: 'ASIANPAINT.BSE',\n      name: 'Asian Paints Limited',\n      sector: 'Chemicals'\n    }, {\n      symbol: 'MARUTI.BSE',\n      name: 'Maruti Suzuki India Limited',\n      sector: 'Automotive'\n    }, {\n      symbol: 'BAJFINANCE.BSE',\n      name: 'Bajaj Finance Limited',\n      sector: 'Financial Services'\n    }];\n  }\n  getIndianStocks() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // For demo purposes, we'll create mock data with realistic Indian stock information\n      // In a real application, you would fetch from a proper API\n      return _this.getMockIndianStocks();\n    })();\n  }\n  getMockIndianStocks() {\n    return this.POPULAR_INDIAN_STOCKS.map(stock => {\n      const basePrice = Math.random() * 3000 + 100; // Random price between 100-3100\n      const change = (Math.random() - 0.5) * 100; // Random change between -50 to +50\n      const changePercent = change / basePrice * 100;\n      return {\n        symbol: stock.symbol.replace('.BSE', ''),\n        name: stock.name,\n        price: Math.round(basePrice * 100) / 100,\n        change: Math.round(change * 100) / 100,\n        changePercent: Math.round(changePercent * 100) / 100,\n        volume: Math.floor(Math.random() * 10000000) + 100000,\n        // Random volume\n        sector: stock.sector,\n        marketCap: Math.floor(Math.random() * 500000) + 10000 // Random market cap in crores\n      };\n    });\n  }\n  // Method to get real-time data (would require API key in production)\n  getRealTimePrice(symbol) {\n    return _asyncToGenerator(function* () {\n      try {\n        // This would require an API key in production\n        // const response = await this.http.get(`${this.API_BASE}/price?symbol=${symbol}&apikey=YOUR_API_KEY`).toPromise();\n        // return response;\n        // For demo, return mock data\n        return {\n          price: (Math.random() * 3000 + 100).toFixed(2),\n          change: ((Math.random() - 0.5) * 100).toFixed(2)\n        };\n      } catch (error) {\n        console.error('Error fetching real-time price:', error);\n        return null;\n      }\n    })();\n  }\n  // Get top gainers\n  getTopGainers(stocks) {\n    return stocks.filter(stock => stock.changePercent > 0).sort((a, b) => b.changePercent - a.changePercent).slice(0, 5);\n  }\n  // Get top losers\n  getTopLosers(stocks) {\n    return stocks.filter(stock => stock.changePercent < 0).sort((a, b) => a.changePercent - b.changePercent).slice(0, 5);\n  }\n  // Get stocks by sector\n  getStocksBySector(stocks, sector) {\n    return stocks.filter(stock => stock.sector === sector);\n  }\n  // Get available sectors\n  getAvailableSectors(stocks) {\n    const sectors = stocks.map(stock => stock.sector).filter(Boolean);\n    return [...new Set(sectors)];\n  }\n  static {\n    this.ɵfac = function IndianStocksService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IndianStocksService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: IndianStocksService,\n      factory: IndianStocksService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["IndianStocksService", "constructor", "http", "API_BASE", "POPULAR_INDIAN_STOCKS", "symbol", "name", "sector", "getIndianStocks", "_this", "_asyncToGenerator", "getMockIndianStocks", "map", "stock", "basePrice", "Math", "random", "change", "changePercent", "replace", "price", "round", "volume", "floor", "marketCap", "getRealTimePrice", "toFixed", "error", "console", "getTopGainers", "stocks", "filter", "sort", "a", "b", "slice", "getTopLosers", "getStocksBySector", "getAvailableSectors", "sectors", "Boolean", "Set", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/var/www/html/trading-app/frontend/src/app/services/indian-stocks.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\n\nexport interface IndianStock {\n  symbol: string;\n  name: string;\n  price: number;\n  change: number;\n  changePercent: number;\n  volume: number;\n  marketCap?: number;\n  sector?: string;\n  high?: number;\n  low?: number;\n  open?: number;\n  previousClose?: number;\n}\n\nexport interface NSEStock {\n  symbol: string;\n  companyName: string;\n  lastPrice: number;\n  change: number;\n  pChange: number;\n  totalTradedVolume: number;\n  totalTradedValue: number;\n  dayHigh: number;\n  dayLow: number;\n  open: number;\n  previousClose: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class IndianStocksService {\n  // Using a free API that doesn't require API key for demo\n  private readonly API_BASE = 'https://api.twelvedata.com';\n  \n  // Popular Indian stocks for demo\n  private readonly POPULAR_INDIAN_STOCKS = [\n    { symbol: 'RELIANCE.BSE', name: 'Reliance Industries Limited', sector: 'Energy' },\n    { symbol: 'TCS.BSE', name: 'Tata Consultancy Services', sector: 'Technology' },\n    { symbol: 'HDFCBANK.BSE', name: 'HDFC Bank Limited', sector: 'Banking' },\n    { symbol: 'INFY.BSE', name: 'Infosys Limited', sector: 'Technology' },\n    { symbol: 'HINDUNILVR.BSE', name: 'Hindustan Unilever Limited', sector: 'Consumer Goods' },\n    { symbol: 'ICICIBANK.BSE', name: 'ICICI Bank Limited', sector: 'Banking' },\n    { symbol: 'KOTAKBANK.BSE', name: 'Kotak Mahindra Bank', sector: 'Banking' },\n    { symbol: 'BHARTIARTL.BSE', name: 'Bharti Airtel Limited', sector: 'Telecommunications' },\n    { symbol: 'ITC.BSE', name: 'ITC Limited', sector: 'Consumer Goods' },\n    { symbol: 'SBIN.BSE', name: 'State Bank of India', sector: 'Banking' },\n    { symbol: 'LT.BSE', name: 'Larsen & Toubro Limited', sector: 'Construction' },\n    { symbol: 'HCLTECH.BSE', name: 'HCL Technologies Limited', sector: 'Technology' },\n    { symbol: 'ASIANPAINT.BSE', name: 'Asian Paints Limited', sector: 'Chemicals' },\n    { symbol: 'MARUTI.BSE', name: 'Maruti Suzuki India Limited', sector: 'Automotive' },\n    { symbol: 'BAJFINANCE.BSE', name: 'Bajaj Finance Limited', sector: 'Financial Services' }\n  ];\n\n  constructor(private http: HttpClient) {}\n\n  async getIndianStocks(): Promise<IndianStock[]> {\n    // For demo purposes, we'll create mock data with realistic Indian stock information\n    // In a real application, you would fetch from a proper API\n    return this.getMockIndianStocks();\n  }\n\n  private getMockIndianStocks(): IndianStock[] {\n    return this.POPULAR_INDIAN_STOCKS.map(stock => {\n      const basePrice = Math.random() * 3000 + 100; // Random price between 100-3100\n      const change = (Math.random() - 0.5) * 100; // Random change between -50 to +50\n      const changePercent = (change / basePrice) * 100;\n      \n      return {\n        symbol: stock.symbol.replace('.BSE', ''),\n        name: stock.name,\n        price: Math.round(basePrice * 100) / 100,\n        change: Math.round(change * 100) / 100,\n        changePercent: Math.round(changePercent * 100) / 100,\n        volume: Math.floor(Math.random() * 10000000) + 100000, // Random volume\n        sector: stock.sector,\n        marketCap: Math.floor(Math.random() * 500000) + 10000 // Random market cap in crores\n      };\n    });\n  }\n\n  // Method to get real-time data (would require API key in production)\n  async getRealTimePrice(symbol: string): Promise<any> {\n    try {\n      // This would require an API key in production\n      // const response = await this.http.get(`${this.API_BASE}/price?symbol=${symbol}&apikey=YOUR_API_KEY`).toPromise();\n      // return response;\n      \n      // For demo, return mock data\n      return {\n        price: (Math.random() * 3000 + 100).toFixed(2),\n        change: ((Math.random() - 0.5) * 100).toFixed(2)\n      };\n    } catch (error) {\n      console.error('Error fetching real-time price:', error);\n      return null;\n    }\n  }\n\n  // Get top gainers\n  getTopGainers(stocks: IndianStock[]): IndianStock[] {\n    return stocks\n      .filter(stock => stock.changePercent > 0)\n      .sort((a, b) => b.changePercent - a.changePercent)\n      .slice(0, 5);\n  }\n\n  // Get top losers\n  getTopLosers(stocks: IndianStock[]): IndianStock[] {\n    return stocks\n      .filter(stock => stock.changePercent < 0)\n      .sort((a, b) => a.changePercent - b.changePercent)\n      .slice(0, 5);\n  }\n\n  // Get stocks by sector\n  getStocksBySector(stocks: IndianStock[], sector: string): IndianStock[] {\n    return stocks.filter(stock => stock.sector === sector);\n  }\n\n  // Get available sectors\n  getAvailableSectors(stocks: IndianStock[]): string[] {\n    const sectors = stocks.map(stock => stock.sector).filter(Boolean);\n    return [...new Set(sectors)] as string[];\n  }\n}\n"], "mappings": ";;;AAmCA,OAAM,MAAOA,mBAAmB;EAuB9BC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAtBxB;IACiB,KAAAC,QAAQ,GAAG,4BAA4B;IAExD;IACiB,KAAAC,qBAAqB,GAAG,CACvC;MAAEC,MAAM,EAAE,cAAc;MAAEC,IAAI,EAAE,6BAA6B;MAAEC,MAAM,EAAE;IAAQ,CAAE,EACjF;MAAEF,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE,2BAA2B;MAAEC,MAAM,EAAE;IAAY,CAAE,EAC9E;MAAEF,MAAM,EAAE,cAAc;MAAEC,IAAI,EAAE,mBAAmB;MAAEC,MAAM,EAAE;IAAS,CAAE,EACxE;MAAEF,MAAM,EAAE,UAAU;MAAEC,IAAI,EAAE,iBAAiB;MAAEC,MAAM,EAAE;IAAY,CAAE,EACrE;MAAEF,MAAM,EAAE,gBAAgB;MAAEC,IAAI,EAAE,4BAA4B;MAAEC,MAAM,EAAE;IAAgB,CAAE,EAC1F;MAAEF,MAAM,EAAE,eAAe;MAAEC,IAAI,EAAE,oBAAoB;MAAEC,MAAM,EAAE;IAAS,CAAE,EAC1E;MAAEF,MAAM,EAAE,eAAe;MAAEC,IAAI,EAAE,qBAAqB;MAAEC,MAAM,EAAE;IAAS,CAAE,EAC3E;MAAEF,MAAM,EAAE,gBAAgB;MAAEC,IAAI,EAAE,uBAAuB;MAAEC,MAAM,EAAE;IAAoB,CAAE,EACzF;MAAEF,MAAM,EAAE,SAAS;MAAEC,IAAI,EAAE,aAAa;MAAEC,MAAM,EAAE;IAAgB,CAAE,EACpE;MAAEF,MAAM,EAAE,UAAU;MAAEC,IAAI,EAAE,qBAAqB;MAAEC,MAAM,EAAE;IAAS,CAAE,EACtE;MAAEF,MAAM,EAAE,QAAQ;MAAEC,IAAI,EAAE,yBAAyB;MAAEC,MAAM,EAAE;IAAc,CAAE,EAC7E;MAAEF,MAAM,EAAE,aAAa;MAAEC,IAAI,EAAE,0BAA0B;MAAEC,MAAM,EAAE;IAAY,CAAE,EACjF;MAAEF,MAAM,EAAE,gBAAgB;MAAEC,IAAI,EAAE,sBAAsB;MAAEC,MAAM,EAAE;IAAW,CAAE,EAC/E;MAAEF,MAAM,EAAE,YAAY;MAAEC,IAAI,EAAE,6BAA6B;MAAEC,MAAM,EAAE;IAAY,CAAE,EACnF;MAAEF,MAAM,EAAE,gBAAgB;MAAEC,IAAI,EAAE,uBAAuB;MAAEC,MAAM,EAAE;IAAoB,CAAE,CAC1F;EAEsC;EAEjCC,eAAeA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACnB;MACA;MACA,OAAOD,KAAI,CAACE,mBAAmB,EAAE;IAAC;EACpC;EAEQA,mBAAmBA,CAAA;IACzB,OAAO,IAAI,CAACP,qBAAqB,CAACQ,GAAG,CAACC,KAAK,IAAG;MAC5C,MAAMC,SAAS,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC;MAC9C,MAAMC,MAAM,GAAG,CAACF,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC,CAAC;MAC5C,MAAME,aAAa,GAAID,MAAM,GAAGH,SAAS,GAAI,GAAG;MAEhD,OAAO;QACLT,MAAM,EAAEQ,KAAK,CAACR,MAAM,CAACc,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;QACxCb,IAAI,EAAEO,KAAK,CAACP,IAAI;QAChBc,KAAK,EAAEL,IAAI,CAACM,KAAK,CAACP,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG;QACxCG,MAAM,EAAEF,IAAI,CAACM,KAAK,CAACJ,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG;QACtCC,aAAa,EAAEH,IAAI,CAACM,KAAK,CAACH,aAAa,GAAG,GAAG,CAAC,GAAG,GAAG;QACpDI,MAAM,EAAEP,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACC,MAAM,EAAE,GAAG,QAAQ,CAAC,GAAG,MAAM;QAAE;QACvDT,MAAM,EAAEM,KAAK,CAACN,MAAM;QACpBiB,SAAS,EAAET,IAAI,CAACQ,KAAK,CAACR,IAAI,CAACC,MAAM,EAAE,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC;OACvD;IACH,CAAC,CAAC;EACJ;EAEA;EACMS,gBAAgBA,CAACpB,MAAc;IAAA,OAAAK,iBAAA;MACnC,IAAI;QACF;QACA;QACA;QAEA;QACA,OAAO;UACLU,KAAK,EAAE,CAACL,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,GAAG,GAAG,EAAEU,OAAO,CAAC,CAAC,CAAC;UAC9CT,MAAM,EAAE,CAAC,CAACF,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,EAAEU,OAAO,CAAC,CAAC;SAChD;MACH,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD,OAAO,IAAI;MACb;IAAC;EACH;EAEA;EACAE,aAAaA,CAACC,MAAqB;IACjC,OAAOA,MAAM,CACVC,MAAM,CAAClB,KAAK,IAAIA,KAAK,CAACK,aAAa,GAAG,CAAC,CAAC,CACxCc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAChB,aAAa,GAAGe,CAAC,CAACf,aAAa,CAAC,CACjDiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA;EACAC,YAAYA,CAACN,MAAqB;IAChC,OAAOA,MAAM,CACVC,MAAM,CAAClB,KAAK,IAAIA,KAAK,CAACK,aAAa,GAAG,CAAC,CAAC,CACxCc,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACf,aAAa,GAAGgB,CAAC,CAAChB,aAAa,CAAC,CACjDiB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAChB;EAEA;EACAE,iBAAiBA,CAACP,MAAqB,EAAEvB,MAAc;IACrD,OAAOuB,MAAM,CAACC,MAAM,CAAClB,KAAK,IAAIA,KAAK,CAACN,MAAM,KAAKA,MAAM,CAAC;EACxD;EAEA;EACA+B,mBAAmBA,CAACR,MAAqB;IACvC,MAAMS,OAAO,GAAGT,MAAM,CAAClB,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACN,MAAM,CAAC,CAACwB,MAAM,CAACS,OAAO,CAAC;IACjE,OAAO,CAAC,GAAG,IAAIC,GAAG,CAACF,OAAO,CAAC,CAAa;EAC1C;;;uCA7FWvC,mBAAmB,EAAA0C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAnB7C,mBAAmB;MAAA8C,OAAA,EAAnB9C,mBAAmB,CAAA+C,IAAA;MAAAC,UAAA,EAFlB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}