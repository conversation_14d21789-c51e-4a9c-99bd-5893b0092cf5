{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nexport class TradingComponent {\n  static {\n    this.ɵfac = function TradingComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || TradingComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: TradingComponent,\n      selectors: [[\"app-trading\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 0,\n      consts: [[1, \"space-y-6\"], [1, \"card\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-900\", \"mb-4\"], [1, \"text-gray-600\"]],\n      template: function TradingComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\", 2);\n          i0.ɵɵtext(3, \"Trading\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\", 3);\n          i0.ɵɵtext(5, \"Trading interface will be implemented here.\");\n          i0.ɵɵelementEnd()()();\n        }\n      },\n      dependencies: [CommonModule],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "TradingComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "TradingComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "encapsulation"], "sources": ["/var/www/html/trading-app/frontend/src/app/pages/trading/trading.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-trading',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"space-y-6\">\n      <div class=\"card\">\n        <h1 class=\"text-2xl font-bold text-gray-900 mb-4\">Trading</h1>\n        <p class=\"text-gray-600\">Trading interface will be implemented here.</p>\n      </div>\n    </div>\n  `\n})\nexport class TradingComponent {}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;AAe9C,OAAM,MAAOC,gBAAgB;;;uCAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA,gBAAgB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UANrBP,EAFJ,CAAAS,cAAA,aAAuB,aACH,YACkC;UAAAT,EAAA,CAAAU,MAAA,cAAO;UAAAV,EAAA,CAAAW,YAAA,EAAK;UAC9DX,EAAA,CAAAS,cAAA,WAAyB;UAAAT,EAAA,CAAAU,MAAA,kDAA2C;UAExEV,EAFwE,CAAAW,YAAA,EAAI,EACpE,EACF;;;qBAPEhB,YAAY;MAAAiB,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}