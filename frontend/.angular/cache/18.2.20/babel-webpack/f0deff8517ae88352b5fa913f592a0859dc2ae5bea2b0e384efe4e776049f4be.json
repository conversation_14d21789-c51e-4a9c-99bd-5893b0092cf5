{"ast": null, "code": "import { operate } from '../util/lift';\nimport { concatAll } from './concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from '../observable/from';\nexport function concat(...args) {\n  const scheduler = popScheduler(args);\n  return operate((source, subscriber) => {\n    concatAll()(from([source, ...args], scheduler)).subscribe(subscriber);\n  });\n}", "map": {"version": 3, "names": ["operate", "concatAll", "popScheduler", "from", "concat", "args", "scheduler", "source", "subscriber", "subscribe"], "sources": ["/var/www/html/trading-app/frontend/node_modules/rxjs/dist/esm/internal/operators/concat.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { concatAll } from './concatAll';\nimport { popScheduler } from '../util/args';\nimport { from } from '../observable/from';\nexport function concat(...args) {\n    const scheduler = popScheduler(args);\n    return operate((source, subscriber) => {\n        concatAll()(from([source, ...args], scheduler)).subscribe(subscriber);\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,IAAI,QAAQ,oBAAoB;AACzC,OAAO,SAASC,MAAMA,CAAC,GAAGC,IAAI,EAAE;EAC5B,MAAMC,SAAS,GAAGJ,YAAY,CAACG,IAAI,CAAC;EACpC,OAAOL,OAAO,CAAC,CAACO,MAAM,EAAEC,UAAU,KAAK;IACnCP,SAAS,CAAC,CAAC,CAACE,IAAI,CAAC,CAACI,MAAM,EAAE,GAAGF,IAAI,CAAC,EAAEC,SAAS,CAAC,CAAC,CAACG,SAAS,CAACD,UAAU,CAAC;EACzE,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}