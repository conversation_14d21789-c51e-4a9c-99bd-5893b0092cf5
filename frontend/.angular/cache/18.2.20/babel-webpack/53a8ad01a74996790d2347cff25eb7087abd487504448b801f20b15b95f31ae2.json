{"ast": null, "code": "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\n// The error overlay is inspired (and mostly copied) from Create React App (https://github.com/facebookincubator/create-react-app)\n// They, in turn, got inspired by webpack-hot-middleware (https://github.com/glenjamin/webpack-hot-middleware).\n\nimport ansiHTML from \"ansi-html-community\";\n\n/**\n * @type {(input: string, position: number) => string}\n */\nvar getCodePoint = String.prototype.codePointAt ? function (input, position) {\n  return input.codePointAt(position);\n} : function (input, position) {\n  return (input.charCodeAt(position) - 0xd800) * 0x400 + input.charCodeAt(position + 1) - 0xdc00 + 0x10000;\n};\n\n/**\n * @param {string} macroText\n * @param {RegExp} macroRegExp\n * @param {(input: string) => string} macroReplacer\n * @returns {string}\n */\nvar replaceUsingRegExp = function replaceUsingRegExp(macroText, macroRegExp, macroReplacer) {\n  macroRegExp.lastIndex = 0;\n  var replaceMatch = macroRegExp.exec(macroText);\n  var replaceResult;\n  if (replaceMatch) {\n    replaceResult = \"\";\n    var replaceLastIndex = 0;\n    do {\n      if (replaceLastIndex !== replaceMatch.index) {\n        replaceResult += macroText.substring(replaceLastIndex, replaceMatch.index);\n      }\n      var replaceInput = replaceMatch[0];\n      replaceResult += macroReplacer(replaceInput);\n      replaceLastIndex = replaceMatch.index + replaceInput.length;\n      // eslint-disable-next-line no-cond-assign\n    } while (replaceMatch = macroRegExp.exec(macroText));\n    if (replaceLastIndex !== macroText.length) {\n      replaceResult += macroText.substring(replaceLastIndex);\n    }\n  } else {\n    replaceResult = macroText;\n  }\n  return replaceResult;\n};\nvar references = {\n  \"<\": \"&lt;\",\n  \">\": \"&gt;\",\n  '\"': \"&quot;\",\n  \"'\": \"&apos;\",\n  \"&\": \"&amp;\"\n};\n\n/**\n * @param {string} text text\n * @returns {string}\n */\nfunction encode(text) {\n  if (!text) {\n    return \"\";\n  }\n  return replaceUsingRegExp(text, /[<>'\"&]/g, function (input) {\n    var result = references[input];\n    if (!result) {\n      var code = input.length > 1 ? getCodePoint(input, 0) : input.charCodeAt(0);\n      result = \"&#\".concat(code, \";\");\n    }\n    return result;\n  });\n}\n\n/**\n * @typedef {Object} StateDefinitions\n * @property {{[event: string]: { target: string; actions?: Array<string> }}} [on]\n */\n\n/**\n * @typedef {Object} Options\n * @property {{[state: string]: StateDefinitions}} states\n * @property {object} context;\n * @property {string} initial\n */\n\n/**\n * @typedef {Object} Implementation\n * @property {{[actionName: string]: (ctx: object, event: any) => object}} actions\n */\n\n/**\n * A simplified `createMachine` from `@xstate/fsm` with the following differences:\n *\n *  - the returned machine is technically a \"service\". No `interpret(machine).start()` is needed.\n *  - the state definition only support `on` and target must be declared with { target: 'nextState', actions: [] } explicitly.\n *  - event passed to `send` must be an object with `type` property.\n *  - actions implementation will be [assign action](https://xstate.js.org/docs/guides/context.html#assign-action) if you return any value.\n *  Do not return anything if you just want to invoke side effect.\n *\n * The goal of this custom function is to avoid installing the entire `'xstate/fsm'` package, while enabling modeling using\n * state machine. You can copy the first parameter into the editor at https://stately.ai/viz to visualize the state machine.\n *\n * @param {Options} options\n * @param {Implementation} implementation\n */\nfunction createMachine(_ref, _ref2) {\n  var states = _ref.states,\n    context = _ref.context,\n    initial = _ref.initial;\n  var actions = _ref2.actions;\n  var currentState = initial;\n  var currentContext = context;\n  return {\n    send: function send(event) {\n      var currentStateOn = states[currentState].on;\n      var transitionConfig = currentStateOn && currentStateOn[event.type];\n      if (transitionConfig) {\n        currentState = transitionConfig.target;\n        if (transitionConfig.actions) {\n          transitionConfig.actions.forEach(function (actName) {\n            var actionImpl = actions[actName];\n            var nextContextValue = actionImpl && actionImpl(currentContext, event);\n            if (nextContextValue) {\n              currentContext = _objectSpread(_objectSpread({}, currentContext), nextContextValue);\n            }\n          });\n        }\n      }\n    }\n  };\n}\n\n/**\n * @typedef {Object} ShowOverlayData\n * @property {'warning' | 'error'} level\n * @property {Array<string  | { moduleIdentifier?: string, moduleName?: string, loc?: string, message?: string }>} messages\n * @property {'build' | 'runtime'} messageSource\n */\n\n/**\n * @typedef {Object} CreateOverlayMachineOptions\n * @property {(data: ShowOverlayData) => void} showOverlay\n * @property {() => void} hideOverlay\n */\n\n/**\n * @param {CreateOverlayMachineOptions} options\n */\nvar createOverlayMachine = function createOverlayMachine(options) {\n  var hideOverlay = options.hideOverlay,\n    showOverlay = options.showOverlay;\n  return createMachine({\n    initial: \"hidden\",\n    context: {\n      level: \"error\",\n      messages: [],\n      messageSource: \"build\"\n    },\n    states: {\n      hidden: {\n        on: {\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          },\n          RUNTIME_ERROR: {\n            target: \"displayRuntimeError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          }\n        }\n      },\n      displayBuildError: {\n        on: {\n          DISMISS: {\n            target: \"hidden\",\n            actions: [\"dismissMessages\", \"hideOverlay\"]\n          },\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"appendMessages\", \"showOverlay\"]\n          }\n        }\n      },\n      displayRuntimeError: {\n        on: {\n          DISMISS: {\n            target: \"hidden\",\n            actions: [\"dismissMessages\", \"hideOverlay\"]\n          },\n          RUNTIME_ERROR: {\n            target: \"displayRuntimeError\",\n            actions: [\"appendMessages\", \"showOverlay\"]\n          },\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          }\n        }\n      }\n    }\n  }, {\n    actions: {\n      dismissMessages: function dismissMessages() {\n        return {\n          messages: [],\n          level: \"error\",\n          messageSource: \"build\"\n        };\n      },\n      appendMessages: function appendMessages(context, event) {\n        return {\n          messages: context.messages.concat(event.messages),\n          level: event.level || context.level,\n          messageSource: event.type === \"RUNTIME_ERROR\" ? \"runtime\" : \"build\"\n        };\n      },\n      setMessages: function setMessages(context, event) {\n        return {\n          messages: event.messages,\n          level: event.level || context.level,\n          messageSource: event.type === \"RUNTIME_ERROR\" ? \"runtime\" : \"build\"\n        };\n      },\n      hideOverlay: hideOverlay,\n      showOverlay: showOverlay\n    }\n  });\n};\n\n/**\n *\n * @param {Error} error\n */\nvar parseErrorToStacks = function parseErrorToStacks(error) {\n  if (!error || !(error instanceof Error)) {\n    throw new Error(\"parseErrorToStacks expects Error object\");\n  }\n  if (typeof error.stack === \"string\") {\n    return error.stack.split(\"\\n\").filter(function (stack) {\n      return stack !== \"Error: \".concat(error.message);\n    });\n  }\n};\n\n/**\n * @callback ErrorCallback\n * @param {ErrorEvent} error\n * @returns {void}\n */\n\n/**\n * @param {ErrorCallback} callback\n */\nvar listenToRuntimeError = function listenToRuntimeError(callback) {\n  window.addEventListener(\"error\", callback);\n  return function cleanup() {\n    window.removeEventListener(\"error\", callback);\n  };\n};\n\n/**\n * @callback UnhandledRejectionCallback\n * @param {PromiseRejectionEvent} rejectionEvent\n * @returns {void}\n */\n\n/**\n * @param {UnhandledRejectionCallback} callback\n */\nvar listenToUnhandledRejection = function listenToUnhandledRejection(callback) {\n  window.addEventListener(\"unhandledrejection\", callback);\n  return function cleanup() {\n    window.removeEventListener(\"unhandledrejection\", callback);\n  };\n};\n\n// Styles are inspired by `react-error-overlay`\n\nvar msgStyles = {\n  error: {\n    backgroundColor: \"rgba(206, 17, 38, 0.1)\",\n    color: \"#fccfcf\"\n  },\n  warning: {\n    backgroundColor: \"rgba(251, 245, 180, 0.1)\",\n    color: \"#fbf5b4\"\n  }\n};\nvar iframeStyle = {\n  position: \"fixed\",\n  top: 0,\n  left: 0,\n  right: 0,\n  bottom: 0,\n  width: \"100vw\",\n  height: \"100vh\",\n  border: \"none\",\n  \"z-index\": 9999999999\n};\nvar containerStyle = {\n  position: \"fixed\",\n  boxSizing: \"border-box\",\n  left: 0,\n  top: 0,\n  right: 0,\n  bottom: 0,\n  width: \"100vw\",\n  height: \"100vh\",\n  fontSize: \"large\",\n  padding: \"2rem 2rem 4rem 2rem\",\n  lineHeight: \"1.2\",\n  whiteSpace: \"pre-wrap\",\n  overflow: \"auto\",\n  backgroundColor: \"rgba(0, 0, 0, 0.9)\",\n  color: \"white\"\n};\nvar headerStyle = {\n  color: \"#e83b46\",\n  fontSize: \"2em\",\n  whiteSpace: \"pre-wrap\",\n  fontFamily: \"sans-serif\",\n  margin: \"0 2rem 2rem 0\",\n  flex: \"0 0 auto\",\n  maxHeight: \"50%\",\n  overflow: \"auto\"\n};\nvar dismissButtonStyle = {\n  color: \"#ffffff\",\n  lineHeight: \"1rem\",\n  fontSize: \"1.5rem\",\n  padding: \"1rem\",\n  cursor: \"pointer\",\n  position: \"absolute\",\n  right: 0,\n  top: 0,\n  backgroundColor: \"transparent\",\n  border: \"none\"\n};\nvar msgTypeStyle = {\n  color: \"#e83b46\",\n  fontSize: \"1.2em\",\n  marginBottom: \"1rem\",\n  fontFamily: \"sans-serif\"\n};\nvar msgTextStyle = {\n  lineHeight: \"1.5\",\n  fontSize: \"1rem\",\n  fontFamily: \"Menlo, Consolas, monospace\"\n};\n\n// ANSI HTML\n\nvar colors = {\n  reset: [\"transparent\", \"transparent\"],\n  black: \"181818\",\n  red: \"E36049\",\n  green: \"B3CB74\",\n  yellow: \"FFD080\",\n  blue: \"7CAFC2\",\n  magenta: \"7FACCA\",\n  cyan: \"C3C2EF\",\n  lightgrey: \"EBE7E3\",\n  darkgrey: \"6D7891\"\n};\nansiHTML.setColors(colors);\n\n/**\n * @param {string} type\n * @param {string  | { file?: string, moduleName?: string, loc?: string, message?: string; stack?: string[] }} item\n * @returns {{ header: string, body: string }}\n */\nvar formatProblem = function formatProblem(type, item) {\n  var header = type === \"warning\" ? \"WARNING\" : \"ERROR\";\n  var body = \"\";\n  if (typeof item === \"string\") {\n    body += item;\n  } else {\n    var file = item.file || \"\";\n    // eslint-disable-next-line no-nested-ternary\n    var moduleName = item.moduleName ? item.moduleName.indexOf(\"!\") !== -1 ? \"\".concat(item.moduleName.replace(/^(\\s|\\S)*!/, \"\"), \" (\").concat(item.moduleName, \")\") : \"\".concat(item.moduleName) : \"\";\n    var loc = item.loc;\n    header += \"\".concat(moduleName || file ? \" in \".concat(moduleName ? \"\".concat(moduleName).concat(file ? \" (\".concat(file, \")\") : \"\") : file).concat(loc ? \" \".concat(loc) : \"\") : \"\");\n    body += item.message || \"\";\n  }\n  if (Array.isArray(item.stack)) {\n    item.stack.forEach(function (stack) {\n      if (typeof stack === \"string\") {\n        body += \"\\r\\n\".concat(stack);\n      }\n    });\n  }\n  return {\n    header: header,\n    body: body\n  };\n};\n\n/**\n * @typedef {Object} CreateOverlayOptions\n * @property {string | null} trustedTypesPolicyName\n * @property {boolean | (error: Error) => void} [catchRuntimeError]\n */\n\n/**\n *\n * @param {CreateOverlayOptions} options\n */\nvar createOverlay = function createOverlay(options) {\n  /** @type {HTMLIFrameElement | null | undefined} */\n  var iframeContainerElement;\n  /** @type {HTMLDivElement | null | undefined} */\n  var containerElement;\n  /** @type {HTMLDivElement | null | undefined} */\n  var headerElement;\n  /** @type {Array<(element: HTMLDivElement) => void>} */\n  var onLoadQueue = [];\n  /** @type {TrustedTypePolicy | undefined} */\n  var overlayTrustedTypesPolicy;\n\n  /**\n   *\n   * @param {HTMLElement} element\n   * @param {CSSStyleDeclaration} style\n   */\n  function applyStyle(element, style) {\n    Object.keys(style).forEach(function (prop) {\n      element.style[prop] = style[prop];\n    });\n  }\n\n  /**\n   * @param {string | null} trustedTypesPolicyName\n   */\n  function createContainer(trustedTypesPolicyName) {\n    // Enable Trusted Types if they are available in the current browser.\n    if (window.trustedTypes) {\n      overlayTrustedTypesPolicy = window.trustedTypes.createPolicy(trustedTypesPolicyName || \"webpack-dev-server#overlay\", {\n        createHTML: function createHTML(value) {\n          return value;\n        }\n      });\n    }\n    iframeContainerElement = document.createElement(\"iframe\");\n    iframeContainerElement.id = \"webpack-dev-server-client-overlay\";\n    iframeContainerElement.src = \"about:blank\";\n    applyStyle(iframeContainerElement, iframeStyle);\n    iframeContainerElement.onload = function () {\n      var contentElement = /** @type {Document} */\n      (/** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument).createElement(\"div\");\n      containerElement = /** @type {Document} */\n      (/** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument).createElement(\"div\");\n      contentElement.id = \"webpack-dev-server-client-overlay-div\";\n      applyStyle(contentElement, containerStyle);\n      headerElement = document.createElement(\"div\");\n      headerElement.innerText = \"Compiled with problems:\";\n      applyStyle(headerElement, headerStyle);\n      var closeButtonElement = document.createElement(\"button\");\n      applyStyle(closeButtonElement, dismissButtonStyle);\n      closeButtonElement.innerText = \"×\";\n      closeButtonElement.ariaLabel = \"Dismiss\";\n      closeButtonElement.addEventListener(\"click\", function () {\n        // eslint-disable-next-line no-use-before-define\n        overlayService.send({\n          type: \"DISMISS\"\n        });\n      });\n      contentElement.appendChild(headerElement);\n      contentElement.appendChild(closeButtonElement);\n      contentElement.appendChild(containerElement);\n\n      /** @type {Document} */\n      (/** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument).body.appendChild(contentElement);\n      onLoadQueue.forEach(function (onLoad) {\n        onLoad(/** @type {HTMLDivElement} */contentElement);\n      });\n      onLoadQueue = [];\n\n      /** @type {HTMLIFrameElement} */\n      iframeContainerElement.onload = null;\n    };\n    document.body.appendChild(iframeContainerElement);\n  }\n\n  /**\n   * @param {(element: HTMLDivElement) => void} callback\n   * @param {string | null} trustedTypesPolicyName\n   */\n  function ensureOverlayExists(callback, trustedTypesPolicyName) {\n    if (containerElement) {\n      containerElement.innerHTML = overlayTrustedTypesPolicy ? overlayTrustedTypesPolicy.createHTML(\"\") : \"\";\n      // Everything is ready, call the callback right away.\n      callback(containerElement);\n      return;\n    }\n    onLoadQueue.push(callback);\n    if (iframeContainerElement) {\n      return;\n    }\n    createContainer(trustedTypesPolicyName);\n  }\n\n  // Successful compilation.\n  function hide() {\n    if (!iframeContainerElement) {\n      return;\n    }\n\n    // Clean up and reset internal state.\n    document.body.removeChild(iframeContainerElement);\n    iframeContainerElement = null;\n    containerElement = null;\n  }\n\n  // Compilation with errors (e.g. syntax error or missing modules).\n  /**\n   * @param {string} type\n   * @param {Array<string  | { moduleIdentifier?: string, moduleName?: string, loc?: string, message?: string }>} messages\n   * @param {string | null} trustedTypesPolicyName\n   * @param {'build' | 'runtime'} messageSource\n   */\n  function show(type, messages, trustedTypesPolicyName, messageSource) {\n    ensureOverlayExists(function () {\n      headerElement.innerText = messageSource === \"runtime\" ? \"Uncaught runtime errors:\" : \"Compiled with problems:\";\n      messages.forEach(function (message) {\n        var entryElement = document.createElement(\"div\");\n        var msgStyle = type === \"warning\" ? msgStyles.warning : msgStyles.error;\n        applyStyle(entryElement, _objectSpread(_objectSpread({}, msgStyle), {}, {\n          padding: \"1rem 1rem 1.5rem 1rem\"\n        }));\n        var typeElement = document.createElement(\"div\");\n        var _formatProblem = formatProblem(type, message),\n          header = _formatProblem.header,\n          body = _formatProblem.body;\n        typeElement.innerText = header;\n        applyStyle(typeElement, msgTypeStyle);\n        if (message.moduleIdentifier) {\n          applyStyle(typeElement, {\n            cursor: \"pointer\"\n          });\n          // element.dataset not supported in IE\n          typeElement.setAttribute(\"data-can-open\", true);\n          typeElement.addEventListener(\"click\", function () {\n            fetch(\"/webpack-dev-server/open-editor?fileName=\".concat(message.moduleIdentifier));\n          });\n        }\n\n        // Make it look similar to our terminal.\n        var text = ansiHTML(encode(body));\n        var messageTextNode = document.createElement(\"div\");\n        applyStyle(messageTextNode, msgTextStyle);\n        messageTextNode.innerHTML = overlayTrustedTypesPolicy ? overlayTrustedTypesPolicy.createHTML(text) : text;\n        entryElement.appendChild(typeElement);\n        entryElement.appendChild(messageTextNode);\n\n        /** @type {HTMLDivElement} */\n        containerElement.appendChild(entryElement);\n      });\n    }, trustedTypesPolicyName);\n  }\n  var overlayService = createOverlayMachine({\n    showOverlay: function showOverlay(_ref3) {\n      var _ref3$level = _ref3.level,\n        level = _ref3$level === void 0 ? \"error\" : _ref3$level,\n        messages = _ref3.messages,\n        messageSource = _ref3.messageSource;\n      return show(level, messages, options.trustedTypesPolicyName, messageSource);\n    },\n    hideOverlay: hide\n  });\n  if (options.catchRuntimeError) {\n    /**\n     * @param {Error | undefined} error\n     * @param {string} fallbackMessage\n     */\n    var handleError = function handleError(error, fallbackMessage) {\n      var errorObject = error instanceof Error ? error : new Error(error || fallbackMessage);\n      var shouldDisplay = typeof options.catchRuntimeError === \"function\" ? options.catchRuntimeError(errorObject) : true;\n      if (shouldDisplay) {\n        overlayService.send({\n          type: \"RUNTIME_ERROR\",\n          messages: [{\n            message: errorObject.message,\n            stack: parseErrorToStacks(errorObject)\n          }]\n        });\n      }\n    };\n    listenToRuntimeError(function (errorEvent) {\n      // error property may be empty in older browser like IE\n      var error = errorEvent.error,\n        message = errorEvent.message;\n      if (!error && !message) {\n        return;\n      }\n\n      // if error stack indicates a React error boundary caught the error, do not show overlay.\n      if (error && error.stack && error.stack.includes(\"invokeGuardedCallbackDev\")) {\n        return;\n      }\n      handleError(error, message);\n    });\n    listenToUnhandledRejection(function (promiseRejectionEvent) {\n      var reason = promiseRejectionEvent.reason;\n      handleError(reason, \"Unknown promise rejection reason\");\n    });\n  }\n  return overlayService;\n};\nexport { formatProblem, createOverlay };", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "ansiHTML", "getCodePoint", "codePointAt", "input", "position", "charCodeAt", "replaceUsingRegExp", "macroText", "macroRegExp", "macroReplacer", "lastIndex", "replaceMatch", "exec", "replaceResult", "replaceLastIndex", "index", "substring", "replaceInput", "references", "encode", "text", "result", "code", "concat", "createMachine", "_ref", "_ref2", "states", "context", "initial", "actions", "currentState", "currentContext", "send", "event", "currentStateOn", "on", "transitionConfig", "type", "target", "actName", "actionImpl", "nextContextValue", "createOverlayMachine", "options", "hideOverlay", "showOverlay", "level", "messages", "messageSource", "hidden", "BUILD_ERROR", "RUNTIME_ERROR", "displayBuildError", "DISMISS", "displayRuntimeError", "dismissMessages", "appendMessages", "setMessages", "parseErrorToStacks", "error", "Error", "stack", "split", "message", "listenToRuntimeError", "callback", "window", "addEventListener", "cleanup", "removeEventListener", "listenToUnhandledRejection", "msgStyles", "backgroundColor", "color", "warning", "iframeStyle", "top", "left", "right", "bottom", "width", "height", "border", "containerStyle", "boxSizing", "fontSize", "padding", "lineHeight", "whiteSpace", "overflow", "headerStyle", "fontFamily", "margin", "flex", "maxHeight", "dismissButtonStyle", "cursor", "msgTypeStyle", "marginBottom", "msgTextStyle", "colors", "reset", "black", "red", "green", "yellow", "blue", "magenta", "cyan", "<PERSON><PERSON>rey", "<PERSON><PERSON>rey", "setColors", "formatProblem", "item", "header", "body", "file", "moduleName", "indexOf", "replace", "loc", "Array", "isArray", "createOverlay", "iframeContainerElement", "containerElement", "headerElement", "onLoadQueue", "overlayTrustedTypesPolicy", "applyStyle", "element", "style", "prop", "createContainer", "trustedTypesPolicyName", "trustedTypes", "createPolicy", "createHTML", "document", "createElement", "id", "src", "onload", "contentElement", "contentDocument", "innerText", "closeButtonElement", "aria<PERSON><PERSON><PERSON>", "overlayService", "append<PERSON><PERSON><PERSON>", "onLoad", "ensureOverlayExists", "innerHTML", "hide", "<PERSON><PERSON><PERSON><PERSON>", "show", "entryElement", "msgStyle", "typeElement", "_formatProblem", "moduleIdentifier", "setAttribute", "fetch", "messageTextNode", "_ref3", "_ref3$level", "catchRuntimeError", "handleError", "fallbackMessage", "errorObject", "shouldDisplay", "errorEvent", "includes", "promiseRejectionEvent", "reason"], "sources": ["/var/www/html/trading-app/node_modules/webpack-dev-server/client/overlay.js"], "sourcesContent": ["function _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n// The error overlay is inspired (and mostly copied) from Create React App (https://github.com/facebookincubator/create-react-app)\n// They, in turn, got inspired by webpack-hot-middleware (https://github.com/glenjamin/webpack-hot-middleware).\n\nimport ansiHTML from \"ansi-html-community\";\n\n/**\n * @type {(input: string, position: number) => string}\n */\nvar getCodePoint = String.prototype.codePointAt ? function (input, position) {\n  return input.codePointAt(position);\n} : function (input, position) {\n  return (input.charCodeAt(position) - 0xd800) * 0x400 + input.charCodeAt(position + 1) - 0xdc00 + 0x10000;\n};\n\n/**\n * @param {string} macroText\n * @param {RegExp} macroRegExp\n * @param {(input: string) => string} macroReplacer\n * @returns {string}\n */\nvar replaceUsingRegExp = function replaceUsingRegExp(macroText, macroRegExp, macroReplacer) {\n  macroRegExp.lastIndex = 0;\n  var replaceMatch = macroRegExp.exec(macroText);\n  var replaceResult;\n  if (replaceMatch) {\n    replaceResult = \"\";\n    var replaceLastIndex = 0;\n    do {\n      if (replaceLastIndex !== replaceMatch.index) {\n        replaceResult += macroText.substring(replaceLastIndex, replaceMatch.index);\n      }\n      var replaceInput = replaceMatch[0];\n      replaceResult += macroReplacer(replaceInput);\n      replaceLastIndex = replaceMatch.index + replaceInput.length;\n      // eslint-disable-next-line no-cond-assign\n    } while (replaceMatch = macroRegExp.exec(macroText));\n    if (replaceLastIndex !== macroText.length) {\n      replaceResult += macroText.substring(replaceLastIndex);\n    }\n  } else {\n    replaceResult = macroText;\n  }\n  return replaceResult;\n};\nvar references = {\n  \"<\": \"&lt;\",\n  \">\": \"&gt;\",\n  '\"': \"&quot;\",\n  \"'\": \"&apos;\",\n  \"&\": \"&amp;\"\n};\n\n/**\n * @param {string} text text\n * @returns {string}\n */\nfunction encode(text) {\n  if (!text) {\n    return \"\";\n  }\n  return replaceUsingRegExp(text, /[<>'\"&]/g, function (input) {\n    var result = references[input];\n    if (!result) {\n      var code = input.length > 1 ? getCodePoint(input, 0) : input.charCodeAt(0);\n      result = \"&#\".concat(code, \";\");\n    }\n    return result;\n  });\n}\n\n/**\n * @typedef {Object} StateDefinitions\n * @property {{[event: string]: { target: string; actions?: Array<string> }}} [on]\n */\n\n/**\n * @typedef {Object} Options\n * @property {{[state: string]: StateDefinitions}} states\n * @property {object} context;\n * @property {string} initial\n */\n\n/**\n * @typedef {Object} Implementation\n * @property {{[actionName: string]: (ctx: object, event: any) => object}} actions\n */\n\n/**\n * A simplified `createMachine` from `@xstate/fsm` with the following differences:\n *\n *  - the returned machine is technically a \"service\". No `interpret(machine).start()` is needed.\n *  - the state definition only support `on` and target must be declared with { target: 'nextState', actions: [] } explicitly.\n *  - event passed to `send` must be an object with `type` property.\n *  - actions implementation will be [assign action](https://xstate.js.org/docs/guides/context.html#assign-action) if you return any value.\n *  Do not return anything if you just want to invoke side effect.\n *\n * The goal of this custom function is to avoid installing the entire `'xstate/fsm'` package, while enabling modeling using\n * state machine. You can copy the first parameter into the editor at https://stately.ai/viz to visualize the state machine.\n *\n * @param {Options} options\n * @param {Implementation} implementation\n */\nfunction createMachine(_ref, _ref2) {\n  var states = _ref.states,\n    context = _ref.context,\n    initial = _ref.initial;\n  var actions = _ref2.actions;\n  var currentState = initial;\n  var currentContext = context;\n  return {\n    send: function send(event) {\n      var currentStateOn = states[currentState].on;\n      var transitionConfig = currentStateOn && currentStateOn[event.type];\n      if (transitionConfig) {\n        currentState = transitionConfig.target;\n        if (transitionConfig.actions) {\n          transitionConfig.actions.forEach(function (actName) {\n            var actionImpl = actions[actName];\n            var nextContextValue = actionImpl && actionImpl(currentContext, event);\n            if (nextContextValue) {\n              currentContext = _objectSpread(_objectSpread({}, currentContext), nextContextValue);\n            }\n          });\n        }\n      }\n    }\n  };\n}\n\n/**\n * @typedef {Object} ShowOverlayData\n * @property {'warning' | 'error'} level\n * @property {Array<string  | { moduleIdentifier?: string, moduleName?: string, loc?: string, message?: string }>} messages\n * @property {'build' | 'runtime'} messageSource\n */\n\n/**\n * @typedef {Object} CreateOverlayMachineOptions\n * @property {(data: ShowOverlayData) => void} showOverlay\n * @property {() => void} hideOverlay\n */\n\n/**\n * @param {CreateOverlayMachineOptions} options\n */\nvar createOverlayMachine = function createOverlayMachine(options) {\n  var hideOverlay = options.hideOverlay,\n    showOverlay = options.showOverlay;\n  return createMachine({\n    initial: \"hidden\",\n    context: {\n      level: \"error\",\n      messages: [],\n      messageSource: \"build\"\n    },\n    states: {\n      hidden: {\n        on: {\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          },\n          RUNTIME_ERROR: {\n            target: \"displayRuntimeError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          }\n        }\n      },\n      displayBuildError: {\n        on: {\n          DISMISS: {\n            target: \"hidden\",\n            actions: [\"dismissMessages\", \"hideOverlay\"]\n          },\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"appendMessages\", \"showOverlay\"]\n          }\n        }\n      },\n      displayRuntimeError: {\n        on: {\n          DISMISS: {\n            target: \"hidden\",\n            actions: [\"dismissMessages\", \"hideOverlay\"]\n          },\n          RUNTIME_ERROR: {\n            target: \"displayRuntimeError\",\n            actions: [\"appendMessages\", \"showOverlay\"]\n          },\n          BUILD_ERROR: {\n            target: \"displayBuildError\",\n            actions: [\"setMessages\", \"showOverlay\"]\n          }\n        }\n      }\n    }\n  }, {\n    actions: {\n      dismissMessages: function dismissMessages() {\n        return {\n          messages: [],\n          level: \"error\",\n          messageSource: \"build\"\n        };\n      },\n      appendMessages: function appendMessages(context, event) {\n        return {\n          messages: context.messages.concat(event.messages),\n          level: event.level || context.level,\n          messageSource: event.type === \"RUNTIME_ERROR\" ? \"runtime\" : \"build\"\n        };\n      },\n      setMessages: function setMessages(context, event) {\n        return {\n          messages: event.messages,\n          level: event.level || context.level,\n          messageSource: event.type === \"RUNTIME_ERROR\" ? \"runtime\" : \"build\"\n        };\n      },\n      hideOverlay: hideOverlay,\n      showOverlay: showOverlay\n    }\n  });\n};\n\n/**\n *\n * @param {Error} error\n */\nvar parseErrorToStacks = function parseErrorToStacks(error) {\n  if (!error || !(error instanceof Error)) {\n    throw new Error(\"parseErrorToStacks expects Error object\");\n  }\n  if (typeof error.stack === \"string\") {\n    return error.stack.split(\"\\n\").filter(function (stack) {\n      return stack !== \"Error: \".concat(error.message);\n    });\n  }\n};\n\n/**\n * @callback ErrorCallback\n * @param {ErrorEvent} error\n * @returns {void}\n */\n\n/**\n * @param {ErrorCallback} callback\n */\nvar listenToRuntimeError = function listenToRuntimeError(callback) {\n  window.addEventListener(\"error\", callback);\n  return function cleanup() {\n    window.removeEventListener(\"error\", callback);\n  };\n};\n\n/**\n * @callback UnhandledRejectionCallback\n * @param {PromiseRejectionEvent} rejectionEvent\n * @returns {void}\n */\n\n/**\n * @param {UnhandledRejectionCallback} callback\n */\nvar listenToUnhandledRejection = function listenToUnhandledRejection(callback) {\n  window.addEventListener(\"unhandledrejection\", callback);\n  return function cleanup() {\n    window.removeEventListener(\"unhandledrejection\", callback);\n  };\n};\n\n// Styles are inspired by `react-error-overlay`\n\nvar msgStyles = {\n  error: {\n    backgroundColor: \"rgba(206, 17, 38, 0.1)\",\n    color: \"#fccfcf\"\n  },\n  warning: {\n    backgroundColor: \"rgba(251, 245, 180, 0.1)\",\n    color: \"#fbf5b4\"\n  }\n};\nvar iframeStyle = {\n  position: \"fixed\",\n  top: 0,\n  left: 0,\n  right: 0,\n  bottom: 0,\n  width: \"100vw\",\n  height: \"100vh\",\n  border: \"none\",\n  \"z-index\": 9999999999\n};\nvar containerStyle = {\n  position: \"fixed\",\n  boxSizing: \"border-box\",\n  left: 0,\n  top: 0,\n  right: 0,\n  bottom: 0,\n  width: \"100vw\",\n  height: \"100vh\",\n  fontSize: \"large\",\n  padding: \"2rem 2rem 4rem 2rem\",\n  lineHeight: \"1.2\",\n  whiteSpace: \"pre-wrap\",\n  overflow: \"auto\",\n  backgroundColor: \"rgba(0, 0, 0, 0.9)\",\n  color: \"white\"\n};\nvar headerStyle = {\n  color: \"#e83b46\",\n  fontSize: \"2em\",\n  whiteSpace: \"pre-wrap\",\n  fontFamily: \"sans-serif\",\n  margin: \"0 2rem 2rem 0\",\n  flex: \"0 0 auto\",\n  maxHeight: \"50%\",\n  overflow: \"auto\"\n};\nvar dismissButtonStyle = {\n  color: \"#ffffff\",\n  lineHeight: \"1rem\",\n  fontSize: \"1.5rem\",\n  padding: \"1rem\",\n  cursor: \"pointer\",\n  position: \"absolute\",\n  right: 0,\n  top: 0,\n  backgroundColor: \"transparent\",\n  border: \"none\"\n};\nvar msgTypeStyle = {\n  color: \"#e83b46\",\n  fontSize: \"1.2em\",\n  marginBottom: \"1rem\",\n  fontFamily: \"sans-serif\"\n};\nvar msgTextStyle = {\n  lineHeight: \"1.5\",\n  fontSize: \"1rem\",\n  fontFamily: \"Menlo, Consolas, monospace\"\n};\n\n// ANSI HTML\n\nvar colors = {\n  reset: [\"transparent\", \"transparent\"],\n  black: \"181818\",\n  red: \"E36049\",\n  green: \"B3CB74\",\n  yellow: \"FFD080\",\n  blue: \"7CAFC2\",\n  magenta: \"7FACCA\",\n  cyan: \"C3C2EF\",\n  lightgrey: \"EBE7E3\",\n  darkgrey: \"6D7891\"\n};\nansiHTML.setColors(colors);\n\n/**\n * @param {string} type\n * @param {string  | { file?: string, moduleName?: string, loc?: string, message?: string; stack?: string[] }} item\n * @returns {{ header: string, body: string }}\n */\nvar formatProblem = function formatProblem(type, item) {\n  var header = type === \"warning\" ? \"WARNING\" : \"ERROR\";\n  var body = \"\";\n  if (typeof item === \"string\") {\n    body += item;\n  } else {\n    var file = item.file || \"\";\n    // eslint-disable-next-line no-nested-ternary\n    var moduleName = item.moduleName ? item.moduleName.indexOf(\"!\") !== -1 ? \"\".concat(item.moduleName.replace(/^(\\s|\\S)*!/, \"\"), \" (\").concat(item.moduleName, \")\") : \"\".concat(item.moduleName) : \"\";\n    var loc = item.loc;\n    header += \"\".concat(moduleName || file ? \" in \".concat(moduleName ? \"\".concat(moduleName).concat(file ? \" (\".concat(file, \")\") : \"\") : file).concat(loc ? \" \".concat(loc) : \"\") : \"\");\n    body += item.message || \"\";\n  }\n  if (Array.isArray(item.stack)) {\n    item.stack.forEach(function (stack) {\n      if (typeof stack === \"string\") {\n        body += \"\\r\\n\".concat(stack);\n      }\n    });\n  }\n  return {\n    header: header,\n    body: body\n  };\n};\n\n/**\n * @typedef {Object} CreateOverlayOptions\n * @property {string | null} trustedTypesPolicyName\n * @property {boolean | (error: Error) => void} [catchRuntimeError]\n */\n\n/**\n *\n * @param {CreateOverlayOptions} options\n */\nvar createOverlay = function createOverlay(options) {\n  /** @type {HTMLIFrameElement | null | undefined} */\n  var iframeContainerElement;\n  /** @type {HTMLDivElement | null | undefined} */\n  var containerElement;\n  /** @type {HTMLDivElement | null | undefined} */\n  var headerElement;\n  /** @type {Array<(element: HTMLDivElement) => void>} */\n  var onLoadQueue = [];\n  /** @type {TrustedTypePolicy | undefined} */\n  var overlayTrustedTypesPolicy;\n\n  /**\n   *\n   * @param {HTMLElement} element\n   * @param {CSSStyleDeclaration} style\n   */\n  function applyStyle(element, style) {\n    Object.keys(style).forEach(function (prop) {\n      element.style[prop] = style[prop];\n    });\n  }\n\n  /**\n   * @param {string | null} trustedTypesPolicyName\n   */\n  function createContainer(trustedTypesPolicyName) {\n    // Enable Trusted Types if they are available in the current browser.\n    if (window.trustedTypes) {\n      overlayTrustedTypesPolicy = window.trustedTypes.createPolicy(trustedTypesPolicyName || \"webpack-dev-server#overlay\", {\n        createHTML: function createHTML(value) {\n          return value;\n        }\n      });\n    }\n    iframeContainerElement = document.createElement(\"iframe\");\n    iframeContainerElement.id = \"webpack-dev-server-client-overlay\";\n    iframeContainerElement.src = \"about:blank\";\n    applyStyle(iframeContainerElement, iframeStyle);\n    iframeContainerElement.onload = function () {\n      var contentElement = /** @type {Document} */\n      (/** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument).createElement(\"div\");\n      containerElement = /** @type {Document} */\n      (/** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument).createElement(\"div\");\n      contentElement.id = \"webpack-dev-server-client-overlay-div\";\n      applyStyle(contentElement, containerStyle);\n      headerElement = document.createElement(\"div\");\n      headerElement.innerText = \"Compiled with problems:\";\n      applyStyle(headerElement, headerStyle);\n      var closeButtonElement = document.createElement(\"button\");\n      applyStyle(closeButtonElement, dismissButtonStyle);\n      closeButtonElement.innerText = \"×\";\n      closeButtonElement.ariaLabel = \"Dismiss\";\n      closeButtonElement.addEventListener(\"click\", function () {\n        // eslint-disable-next-line no-use-before-define\n        overlayService.send({\n          type: \"DISMISS\"\n        });\n      });\n      contentElement.appendChild(headerElement);\n      contentElement.appendChild(closeButtonElement);\n      contentElement.appendChild(containerElement);\n\n      /** @type {Document} */\n      (/** @type {HTMLIFrameElement} */\n      iframeContainerElement.contentDocument).body.appendChild(contentElement);\n      onLoadQueue.forEach(function (onLoad) {\n        onLoad(/** @type {HTMLDivElement} */contentElement);\n      });\n      onLoadQueue = [];\n\n      /** @type {HTMLIFrameElement} */\n      iframeContainerElement.onload = null;\n    };\n    document.body.appendChild(iframeContainerElement);\n  }\n\n  /**\n   * @param {(element: HTMLDivElement) => void} callback\n   * @param {string | null} trustedTypesPolicyName\n   */\n  function ensureOverlayExists(callback, trustedTypesPolicyName) {\n    if (containerElement) {\n      containerElement.innerHTML = overlayTrustedTypesPolicy ? overlayTrustedTypesPolicy.createHTML(\"\") : \"\";\n      // Everything is ready, call the callback right away.\n      callback(containerElement);\n      return;\n    }\n    onLoadQueue.push(callback);\n    if (iframeContainerElement) {\n      return;\n    }\n    createContainer(trustedTypesPolicyName);\n  }\n\n  // Successful compilation.\n  function hide() {\n    if (!iframeContainerElement) {\n      return;\n    }\n\n    // Clean up and reset internal state.\n    document.body.removeChild(iframeContainerElement);\n    iframeContainerElement = null;\n    containerElement = null;\n  }\n\n  // Compilation with errors (e.g. syntax error or missing modules).\n  /**\n   * @param {string} type\n   * @param {Array<string  | { moduleIdentifier?: string, moduleName?: string, loc?: string, message?: string }>} messages\n   * @param {string | null} trustedTypesPolicyName\n   * @param {'build' | 'runtime'} messageSource\n   */\n  function show(type, messages, trustedTypesPolicyName, messageSource) {\n    ensureOverlayExists(function () {\n      headerElement.innerText = messageSource === \"runtime\" ? \"Uncaught runtime errors:\" : \"Compiled with problems:\";\n      messages.forEach(function (message) {\n        var entryElement = document.createElement(\"div\");\n        var msgStyle = type === \"warning\" ? msgStyles.warning : msgStyles.error;\n        applyStyle(entryElement, _objectSpread(_objectSpread({}, msgStyle), {}, {\n          padding: \"1rem 1rem 1.5rem 1rem\"\n        }));\n        var typeElement = document.createElement(\"div\");\n        var _formatProblem = formatProblem(type, message),\n          header = _formatProblem.header,\n          body = _formatProblem.body;\n        typeElement.innerText = header;\n        applyStyle(typeElement, msgTypeStyle);\n        if (message.moduleIdentifier) {\n          applyStyle(typeElement, {\n            cursor: \"pointer\"\n          });\n          // element.dataset not supported in IE\n          typeElement.setAttribute(\"data-can-open\", true);\n          typeElement.addEventListener(\"click\", function () {\n            fetch(\"/webpack-dev-server/open-editor?fileName=\".concat(message.moduleIdentifier));\n          });\n        }\n\n        // Make it look similar to our terminal.\n        var text = ansiHTML(encode(body));\n        var messageTextNode = document.createElement(\"div\");\n        applyStyle(messageTextNode, msgTextStyle);\n        messageTextNode.innerHTML = overlayTrustedTypesPolicy ? overlayTrustedTypesPolicy.createHTML(text) : text;\n        entryElement.appendChild(typeElement);\n        entryElement.appendChild(messageTextNode);\n\n        /** @type {HTMLDivElement} */\n        containerElement.appendChild(entryElement);\n      });\n    }, trustedTypesPolicyName);\n  }\n  var overlayService = createOverlayMachine({\n    showOverlay: function showOverlay(_ref3) {\n      var _ref3$level = _ref3.level,\n        level = _ref3$level === void 0 ? \"error\" : _ref3$level,\n        messages = _ref3.messages,\n        messageSource = _ref3.messageSource;\n      return show(level, messages, options.trustedTypesPolicyName, messageSource);\n    },\n    hideOverlay: hide\n  });\n  if (options.catchRuntimeError) {\n    /**\n     * @param {Error | undefined} error\n     * @param {string} fallbackMessage\n     */\n    var handleError = function handleError(error, fallbackMessage) {\n      var errorObject = error instanceof Error ? error : new Error(error || fallbackMessage);\n      var shouldDisplay = typeof options.catchRuntimeError === \"function\" ? options.catchRuntimeError(errorObject) : true;\n      if (shouldDisplay) {\n        overlayService.send({\n          type: \"RUNTIME_ERROR\",\n          messages: [{\n            message: errorObject.message,\n            stack: parseErrorToStacks(errorObject)\n          }]\n        });\n      }\n    };\n    listenToRuntimeError(function (errorEvent) {\n      // error property may be empty in older browser like IE\n      var error = errorEvent.error,\n        message = errorEvent.message;\n      if (!error && !message) {\n        return;\n      }\n\n      // if error stack indicates a React error boundary caught the error, do not show overlay.\n      if (error && error.stack && error.stack.includes(\"invokeGuardedCallbackDev\")) {\n        return;\n      }\n      handleError(error, message);\n    });\n    listenToUnhandledRejection(function (promiseRejectionEvent) {\n      var reason = promiseRejectionEvent.reason;\n      handleError(reason, \"Unknown promise rejection reason\");\n    });\n  }\n  return overlayService;\n};\nexport { formatProblem, createOverlay };"], "mappings": "AAAA,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGS,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAACY,MAAM,CAAC,UAAUL,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACI,wBAAwB,CAACP,CAAC,EAAEC,CAAC,CAAC,CAACO,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACO,IAAI,CAACC,KAAK,CAACR,CAAC,EAAER,CAAC,CAAC;EAAE;EAAE,OAAOQ,CAAC;AAAE;AAC9P,SAASS,aAAaA,CAACX,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,SAAS,CAACC,MAAM,EAAEZ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIU,SAAS,CAACX,CAAC,CAAC,GAAGW,SAAS,CAACX,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAACjB,CAAC,EAAEG,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEE,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACI,wBAAwB,CAACL,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASe,eAAeA,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGkB,cAAc,CAAClB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAE;IAAEmB,KAAK,EAAElB,CAAC;IAAEM,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGtB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASmB,cAAcA,CAACjB,CAAC,EAAE;EAAE,IAAIqB,CAAC,GAAGC,YAAY,CAACtB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIT,OAAO,CAAC8B,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACtB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIR,OAAO,CAACS,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACP,MAAM,CAAC8B,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKzB,CAAC,EAAE;IAAE,IAAIuB,CAAC,GAAGvB,CAAC,CAAC0B,IAAI,CAACxB,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIR,OAAO,CAAC8B,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK1B,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE3B,CAAC,CAAC;AAAE;AAC3T;AACA;;AAEA,OAAO4B,QAAQ,MAAM,qBAAqB;;AAE1C;AACA;AACA;AACA,IAAIC,YAAY,GAAGH,MAAM,CAAC9B,SAAS,CAACkC,WAAW,GAAG,UAAUC,KAAK,EAAEC,QAAQ,EAAE;EAC3E,OAAOD,KAAK,CAACD,WAAW,CAACE,QAAQ,CAAC;AACpC,CAAC,GAAG,UAAUD,KAAK,EAAEC,QAAQ,EAAE;EAC7B,OAAO,CAACD,KAAK,CAACE,UAAU,CAACD,QAAQ,CAAC,GAAG,MAAM,IAAI,KAAK,GAAGD,KAAK,CAACE,UAAU,CAACD,QAAQ,GAAG,CAAC,CAAC,GAAG,MAAM,GAAG,OAAO;AAC1G,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,SAAS,EAAEC,WAAW,EAAEC,aAAa,EAAE;EAC1FD,WAAW,CAACE,SAAS,GAAG,CAAC;EACzB,IAAIC,YAAY,GAAGH,WAAW,CAACI,IAAI,CAACL,SAAS,CAAC;EAC9C,IAAIM,aAAa;EACjB,IAAIF,YAAY,EAAE;IAChBE,aAAa,GAAG,EAAE;IAClB,IAAIC,gBAAgB,GAAG,CAAC;IACxB,GAAG;MACD,IAAIA,gBAAgB,KAAKH,YAAY,CAACI,KAAK,EAAE;QAC3CF,aAAa,IAAIN,SAAS,CAACS,SAAS,CAACF,gBAAgB,EAAEH,YAAY,CAACI,KAAK,CAAC;MAC5E;MACA,IAAIE,YAAY,GAAGN,YAAY,CAAC,CAAC,CAAC;MAClCE,aAAa,IAAIJ,aAAa,CAACQ,YAAY,CAAC;MAC5CH,gBAAgB,GAAGH,YAAY,CAACI,KAAK,GAAGE,YAAY,CAAClC,MAAM;MAC3D;IACF,CAAC,QAAQ4B,YAAY,GAAGH,WAAW,CAACI,IAAI,CAACL,SAAS,CAAC;IACnD,IAAIO,gBAAgB,KAAKP,SAAS,CAACxB,MAAM,EAAE;MACzC8B,aAAa,IAAIN,SAAS,CAACS,SAAS,CAACF,gBAAgB,CAAC;IACxD;EACF,CAAC,MAAM;IACLD,aAAa,GAAGN,SAAS;EAC3B;EACA,OAAOM,aAAa;AACtB,CAAC;AACD,IAAIK,UAAU,GAAG;EACf,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE,QAAQ;EACb,GAAG,EAAE;AACP,CAAC;;AAED;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAI,CAACA,IAAI,EAAE;IACT,OAAO,EAAE;EACX;EACA,OAAOd,kBAAkB,CAACc,IAAI,EAAE,UAAU,EAAE,UAAUjB,KAAK,EAAE;IAC3D,IAAIkB,MAAM,GAAGH,UAAU,CAACf,KAAK,CAAC;IAC9B,IAAI,CAACkB,MAAM,EAAE;MACX,IAAIC,IAAI,GAAGnB,KAAK,CAACpB,MAAM,GAAG,CAAC,GAAGkB,YAAY,CAACE,KAAK,EAAE,CAAC,CAAC,GAAGA,KAAK,CAACE,UAAU,CAAC,CAAC,CAAC;MAC1EgB,MAAM,GAAG,IAAI,CAACE,MAAM,CAACD,IAAI,EAAE,GAAG,CAAC;IACjC;IACA,OAAOD,MAAM;EACf,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,aAAaA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAClC,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;IACtBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,OAAO,GAAGJ,IAAI,CAACI,OAAO;EACxB,IAAIC,OAAO,GAAGJ,KAAK,CAACI,OAAO;EAC3B,IAAIC,YAAY,GAAGF,OAAO;EAC1B,IAAIG,cAAc,GAAGJ,OAAO;EAC5B,OAAO;IACLK,IAAI,EAAE,SAASA,IAAIA,CAACC,KAAK,EAAE;MACzB,IAAIC,cAAc,GAAGR,MAAM,CAACI,YAAY,CAAC,CAACK,EAAE;MAC5C,IAAIC,gBAAgB,GAAGF,cAAc,IAAIA,cAAc,CAACD,KAAK,CAACI,IAAI,CAAC;MACnE,IAAID,gBAAgB,EAAE;QACpBN,YAAY,GAAGM,gBAAgB,CAACE,MAAM;QACtC,IAAIF,gBAAgB,CAACP,OAAO,EAAE;UAC5BO,gBAAgB,CAACP,OAAO,CAAC9C,OAAO,CAAC,UAAUwD,OAAO,EAAE;YAClD,IAAIC,UAAU,GAAGX,OAAO,CAACU,OAAO,CAAC;YACjC,IAAIE,gBAAgB,GAAGD,UAAU,IAAIA,UAAU,CAACT,cAAc,EAAEE,KAAK,CAAC;YACtE,IAAIQ,gBAAgB,EAAE;cACpBV,cAAc,GAAGnD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmD,cAAc,CAAC,EAAEU,gBAAgB,CAAC;YACrF;UACF,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,OAAO,EAAE;EAChE,IAAIC,WAAW,GAAGD,OAAO,CAACC,WAAW;IACnCC,WAAW,GAAGF,OAAO,CAACE,WAAW;EACnC,OAAOtB,aAAa,CAAC;IACnBK,OAAO,EAAE,QAAQ;IACjBD,OAAO,EAAE;MACPmB,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE;IACjB,CAAC;IACDtB,MAAM,EAAE;MACNuB,MAAM,EAAE;QACNd,EAAE,EAAE;UACFe,WAAW,EAAE;YACXZ,MAAM,EAAE,mBAAmB;YAC3BT,OAAO,EAAE,CAAC,aAAa,EAAE,aAAa;UACxC,CAAC;UACDsB,aAAa,EAAE;YACbb,MAAM,EAAE,qBAAqB;YAC7BT,OAAO,EAAE,CAAC,aAAa,EAAE,aAAa;UACxC;QACF;MACF,CAAC;MACDuB,iBAAiB,EAAE;QACjBjB,EAAE,EAAE;UACFkB,OAAO,EAAE;YACPf,MAAM,EAAE,QAAQ;YAChBT,OAAO,EAAE,CAAC,iBAAiB,EAAE,aAAa;UAC5C,CAAC;UACDqB,WAAW,EAAE;YACXZ,MAAM,EAAE,mBAAmB;YAC3BT,OAAO,EAAE,CAAC,gBAAgB,EAAE,aAAa;UAC3C;QACF;MACF,CAAC;MACDyB,mBAAmB,EAAE;QACnBnB,EAAE,EAAE;UACFkB,OAAO,EAAE;YACPf,MAAM,EAAE,QAAQ;YAChBT,OAAO,EAAE,CAAC,iBAAiB,EAAE,aAAa;UAC5C,CAAC;UACDsB,aAAa,EAAE;YACbb,MAAM,EAAE,qBAAqB;YAC7BT,OAAO,EAAE,CAAC,gBAAgB,EAAE,aAAa;UAC3C,CAAC;UACDqB,WAAW,EAAE;YACXZ,MAAM,EAAE,mBAAmB;YAC3BT,OAAO,EAAE,CAAC,aAAa,EAAE,aAAa;UACxC;QACF;MACF;IACF;EACF,CAAC,EAAE;IACDA,OAAO,EAAE;MACP0B,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;QAC1C,OAAO;UACLR,QAAQ,EAAE,EAAE;UACZD,KAAK,EAAE,OAAO;UACdE,aAAa,EAAE;QACjB,CAAC;MACH,CAAC;MACDQ,cAAc,EAAE,SAASA,cAAcA,CAAC7B,OAAO,EAAEM,KAAK,EAAE;QACtD,OAAO;UACLc,QAAQ,EAAEpB,OAAO,CAACoB,QAAQ,CAACzB,MAAM,CAACW,KAAK,CAACc,QAAQ,CAAC;UACjDD,KAAK,EAAEb,KAAK,CAACa,KAAK,IAAInB,OAAO,CAACmB,KAAK;UACnCE,aAAa,EAAEf,KAAK,CAACI,IAAI,KAAK,eAAe,GAAG,SAAS,GAAG;QAC9D,CAAC;MACH,CAAC;MACDoB,WAAW,EAAE,SAASA,WAAWA,CAAC9B,OAAO,EAAEM,KAAK,EAAE;QAChD,OAAO;UACLc,QAAQ,EAAEd,KAAK,CAACc,QAAQ;UACxBD,KAAK,EAAEb,KAAK,CAACa,KAAK,IAAInB,OAAO,CAACmB,KAAK;UACnCE,aAAa,EAAEf,KAAK,CAACI,IAAI,KAAK,eAAe,GAAG,SAAS,GAAG;QAC9D,CAAC;MACH,CAAC;MACDO,WAAW,EAAEA,WAAW;MACxBC,WAAW,EAAEA;IACf;EACF,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAIa,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAE;EAC1D,IAAI,CAACA,KAAK,IAAI,EAAEA,KAAK,YAAYC,KAAK,CAAC,EAAE;IACvC,MAAM,IAAIA,KAAK,CAAC,yCAAyC,CAAC;EAC5D;EACA,IAAI,OAAOD,KAAK,CAACE,KAAK,KAAK,QAAQ,EAAE;IACnC,OAAOF,KAAK,CAACE,KAAK,CAACC,KAAK,CAAC,IAAI,CAAC,CAACvF,MAAM,CAAC,UAAUsF,KAAK,EAAE;MACrD,OAAOA,KAAK,KAAK,SAAS,CAACvC,MAAM,CAACqC,KAAK,CAACI,OAAO,CAAC;IAClD,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,QAAQ,EAAE;EACjEC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEF,QAAQ,CAAC;EAC1C,OAAO,SAASG,OAAOA,CAAA,EAAG;IACxBF,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAEJ,QAAQ,CAAC;EAC/C,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAIK,0BAA0B,GAAG,SAASA,0BAA0BA,CAACL,QAAQ,EAAE;EAC7EC,MAAM,CAACC,gBAAgB,CAAC,oBAAoB,EAAEF,QAAQ,CAAC;EACvD,OAAO,SAASG,OAAOA,CAAA,EAAG;IACxBF,MAAM,CAACG,mBAAmB,CAAC,oBAAoB,EAAEJ,QAAQ,CAAC;EAC5D,CAAC;AACH,CAAC;;AAED;;AAEA,IAAIM,SAAS,GAAG;EACdZ,KAAK,EAAE;IACLa,eAAe,EAAE,wBAAwB;IACzCC,KAAK,EAAE;EACT,CAAC;EACDC,OAAO,EAAE;IACPF,eAAe,EAAE,0BAA0B;IAC3CC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,WAAW,GAAG;EAChBxE,QAAQ,EAAE,OAAO;EACjByE,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,OAAO;EACfC,MAAM,EAAE,MAAM;EACd,SAAS,EAAE;AACb,CAAC;AACD,IAAIC,cAAc,GAAG;EACnBhF,QAAQ,EAAE,OAAO;EACjBiF,SAAS,EAAE,YAAY;EACvBP,IAAI,EAAE,CAAC;EACPD,GAAG,EAAE,CAAC;EACNE,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,OAAO;EACfI,QAAQ,EAAE,OAAO;EACjBC,OAAO,EAAE,qBAAqB;EAC9BC,UAAU,EAAE,KAAK;EACjBC,UAAU,EAAE,UAAU;EACtBC,QAAQ,EAAE,MAAM;EAChBjB,eAAe,EAAE,oBAAoB;EACrCC,KAAK,EAAE;AACT,CAAC;AACD,IAAIiB,WAAW,GAAG;EAChBjB,KAAK,EAAE,SAAS;EAChBY,QAAQ,EAAE,KAAK;EACfG,UAAU,EAAE,UAAU;EACtBG,UAAU,EAAE,YAAY;EACxBC,MAAM,EAAE,eAAe;EACvBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAE,KAAK;EAChBL,QAAQ,EAAE;AACZ,CAAC;AACD,IAAIM,kBAAkB,GAAG;EACvBtB,KAAK,EAAE,SAAS;EAChBc,UAAU,EAAE,MAAM;EAClBF,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,MAAM;EACfU,MAAM,EAAE,SAAS;EACjB7F,QAAQ,EAAE,UAAU;EACpB2E,KAAK,EAAE,CAAC;EACRF,GAAG,EAAE,CAAC;EACNJ,eAAe,EAAE,aAAa;EAC9BU,MAAM,EAAE;AACV,CAAC;AACD,IAAIe,YAAY,GAAG;EACjBxB,KAAK,EAAE,SAAS;EAChBY,QAAQ,EAAE,OAAO;EACjBa,YAAY,EAAE,MAAM;EACpBP,UAAU,EAAE;AACd,CAAC;AACD,IAAIQ,YAAY,GAAG;EACjBZ,UAAU,EAAE,KAAK;EACjBF,QAAQ,EAAE,MAAM;EAChBM,UAAU,EAAE;AACd,CAAC;;AAED;;AAEA,IAAIS,MAAM,GAAG;EACXC,KAAK,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;EACrCC,KAAK,EAAE,QAAQ;EACfC,GAAG,EAAE,QAAQ;EACbC,KAAK,EAAE,QAAQ;EACfC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,QAAQ;EACdC,OAAO,EAAE,QAAQ;EACjBC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,QAAQ;EACnBC,QAAQ,EAAE;AACZ,CAAC;AACD/G,QAAQ,CAACgH,SAAS,CAACX,MAAM,CAAC;;AAE1B;AACA;AACA;AACA;AACA;AACA,IAAIY,aAAa,GAAG,SAASA,aAAaA,CAAC3E,IAAI,EAAE4E,IAAI,EAAE;EACrD,IAAIC,MAAM,GAAG7E,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,OAAO;EACrD,IAAI8E,IAAI,GAAG,EAAE;EACb,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC5BE,IAAI,IAAIF,IAAI;EACd,CAAC,MAAM;IACL,IAAIG,IAAI,GAAGH,IAAI,CAACG,IAAI,IAAI,EAAE;IAC1B;IACA,IAAIC,UAAU,GAAGJ,IAAI,CAACI,UAAU,GAAGJ,IAAI,CAACI,UAAU,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAChG,MAAM,CAAC2F,IAAI,CAACI,UAAU,CAACE,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAACjG,MAAM,CAAC2F,IAAI,CAACI,UAAU,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC/F,MAAM,CAAC2F,IAAI,CAACI,UAAU,CAAC,GAAG,EAAE;IAClM,IAAIG,GAAG,GAAGP,IAAI,CAACO,GAAG;IAClBN,MAAM,IAAI,EAAE,CAAC5F,MAAM,CAAC+F,UAAU,IAAID,IAAI,GAAG,MAAM,CAAC9F,MAAM,CAAC+F,UAAU,GAAG,EAAE,CAAC/F,MAAM,CAAC+F,UAAU,CAAC,CAAC/F,MAAM,CAAC8F,IAAI,GAAG,IAAI,CAAC9F,MAAM,CAAC8F,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC,GAAGA,IAAI,CAAC,CAAC9F,MAAM,CAACkG,GAAG,GAAG,GAAG,CAAClG,MAAM,CAACkG,GAAG,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;IACrLL,IAAI,IAAIF,IAAI,CAAClD,OAAO,IAAI,EAAE;EAC5B;EACA,IAAI0D,KAAK,CAACC,OAAO,CAACT,IAAI,CAACpD,KAAK,CAAC,EAAE;IAC7BoD,IAAI,CAACpD,KAAK,CAAC9E,OAAO,CAAC,UAAU8E,KAAK,EAAE;MAClC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7BsD,IAAI,IAAI,MAAM,CAAC7F,MAAM,CAACuC,KAAK,CAAC;MAC9B;IACF,CAAC,CAAC;EACJ;EACA,OAAO;IACLqD,MAAM,EAAEA,MAAM;IACdC,IAAI,EAAEA;EACR,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,IAAIQ,aAAa,GAAG,SAASA,aAAaA,CAAChF,OAAO,EAAE;EAClD;EACA,IAAIiF,sBAAsB;EAC1B;EACA,IAAIC,gBAAgB;EACpB;EACA,IAAIC,aAAa;EACjB;EACA,IAAIC,WAAW,GAAG,EAAE;EACpB;EACA,IAAIC,yBAAyB;;EAE7B;AACF;AACA;AACA;AACA;EACE,SAASC,UAAUA,CAACC,OAAO,EAAEC,KAAK,EAAE;IAClC/J,MAAM,CAACC,IAAI,CAAC8J,KAAK,CAAC,CAACpJ,OAAO,CAAC,UAAUqJ,IAAI,EAAE;MACzCF,OAAO,CAACC,KAAK,CAACC,IAAI,CAAC,GAAGD,KAAK,CAACC,IAAI,CAAC;IACnC,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;EACE,SAASC,eAAeA,CAACC,sBAAsB,EAAE;IAC/C;IACA,IAAIpE,MAAM,CAACqE,YAAY,EAAE;MACvBP,yBAAyB,GAAG9D,MAAM,CAACqE,YAAY,CAACC,YAAY,CAACF,sBAAsB,IAAI,4BAA4B,EAAE;QACnHG,UAAU,EAAE,SAASA,UAAUA,CAACpJ,KAAK,EAAE;UACrC,OAAOA,KAAK;QACd;MACF,CAAC,CAAC;IACJ;IACAuI,sBAAsB,GAAGc,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IACzDf,sBAAsB,CAACgB,EAAE,GAAG,mCAAmC;IAC/DhB,sBAAsB,CAACiB,GAAG,GAAG,aAAa;IAC1CZ,UAAU,CAACL,sBAAsB,EAAEjD,WAAW,CAAC;IAC/CiD,sBAAsB,CAACkB,MAAM,GAAG,YAAY;MAC1C,IAAIC,cAAc,GAAG;MACrB,CAAC;MACDnB,sBAAsB,CAACoB,eAAe,EAAEL,aAAa,CAAC,KAAK,CAAC;MAC5Dd,gBAAgB,GAAG;MACnB,CAAC;MACDD,sBAAsB,CAACoB,eAAe,EAAEL,aAAa,CAAC,KAAK,CAAC;MAC5DI,cAAc,CAACH,EAAE,GAAG,uCAAuC;MAC3DX,UAAU,CAACc,cAAc,EAAE5D,cAAc,CAAC;MAC1C2C,aAAa,GAAGY,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAC7Cb,aAAa,CAACmB,SAAS,GAAG,yBAAyB;MACnDhB,UAAU,CAACH,aAAa,EAAEpC,WAAW,CAAC;MACtC,IAAIwD,kBAAkB,GAAGR,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MACzDV,UAAU,CAACiB,kBAAkB,EAAEnD,kBAAkB,CAAC;MAClDmD,kBAAkB,CAACD,SAAS,GAAG,GAAG;MAClCC,kBAAkB,CAACC,SAAS,GAAG,SAAS;MACxCD,kBAAkB,CAAC/E,gBAAgB,CAAC,OAAO,EAAE,YAAY;QACvD;QACAiF,cAAc,CAACpH,IAAI,CAAC;UAClBK,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,CAAC;MACF0G,cAAc,CAACM,WAAW,CAACvB,aAAa,CAAC;MACzCiB,cAAc,CAACM,WAAW,CAACH,kBAAkB,CAAC;MAC9CH,cAAc,CAACM,WAAW,CAACxB,gBAAgB,CAAC;;MAE5C;MACA,CAAC;MACDD,sBAAsB,CAACoB,eAAe,EAAE7B,IAAI,CAACkC,WAAW,CAACN,cAAc,CAAC;MACxEhB,WAAW,CAAChJ,OAAO,CAAC,UAAUuK,MAAM,EAAE;QACpCA,MAAM,CAAC,6BAA6BP,cAAc,CAAC;MACrD,CAAC,CAAC;MACFhB,WAAW,GAAG,EAAE;;MAEhB;MACAH,sBAAsB,CAACkB,MAAM,GAAG,IAAI;IACtC,CAAC;IACDJ,QAAQ,CAACvB,IAAI,CAACkC,WAAW,CAACzB,sBAAsB,CAAC;EACnD;;EAEA;AACF;AACA;AACA;EACE,SAAS2B,mBAAmBA,CAACtF,QAAQ,EAAEqE,sBAAsB,EAAE;IAC7D,IAAIT,gBAAgB,EAAE;MACpBA,gBAAgB,CAAC2B,SAAS,GAAGxB,yBAAyB,GAAGA,yBAAyB,CAACS,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE;MACtG;MACAxE,QAAQ,CAAC4D,gBAAgB,CAAC;MAC1B;IACF;IACAE,WAAW,CAACrJ,IAAI,CAACuF,QAAQ,CAAC;IAC1B,IAAI2D,sBAAsB,EAAE;MAC1B;IACF;IACAS,eAAe,CAACC,sBAAsB,CAAC;EACzC;;EAEA;EACA,SAASmB,IAAIA,CAAA,EAAG;IACd,IAAI,CAAC7B,sBAAsB,EAAE;MAC3B;IACF;;IAEA;IACAc,QAAQ,CAACvB,IAAI,CAACuC,WAAW,CAAC9B,sBAAsB,CAAC;IACjDA,sBAAsB,GAAG,IAAI;IAC7BC,gBAAgB,GAAG,IAAI;EACzB;;EAEA;EACA;AACF;AACA;AACA;AACA;AACA;EACE,SAAS8B,IAAIA,CAACtH,IAAI,EAAEU,QAAQ,EAAEuF,sBAAsB,EAAEtF,aAAa,EAAE;IACnEuG,mBAAmB,CAAC,YAAY;MAC9BzB,aAAa,CAACmB,SAAS,GAAGjG,aAAa,KAAK,SAAS,GAAG,0BAA0B,GAAG,yBAAyB;MAC9GD,QAAQ,CAAChE,OAAO,CAAC,UAAUgF,OAAO,EAAE;QAClC,IAAI6F,YAAY,GAAGlB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAChD,IAAIkB,QAAQ,GAAGxH,IAAI,KAAK,SAAS,GAAGkC,SAAS,CAACG,OAAO,GAAGH,SAAS,CAACZ,KAAK;QACvEsE,UAAU,CAAC2B,YAAY,EAAEhL,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiL,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;UACtEvE,OAAO,EAAE;QACX,CAAC,CAAC,CAAC;QACH,IAAIwE,WAAW,GAAGpB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC/C,IAAIoB,cAAc,GAAG/C,aAAa,CAAC3E,IAAI,EAAE0B,OAAO,CAAC;UAC/CmD,MAAM,GAAG6C,cAAc,CAAC7C,MAAM;UAC9BC,IAAI,GAAG4C,cAAc,CAAC5C,IAAI;QAC5B2C,WAAW,CAACb,SAAS,GAAG/B,MAAM;QAC9Be,UAAU,CAAC6B,WAAW,EAAE7D,YAAY,CAAC;QACrC,IAAIlC,OAAO,CAACiG,gBAAgB,EAAE;UAC5B/B,UAAU,CAAC6B,WAAW,EAAE;YACtB9D,MAAM,EAAE;UACV,CAAC,CAAC;UACF;UACA8D,WAAW,CAACG,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC;UAC/CH,WAAW,CAAC3F,gBAAgB,CAAC,OAAO,EAAE,YAAY;YAChD+F,KAAK,CAAC,2CAA2C,CAAC5I,MAAM,CAACyC,OAAO,CAACiG,gBAAgB,CAAC,CAAC;UACrF,CAAC,CAAC;QACJ;;QAEA;QACA,IAAI7I,IAAI,GAAGpB,QAAQ,CAACmB,MAAM,CAACiG,IAAI,CAAC,CAAC;QACjC,IAAIgD,eAAe,GAAGzB,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QACnDV,UAAU,CAACkC,eAAe,EAAEhE,YAAY,CAAC;QACzCgE,eAAe,CAACX,SAAS,GAAGxB,yBAAyB,GAAGA,yBAAyB,CAACS,UAAU,CAACtH,IAAI,CAAC,GAAGA,IAAI;QACzGyI,YAAY,CAACP,WAAW,CAACS,WAAW,CAAC;QACrCF,YAAY,CAACP,WAAW,CAACc,eAAe,CAAC;;QAEzC;QACAtC,gBAAgB,CAACwB,WAAW,CAACO,YAAY,CAAC;MAC5C,CAAC,CAAC;IACJ,CAAC,EAAEtB,sBAAsB,CAAC;EAC5B;EACA,IAAIc,cAAc,GAAG1G,oBAAoB,CAAC;IACxCG,WAAW,EAAE,SAASA,WAAWA,CAACuH,KAAK,EAAE;MACvC,IAAIC,WAAW,GAAGD,KAAK,CAACtH,KAAK;QAC3BA,KAAK,GAAGuH,WAAW,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,WAAW;QACtDtH,QAAQ,GAAGqH,KAAK,CAACrH,QAAQ;QACzBC,aAAa,GAAGoH,KAAK,CAACpH,aAAa;MACrC,OAAO2G,IAAI,CAAC7G,KAAK,EAAEC,QAAQ,EAAEJ,OAAO,CAAC2F,sBAAsB,EAAEtF,aAAa,CAAC;IAC7E,CAAC;IACDJ,WAAW,EAAE6G;EACf,CAAC,CAAC;EACF,IAAI9G,OAAO,CAAC2H,iBAAiB,EAAE;IAC7B;AACJ;AACA;AACA;IACI,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAAC5G,KAAK,EAAE6G,eAAe,EAAE;MAC7D,IAAIC,WAAW,GAAG9G,KAAK,YAAYC,KAAK,GAAGD,KAAK,GAAG,IAAIC,KAAK,CAACD,KAAK,IAAI6G,eAAe,CAAC;MACtF,IAAIE,aAAa,GAAG,OAAO/H,OAAO,CAAC2H,iBAAiB,KAAK,UAAU,GAAG3H,OAAO,CAAC2H,iBAAiB,CAACG,WAAW,CAAC,GAAG,IAAI;MACnH,IAAIC,aAAa,EAAE;QACjBtB,cAAc,CAACpH,IAAI,CAAC;UAClBK,IAAI,EAAE,eAAe;UACrBU,QAAQ,EAAE,CAAC;YACTgB,OAAO,EAAE0G,WAAW,CAAC1G,OAAO;YAC5BF,KAAK,EAAEH,kBAAkB,CAAC+G,WAAW;UACvC,CAAC;QACH,CAAC,CAAC;MACJ;IACF,CAAC;IACDzG,oBAAoB,CAAC,UAAU2G,UAAU,EAAE;MACzC;MACA,IAAIhH,KAAK,GAAGgH,UAAU,CAAChH,KAAK;QAC1BI,OAAO,GAAG4G,UAAU,CAAC5G,OAAO;MAC9B,IAAI,CAACJ,KAAK,IAAI,CAACI,OAAO,EAAE;QACtB;MACF;;MAEA;MACA,IAAIJ,KAAK,IAAIA,KAAK,CAACE,KAAK,IAAIF,KAAK,CAACE,KAAK,CAAC+G,QAAQ,CAAC,0BAA0B,CAAC,EAAE;QAC5E;MACF;MACAL,WAAW,CAAC5G,KAAK,EAAEI,OAAO,CAAC;IAC7B,CAAC,CAAC;IACFO,0BAA0B,CAAC,UAAUuG,qBAAqB,EAAE;MAC1D,IAAIC,MAAM,GAAGD,qBAAqB,CAACC,MAAM;MACzCP,WAAW,CAACO,MAAM,EAAE,kCAAkC,CAAC;IACzD,CAAC,CAAC;EACJ;EACA,OAAO1B,cAAc;AACvB,CAAC;AACD,SAASpC,aAAa,EAAEW,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}