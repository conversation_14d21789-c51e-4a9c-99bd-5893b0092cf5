{"ast": null, "code": "import { Observable } from '../Observable';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from '../util/isFunction';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleIterable(input, scheduler) {\n  return new Observable(subscriber => {\n    let iterator;\n    executeSchedule(subscriber, scheduler, () => {\n      iterator = input[Symbol_iterator]();\n      executeSchedule(subscriber, scheduler, () => {\n        let value;\n        let done;\n        try {\n          ({\n            value,\n            done\n          } = iterator.next());\n        } catch (err) {\n          subscriber.error(err);\n          return;\n        }\n        if (done) {\n          subscriber.complete();\n        } else {\n          subscriber.next(value);\n        }\n      }, 0, true);\n    });\n    return () => isFunction(iterator === null || iterator === void 0 ? void 0 : iterator.return) && iterator.return();\n  });\n}", "map": {"version": 3, "names": ["Observable", "iterator", "Symbol_iterator", "isFunction", "executeSchedule", "scheduleIterable", "input", "scheduler", "subscriber", "value", "done", "next", "err", "error", "complete", "return"], "sources": ["/var/www/html/trading-app/frontend/node_modules/rxjs/dist/esm/internal/scheduled/scheduleIterable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { iterator as Symbol_iterator } from '../symbol/iterator';\nimport { isFunction } from '../util/isFunction';\nimport { executeSchedule } from '../util/executeSchedule';\nexport function scheduleIterable(input, scheduler) {\n    return new Observable((subscriber) => {\n        let iterator;\n        executeSchedule(subscriber, scheduler, () => {\n            iterator = input[Symbol_iterator]();\n            executeSchedule(subscriber, scheduler, () => {\n                let value;\n                let done;\n                try {\n                    ({ value, done } = iterator.next());\n                }\n                catch (err) {\n                    subscriber.error(err);\n                    return;\n                }\n                if (done) {\n                    subscriber.complete();\n                }\n                else {\n                    subscriber.next(value);\n                }\n            }, 0, true);\n        });\n        return () => isFunction(iterator === null || iterator === void 0 ? void 0 : iterator.return) && iterator.return();\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,QAAQ,IAAIC,eAAe,QAAQ,oBAAoB;AAChE,SAASC,UAAU,QAAQ,oBAAoB;AAC/C,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAO,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC/C,OAAO,IAAIP,UAAU,CAAEQ,UAAU,IAAK;IAClC,IAAIP,QAAQ;IACZG,eAAe,CAACI,UAAU,EAAED,SAAS,EAAE,MAAM;MACzCN,QAAQ,GAAGK,KAAK,CAACJ,eAAe,CAAC,CAAC,CAAC;MACnCE,eAAe,CAACI,UAAU,EAAED,SAAS,EAAE,MAAM;QACzC,IAAIE,KAAK;QACT,IAAIC,IAAI;QACR,IAAI;UACA,CAAC;YAAED,KAAK;YAAEC;UAAK,CAAC,GAAGT,QAAQ,CAACU,IAAI,CAAC,CAAC;QACtC,CAAC,CACD,OAAOC,GAAG,EAAE;UACRJ,UAAU,CAACK,KAAK,CAACD,GAAG,CAAC;UACrB;QACJ;QACA,IAAIF,IAAI,EAAE;UACNF,UAAU,CAACM,QAAQ,CAAC,CAAC;QACzB,CAAC,MACI;UACDN,UAAU,CAACG,IAAI,CAACF,KAAK,CAAC;QAC1B;MACJ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACf,CAAC,CAAC;IACF,OAAO,MAAMN,UAAU,CAACF,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACc,MAAM,CAAC,IAAId,QAAQ,CAACc,MAAM,CAAC,CAAC;EACrH,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}