{"ast": null, "code": "import _asyncToGenerator from \"/var/www/html/trading-app/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/indian-stocks.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nconst _forTrack0 = ($index, $item) => $item.symbol;\nfunction IndianStocksComponent_Conditional_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 5);\n  }\n}\nfunction IndianStocksComponent_Conditional_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 33);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.topGainer.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.topGainer.symbol);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"+\", ctx_r0.topGainer.changePercent.toFixed(2), \"%\");\n  }\n}\nfunction IndianStocksComponent_Conditional_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"p\", 12);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 34);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.topLoser.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.topLoser.symbol);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r0.topLoser.changePercent.toFixed(2), \"%\");\n  }\n}\nfunction IndianStocksComponent_For_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\", 19);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const sector_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", sector_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(sector_r2);\n  }\n}\nfunction IndianStocksComponent_For_89_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 30)(1, \"td\")(2, \"div\")(3, \"div\", 35);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 36);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"td\")(8, \"span\", 37);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"td\", 12);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"td\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"td\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"td\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"td\");\n    i0.ɵɵtext(19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"td\")(21, \"button\", 38);\n    i0.ɵɵtext(22, \"Trade\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const stock_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(stock_r3.symbol);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stock_r3.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", stock_r3.sector, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", stock_r3.price.toFixed(2), \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(stock_r3.change >= 0 ? \"text-green-600\" : \"text-red-600\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", stock_r3.change >= 0 ? \"+\" : \"\", \"\\u20B9\", stock_r3.change.toFixed(2), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(stock_r3.changePercent >= 0 ? \"text-green-600\" : \"text-red-600\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", stock_r3.changePercent >= 0 ? \"+\" : \"\", \"\", stock_r3.changePercent.toFixed(2), \"% \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.formatVolume(stock_r3.volume));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", stock_r3.marketCap == null ? null : stock_r3.marketCap.toLocaleString(), \"\");\n  }\n}\nfunction IndianStocksComponent_ForEmpty_90_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\", 39);\n    i0.ɵɵtext(2, \" No stocks found matching your criteria \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class IndianStocksComponent {\n  constructor(indianStocksService) {\n    this.indianStocksService = indianStocksService;\n    this.stocks = [];\n    this.filteredStocks = [];\n    this.availableSectors = [];\n    this.selectedSector = '';\n    this.searchTerm = '';\n    this.sortBy = 'name';\n    this.loading = false;\n    this.lastUpdated = new Date();\n  }\n  ngOnInit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.loadStocks();\n    })();\n  }\n  loadStocks() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.loading = true;\n      try {\n        _this2.stocks = yield _this2.indianStocksService.getIndianStocks();\n        _this2.availableSectors = _this2.indianStocksService.getAvailableSectors(_this2.stocks);\n        _this2.updateMarketSummary();\n        _this2.filterStocks();\n        _this2.lastUpdated = new Date();\n      } catch (error) {\n        console.error('Error loading stocks:', error);\n      } finally {\n        _this2.loading = false;\n      }\n    })();\n  }\n  refreshData() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      yield _this3.loadStocks();\n    })();\n  }\n  filterStocks() {\n    let filtered = [...this.stocks];\n    // Filter by sector\n    if (this.selectedSector) {\n      filtered = this.indianStocksService.getStocksBySector(filtered, this.selectedSector);\n    }\n    // Filter by search term\n    if (this.searchTerm) {\n      const term = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(stock => stock.name.toLowerCase().includes(term) || stock.symbol.toLowerCase().includes(term));\n    }\n    this.filteredStocks = filtered;\n    this.sortStocks();\n  }\n  sortStocks() {\n    this.filteredStocks.sort((a, b) => {\n      const aValue = a[this.sortBy];\n      const bValue = b[this.sortBy];\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return aValue.localeCompare(bValue);\n      }\n      return bValue - aValue; // Descending order for numbers\n    });\n  }\n  updateMarketSummary() {\n    const gainers = this.indianStocksService.getTopGainers(this.stocks);\n    const losers = this.indianStocksService.getTopLosers(this.stocks);\n    this.topGainer = gainers[0];\n    this.topLoser = losers[0];\n  }\n  getGainersCount() {\n    return this.stocks.filter(stock => stock.changePercent > 0).length;\n  }\n  getLosersCount() {\n    return this.stocks.filter(stock => stock.changePercent < 0).length;\n  }\n  formatVolume(volume) {\n    if (volume >= 10000000) {\n      return (volume / 10000000).toFixed(1) + 'Cr';\n    } else if (volume >= 100000) {\n      return (volume / 100000).toFixed(1) + 'L';\n    } else if (volume >= 1000) {\n      return (volume / 1000).toFixed(1) + 'K';\n    }\n    return volume.toString();\n  }\n  static {\n    this.ɵfac = function IndianStocksComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IndianStocksComponent)(i0.ɵɵdirectiveInject(i1.IndianStocksService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: IndianStocksComponent,\n      selectors: [[\"app-indian-stocks\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 94,\n      vars: 15,\n      consts: [[1, \"space-y-6\"], [1, \"flex\", \"justify-between\", \"items-center\"], [1, \"text-2xl\", \"font-bold\", \"text-gray-900\"], [1, \"text-gray-600\", \"mt-1\"], [1, \"btn\", \"btn-primary\", 3, \"click\", \"disabled\"], [1, \"inline-block\", \"animate-spin\", \"rounded-full\", \"h-4\", \"w-4\", \"border-b-2\", \"border-white\", \"mr-2\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-6\"], [1, \"card\"], [1, \"text-lg\", \"font-medium\", \"text-gray-900\", \"mb-2\"], [1, \"space-y-2\"], [1, \"flex\", \"justify-between\"], [1, \"text-gray-600\"], [1, \"font-medium\"], [1, \"font-medium\", \"text-green-600\"], [1, \"font-medium\", \"text-red-600\"], [1, \"flex\", \"flex-wrap\", \"gap-4\", \"items-center\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-gray-700\", \"mb-1\"], [1, \"input\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"\"], [3, \"value\"], [\"type\", \"text\", \"placeholder\", \"Search stocks...\", 1, \"input\", 3, \"ngModelChange\", \"ngModel\"], [\"value\", \"name\"], [\"value\", \"price\"], [\"value\", \"change\"], [\"value\", \"changePercent\"], [\"value\", \"volume\"], [1, \"overflow-x-auto\"], [1, \"table\"], [1, \"bg-gray-50\"], [1, \"divide-y\", \"divide-gray-200\"], [1, \"hover:bg-gray-50\"], [1, \"text-center\", \"text-sm\", \"text-gray-500\"], [1, \"text-sm\", \"text-gray-600\"], [1, \"text-lg\", \"font-bold\", \"text-green-600\"], [1, \"text-lg\", \"font-bold\", \"text-red-600\"], [1, \"font-medium\", \"text-gray-900\"], [1, \"text-sm\", \"text-gray-500\"], [1, \"inline-flex\", \"items-center\", \"px-2.5\", \"py-0.5\", \"rounded-full\", \"text-xs\", \"font-medium\", \"bg-blue-100\", \"text-blue-800\"], [1, \"btn\", \"btn-primary\", \"text-sm\"], [\"colspan\", \"8\", 1, \"text-center\", \"py-8\", \"text-gray-500\"]],\n      template: function IndianStocksComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\")(3, \"h1\", 2);\n          i0.ɵɵtext(4, \"Indian Stocks\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 3);\n          i0.ɵɵtext(6, \"Live prices from Bombay Stock Exchange (BSE)\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function IndianStocksComponent_Template_button_click_7_listener() {\n            return ctx.refreshData();\n          });\n          i0.ɵɵtemplate(8, IndianStocksComponent_Conditional_8_Template, 1, 0, \"span\", 5);\n          i0.ɵɵtext(9, \" Refresh \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 6)(11, \"div\", 7)(12, \"h3\", 8);\n          i0.ɵɵtext(13, \"Market Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"div\", 9)(15, \"div\", 10)(16, \"span\", 11);\n          i0.ɵɵtext(17, \"Total Stocks:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"span\", 12);\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"div\", 10)(21, \"span\", 11);\n          i0.ɵɵtext(22, \"Gainers:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"span\", 13);\n          i0.ɵɵtext(24);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(25, \"div\", 10)(26, \"span\", 11);\n          i0.ɵɵtext(27, \"Losers:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"span\", 14);\n          i0.ɵɵtext(29);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(30, \"div\", 7)(31, \"h3\", 8);\n          i0.ɵɵtext(32, \"Top Gainer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(33, IndianStocksComponent_Conditional_33_Template, 7, 3, \"div\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"div\", 7)(35, \"h3\", 8);\n          i0.ɵɵtext(36, \"Top Loser\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(37, IndianStocksComponent_Conditional_37_Template, 7, 3, \"div\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"div\", 7)(39, \"div\", 15)(40, \"div\")(41, \"label\", 16);\n          i0.ɵɵtext(42, \"Filter by Sector\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"select\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function IndianStocksComponent_Template_select_ngModelChange_43_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedSector, $event) || (ctx.selectedSector = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function IndianStocksComponent_Template_select_ngModelChange_43_listener() {\n            return ctx.filterStocks();\n          });\n          i0.ɵɵelementStart(44, \"option\", 18);\n          i0.ɵɵtext(45, \"All Sectors\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵrepeaterCreate(46, IndianStocksComponent_For_47_Template, 2, 2, \"option\", 19, i0.ɵɵrepeaterTrackByIdentity);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(48, \"div\")(49, \"label\", 16);\n          i0.ɵɵtext(50, \"Search\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"input\", 20);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function IndianStocksComponent_Template_input_ngModelChange_51_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function IndianStocksComponent_Template_input_ngModelChange_51_listener() {\n            return ctx.filterStocks();\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(52, \"div\")(53, \"label\", 16);\n          i0.ɵɵtext(54, \"Sort by\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"select\", 17);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function IndianStocksComponent_Template_select_ngModelChange_55_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.sortBy, $event) || (ctx.sortBy = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"ngModelChange\", function IndianStocksComponent_Template_select_ngModelChange_55_listener() {\n            return ctx.sortStocks();\n          });\n          i0.ɵɵelementStart(56, \"option\", 21);\n          i0.ɵɵtext(57, \"Name\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"option\", 22);\n          i0.ɵɵtext(59, \"Price\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"option\", 23);\n          i0.ɵɵtext(61, \"Change\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"option\", 24);\n          i0.ɵɵtext(63, \"Change %\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"option\", 25);\n          i0.ɵɵtext(65, \"Volume\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(66, \"div\", 7)(67, \"div\", 26)(68, \"table\", 27)(69, \"thead\", 28)(70, \"tr\")(71, \"th\");\n          i0.ɵɵtext(72, \"Stock\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"th\");\n          i0.ɵɵtext(74, \"Sector\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"th\");\n          i0.ɵɵtext(76, \"Price (\\u20B9)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"th\");\n          i0.ɵɵtext(78, \"Change\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"th\");\n          i0.ɵɵtext(80, \"Change %\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(81, \"th\");\n          i0.ɵɵtext(82, \"Volume\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(83, \"th\");\n          i0.ɵɵtext(84, \"Market Cap (Cr)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"th\");\n          i0.ɵɵtext(86, \"Action\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(87, \"tbody\", 29);\n          i0.ɵɵrepeaterCreate(88, IndianStocksComponent_For_89_Template, 23, 14, \"tr\", 30, _forTrack0, false, IndianStocksComponent_ForEmpty_90_Template, 3, 0, \"tr\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(91, \"div\", 31);\n          i0.ɵɵtext(92);\n          i0.ɵɵpipe(93, \"date\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵproperty(\"disabled\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(ctx.loading ? 8 : -1);\n          i0.ɵɵadvance(11);\n          i0.ɵɵtextInterpolate(ctx.stocks.length);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.getGainersCount());\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(ctx.getLosersCount());\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(ctx.topGainer ? 33 : -1);\n          i0.ɵɵadvance(4);\n          i0.ɵɵconditional(ctx.topLoser ? 37 : -1);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.selectedSector);\n          i0.ɵɵadvance(3);\n          i0.ɵɵrepeater(ctx.availableSectors);\n          i0.ɵɵadvance(5);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.sortBy);\n          i0.ɵɵadvance(33);\n          i0.ɵɵrepeater(ctx.filteredStocks);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate1(\" Last updated: \", i0.ɵɵpipeBind2(93, 12, ctx.lastUpdated, \"medium\"), \" \");\n        }\n      },\n      dependencies: [CommonModule, i2.DatePipe, FormsModule, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.NgControlStatus, i3.NgModel],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "FormsModule", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "<PERSON><PERSON><PERSON><PERSON>", "name", "symbol", "ɵɵtextInterpolate1", "changePercent", "toFixed", "topLoser", "ɵɵproperty", "sector_r2", "stock_r3", "sector", "price", "ɵɵclassMap", "change", "ɵɵtextInterpolate2", "formatVolume", "volume", "marketCap", "toLocaleString", "IndianStocksComponent", "constructor", "indianStocksService", "stocks", "filteredStocks", "availableSectors", "selectedSector", "searchTerm", "sortBy", "loading", "lastUpdated", "Date", "ngOnInit", "_this", "_asyncToGenerator", "loadStocks", "_this2", "getIndianStocks", "getAvailableSectors", "updateMarketSummary", "filterStocks", "error", "console", "refreshData", "_this3", "filtered", "getStocksBySector", "term", "toLowerCase", "filter", "stock", "includes", "sortStocks", "sort", "a", "b", "aValue", "bValue", "localeCompare", "gainers", "getTopGainers", "losers", "getTopLosers", "getGainersCount", "length", "getLosersCount", "toString", "ɵɵdirectiveInject", "i1", "IndianStocksService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "IndianStocksComponent_Template", "rf", "ctx", "ɵɵlistener", "IndianStocksComponent_Template_button_click_7_listener", "ɵɵtemplate", "IndianStocksComponent_Conditional_8_Template", "IndianStocksComponent_Conditional_33_Template", "IndianStocksComponent_Conditional_37_Template", "ɵɵtwoWayListener", "IndianStocksComponent_Template_select_ngModelChange_43_listener", "$event", "ɵɵtwoWayBindingSet", "ɵɵrepeaterCreate", "IndianStocksComponent_For_47_Template", "ɵɵrepeaterTrackByIdentity", "IndianStocksComponent_Template_input_ngModelChange_51_listener", "IndianStocksComponent_Template_select_ngModelChange_55_listener", "IndianStocksComponent_For_89_Template", "_forTrack0", "IndianStocksComponent_ForEmpty_90_Template", "ɵɵconditional", "ɵɵtwoWayProperty", "ɵɵrepeater", "ɵɵpipeBind2", "i2", "DatePipe", "i3", "NgSelectOption", "ɵNgSelectMultipleOption", "DefaultValueAccessor", "SelectControlValueAccessor", "NgControlStatus", "NgModel", "encapsulation"], "sources": ["/var/www/html/trading-app/frontend/src/app/pages/indian-stocks/indian-stocks.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { IndianStocksService, IndianStock } from '../../services/indian-stocks.service';\n\n@Component({\n  selector: 'app-indian-stocks',\n  standalone: true,\n  imports: [CommonModule, FormsModule],\n  template: `\n    <div class=\"space-y-6\">\n      <!-- Header -->\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900\">Indian Stocks</h1>\n          <p class=\"text-gray-600 mt-1\">Live prices from Bombay Stock Exchange (BSE)</p>\n        </div>\n        <button \n          (click)=\"refreshData()\" \n          [disabled]=\"loading\"\n          class=\"btn btn-primary\">\n          @if (loading) {\n            <span class=\"inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></span>\n          }\n          Refresh\n        </button>\n      </div>\n\n      <!-- Market Summary -->\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div class=\"card\">\n          <h3 class=\"text-lg font-medium text-gray-900 mb-2\">Market Overview</h3>\n          <div class=\"space-y-2\">\n            <div class=\"flex justify-between\">\n              <span class=\"text-gray-600\">Total Stocks:</span>\n              <span class=\"font-medium\">{{ stocks.length }}</span>\n            </div>\n            <div class=\"flex justify-between\">\n              <span class=\"text-gray-600\">Gainers:</span>\n              <span class=\"font-medium text-green-600\">{{ getGainersCount() }}</span>\n            </div>\n            <div class=\"flex justify-between\">\n              <span class=\"text-gray-600\">Losers:</span>\n              <span class=\"font-medium text-red-600\">{{ getLosersCount() }}</span>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"card\">\n          <h3 class=\"text-lg font-medium text-gray-900 mb-2\">Top Gainer</h3>\n          @if (topGainer) {\n            <div>\n              <p class=\"font-medium\">{{ topGainer.name }}</p>\n              <p class=\"text-sm text-gray-600\">{{ topGainer.symbol }}</p>\n              <p class=\"text-lg font-bold text-green-600\">+{{ topGainer.changePercent.toFixed(2) }}%</p>\n            </div>\n          }\n        </div>\n\n        <div class=\"card\">\n          <h3 class=\"text-lg font-medium text-gray-900 mb-2\">Top Loser</h3>\n          @if (topLoser) {\n            <div>\n              <p class=\"font-medium\">{{ topLoser.name }}</p>\n              <p class=\"text-sm text-gray-600\">{{ topLoser.symbol }}</p>\n              <p class=\"text-lg font-bold text-red-600\">{{ topLoser.changePercent.toFixed(2) }}%</p>\n            </div>\n          }\n        </div>\n      </div>\n\n      <!-- Filters -->\n      <div class=\"card\">\n        <div class=\"flex flex-wrap gap-4 items-center\">\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-1\">Filter by Sector</label>\n            <select [(ngModel)]=\"selectedSector\" (ngModelChange)=\"filterStocks()\" class=\"input\">\n              <option value=\"\">All Sectors</option>\n              @for (sector of availableSectors; track sector) {\n                <option [value]=\"sector\">{{ sector }}</option>\n              }\n            </select>\n          </div>\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-1\">Search</label>\n            <input \n              type=\"text\" \n              [(ngModel)]=\"searchTerm\" \n              (ngModelChange)=\"filterStocks()\"\n              placeholder=\"Search stocks...\" \n              class=\"input\">\n          </div>\n          <div>\n            <label class=\"block text-sm font-medium text-gray-700 mb-1\">Sort by</label>\n            <select [(ngModel)]=\"sortBy\" (ngModelChange)=\"sortStocks()\" class=\"input\">\n              <option value=\"name\">Name</option>\n              <option value=\"price\">Price</option>\n              <option value=\"change\">Change</option>\n              <option value=\"changePercent\">Change %</option>\n              <option value=\"volume\">Volume</option>\n            </select>\n          </div>\n        </div>\n      </div>\n\n      <!-- Stocks Table -->\n      <div class=\"card\">\n        <div class=\"overflow-x-auto\">\n          <table class=\"table\">\n            <thead class=\"bg-gray-50\">\n              <tr>\n                <th>Stock</th>\n                <th>Sector</th>\n                <th>Price (₹)</th>\n                <th>Change</th>\n                <th>Change %</th>\n                <th>Volume</th>\n                <th>Market Cap (Cr)</th>\n                <th>Action</th>\n              </tr>\n            </thead>\n            <tbody class=\"divide-y divide-gray-200\">\n              @for (stock of filteredStocks; track stock.symbol) {\n                <tr class=\"hover:bg-gray-50\">\n                  <td>\n                    <div>\n                      <div class=\"font-medium text-gray-900\">{{ stock.symbol }}</div>\n                      <div class=\"text-sm text-gray-500\">{{ stock.name }}</div>\n                    </div>\n                  </td>\n                  <td>\n                    <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n                      {{ stock.sector }}\n                    </span>\n                  </td>\n                  <td class=\"font-medium\">₹{{ stock.price.toFixed(2) }}</td>\n                  <td [class]=\"stock.change >= 0 ? 'text-green-600' : 'text-red-600'\">\n                    {{ stock.change >= 0 ? '+' : '' }}₹{{ stock.change.toFixed(2) }}\n                  </td>\n                  <td [class]=\"stock.changePercent >= 0 ? 'text-green-600' : 'text-red-600'\">\n                    {{ stock.changePercent >= 0 ? '+' : '' }}{{ stock.changePercent.toFixed(2) }}%\n                  </td>\n                  <td>{{ formatVolume(stock.volume) }}</td>\n                  <td>₹{{ stock.marketCap?.toLocaleString() }}</td>\n                  <td>\n                    <button class=\"btn btn-primary text-sm\">Trade</button>\n                  </td>\n                </tr>\n              } @empty {\n                <tr>\n                  <td colspan=\"8\" class=\"text-center py-8 text-gray-500\">\n                    No stocks found matching your criteria\n                  </td>\n                </tr>\n              }\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      <!-- Last Updated -->\n      <div class=\"text-center text-sm text-gray-500\">\n        Last updated: {{ lastUpdated | date:'medium' }}\n      </div>\n    </div>\n  `\n})\nexport class IndianStocksComponent implements OnInit {\n  stocks: IndianStock[] = [];\n  filteredStocks: IndianStock[] = [];\n  availableSectors: string[] = [];\n  selectedSector = '';\n  searchTerm = '';\n  sortBy = 'name';\n  loading = false;\n  lastUpdated = new Date();\n\n  topGainer?: IndianStock;\n  topLoser?: IndianStock;\n\n  constructor(private indianStocksService: IndianStocksService) {}\n\n  async ngOnInit(): Promise<void> {\n    await this.loadStocks();\n  }\n\n  async loadStocks(): Promise<void> {\n    this.loading = true;\n    try {\n      this.stocks = await this.indianStocksService.getIndianStocks();\n      this.availableSectors = this.indianStocksService.getAvailableSectors(this.stocks);\n      this.updateMarketSummary();\n      this.filterStocks();\n      this.lastUpdated = new Date();\n    } catch (error) {\n      console.error('Error loading stocks:', error);\n    } finally {\n      this.loading = false;\n    }\n  }\n\n  async refreshData(): Promise<void> {\n    await this.loadStocks();\n  }\n\n  filterStocks(): void {\n    let filtered = [...this.stocks];\n\n    // Filter by sector\n    if (this.selectedSector) {\n      filtered = this.indianStocksService.getStocksBySector(filtered, this.selectedSector);\n    }\n\n    // Filter by search term\n    if (this.searchTerm) {\n      const term = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(stock => \n        stock.name.toLowerCase().includes(term) || \n        stock.symbol.toLowerCase().includes(term)\n      );\n    }\n\n    this.filteredStocks = filtered;\n    this.sortStocks();\n  }\n\n  sortStocks(): void {\n    this.filteredStocks.sort((a, b) => {\n      const aValue = a[this.sortBy as keyof IndianStock] as number;\n      const bValue = b[this.sortBy as keyof IndianStock] as number;\n      \n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return aValue.localeCompare(bValue);\n      }\n      \n      return bValue - aValue; // Descending order for numbers\n    });\n  }\n\n  updateMarketSummary(): void {\n    const gainers = this.indianStocksService.getTopGainers(this.stocks);\n    const losers = this.indianStocksService.getTopLosers(this.stocks);\n    \n    this.topGainer = gainers[0];\n    this.topLoser = losers[0];\n  }\n\n  getGainersCount(): number {\n    return this.stocks.filter(stock => stock.changePercent > 0).length;\n  }\n\n  getLosersCount(): number {\n    return this.stocks.filter(stock => stock.changePercent < 0).length;\n  }\n\n  formatVolume(volume: number): string {\n    if (volume >= 10000000) {\n      return (volume / 10000000).toFixed(1) + 'Cr';\n    } else if (volume >= 100000) {\n      return (volume / 100000).toFixed(1) + 'L';\n    } else if (volume >= 1000) {\n      return (volume / 1000).toFixed(1) + 'K';\n    }\n    return volume.toString();\n  }\n}\n"], "mappings": ";AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;;;;;;;;IAoBhCC,EAAA,CAAAC,SAAA,cAAiG;;;;;IA8B/FD,EADF,CAAAE,cAAA,UAAK,YACoB;IAAAF,EAAA,CAAAG,MAAA,GAAoB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC/CJ,EAAA,CAAAE,cAAA,YAAiC;IAAAF,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC3DJ,EAAA,CAAAE,cAAA,YAA4C;IAAAF,EAAA,CAAAG,MAAA,GAA0C;IACxFH,EADwF,CAAAI,YAAA,EAAI,EACtF;;;;IAHmBJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,SAAA,CAAAC,IAAA,CAAoB;IACVT,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,SAAA,CAAAE,MAAA,CAAsB;IACXV,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAW,kBAAA,MAAAJ,MAAA,CAAAC,SAAA,CAAAI,aAAA,CAAAC,OAAA,SAA0C;;;;;IAStFb,EADF,CAAAE,cAAA,UAAK,YACoB;IAAAF,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC9CJ,EAAA,CAAAE,cAAA,YAAiC;IAAAF,EAAA,CAAAG,MAAA,GAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC1DJ,EAAA,CAAAE,cAAA,YAA0C;IAAAF,EAAA,CAAAG,MAAA,GAAwC;IACpFH,EADoF,CAAAI,YAAA,EAAI,EAClF;;;;IAHmBJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAO,QAAA,CAAAL,IAAA,CAAmB;IACTT,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAO,QAAA,CAAAJ,MAAA,CAAqB;IACZV,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAW,kBAAA,KAAAJ,MAAA,CAAAO,QAAA,CAAAF,aAAA,CAAAC,OAAA,SAAwC;;;;;IAchFb,EAAA,CAAAE,cAAA,iBAAyB;IAAAF,EAAA,CAAAG,MAAA,GAAY;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;IAAtCJ,EAAA,CAAAe,UAAA,UAAAC,SAAA,CAAgB;IAAChB,EAAA,CAAAK,SAAA,EAAY;IAAZL,EAAA,CAAAM,iBAAA,CAAAU,SAAA,CAAY;;;;;IA+C/BhB,EAHN,CAAAE,cAAA,aAA6B,SACvB,UACG,cACoC;IAAAF,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC/DJ,EAAA,CAAAE,cAAA,cAAmC;IAAAF,EAAA,CAAAG,MAAA,GAAgB;IAEvDH,EAFuD,CAAAI,YAAA,EAAM,EACrD,EACH;IAEHJ,EADF,CAAAE,cAAA,SAAI,eAC8G;IAC9GF,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACJ;IACLJ,EAAA,CAAAE,cAAA,cAAwB;IAAAF,EAAA,CAAAG,MAAA,IAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC1DJ,EAAA,CAAAE,cAAA,UAAoE;IAClEF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,UAA2E;IACzEF,EAAA,CAAAG,MAAA,IACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAE,cAAA,UAAI;IAAAF,EAAA,CAAAG,MAAA,IAAgC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzCJ,EAAA,CAAAE,cAAA,UAAI;IAAAF,EAAA,CAAAG,MAAA,IAAwC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAE/CJ,EADF,CAAAE,cAAA,UAAI,kBACsC;IAAAF,EAAA,CAAAG,MAAA,aAAK;IAEjDH,EAFiD,CAAAI,YAAA,EAAS,EACnD,EACF;;;;;IArBwCJ,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAM,iBAAA,CAAAW,QAAA,CAAAP,MAAA,CAAkB;IACtBV,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAM,iBAAA,CAAAW,QAAA,CAAAR,IAAA,CAAgB;IAKnDT,EAAA,CAAAK,SAAA,GACF;IADEL,EAAA,CAAAW,kBAAA,MAAAM,QAAA,CAAAC,MAAA,MACF;IAEsBlB,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAW,kBAAA,WAAAM,QAAA,CAAAE,KAAA,CAAAN,OAAA,QAA6B;IACjDb,EAAA,CAAAK,SAAA,EAA+D;IAA/DL,EAAA,CAAAoB,UAAA,CAAAH,QAAA,CAAAI,MAAA,0CAA+D;IACjErB,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAsB,kBAAA,MAAAL,QAAA,CAAAI,MAAA,4BAAAJ,QAAA,CAAAI,MAAA,CAAAR,OAAA,SACF;IACIb,EAAA,CAAAK,SAAA,EAAsE;IAAtEL,EAAA,CAAAoB,UAAA,CAAAH,QAAA,CAAAL,aAAA,0CAAsE;IACxEZ,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAsB,kBAAA,MAAAL,QAAA,CAAAL,aAAA,sBAAAK,QAAA,CAAAL,aAAA,CAAAC,OAAA,UACF;IACIb,EAAA,CAAAK,SAAA,GAAgC;IAAhCL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAgB,YAAA,CAAAN,QAAA,CAAAO,MAAA,EAAgC;IAChCxB,EAAA,CAAAK,SAAA,GAAwC;IAAxCL,EAAA,CAAAW,kBAAA,WAAAM,QAAA,CAAAQ,SAAA,kBAAAR,QAAA,CAAAQ,SAAA,CAAAC,cAAA,OAAwC;;;;;IAO5C1B,EADF,CAAAE,cAAA,SAAI,aACqD;IACrDF,EAAA,CAAAG,MAAA,+CACF;IACFH,EADE,CAAAI,YAAA,EAAK,EACF;;;AAcrB,OAAM,MAAOuB,qBAAqB;EAahCC,YAAoBC,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;IAZvC,KAAAC,MAAM,GAAkB,EAAE;IAC1B,KAAAC,cAAc,GAAkB,EAAE;IAClC,KAAAC,gBAAgB,GAAa,EAAE;IAC/B,KAAAC,cAAc,GAAG,EAAE;IACnB,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,MAAM,GAAG,MAAM;IACf,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE;EAKuC;EAEzDC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,MAAMD,KAAI,CAACE,UAAU,EAAE;IAAC;EAC1B;EAEMA,UAAUA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAF,iBAAA;MACdE,MAAI,CAACP,OAAO,GAAG,IAAI;MACnB,IAAI;QACFO,MAAI,CAACb,MAAM,SAASa,MAAI,CAACd,mBAAmB,CAACe,eAAe,EAAE;QAC9DD,MAAI,CAACX,gBAAgB,GAAGW,MAAI,CAACd,mBAAmB,CAACgB,mBAAmB,CAACF,MAAI,CAACb,MAAM,CAAC;QACjFa,MAAI,CAACG,mBAAmB,EAAE;QAC1BH,MAAI,CAACI,YAAY,EAAE;QACnBJ,MAAI,CAACN,WAAW,GAAG,IAAIC,IAAI,EAAE;MAC/B,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC/C,CAAC,SAAS;QACRL,MAAI,CAACP,OAAO,GAAG,KAAK;MACtB;IAAC;EACH;EAEMc,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAV,iBAAA;MACf,MAAMU,MAAI,CAACT,UAAU,EAAE;IAAC;EAC1B;EAEAK,YAAYA,CAAA;IACV,IAAIK,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACtB,MAAM,CAAC;IAE/B;IACA,IAAI,IAAI,CAACG,cAAc,EAAE;MACvBmB,QAAQ,GAAG,IAAI,CAACvB,mBAAmB,CAACwB,iBAAiB,CAACD,QAAQ,EAAE,IAAI,CAACnB,cAAc,CAAC;IACtF;IAEA;IACA,IAAI,IAAI,CAACC,UAAU,EAAE;MACnB,MAAMoB,IAAI,GAAG,IAAI,CAACpB,UAAU,CAACqB,WAAW,EAAE;MAC1CH,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACC,KAAK,IAC9BA,KAAK,CAAChD,IAAI,CAAC8C,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,IACvCG,KAAK,CAAC/C,MAAM,CAAC6C,WAAW,EAAE,CAACG,QAAQ,CAACJ,IAAI,CAAC,CAC1C;IACH;IAEA,IAAI,CAACvB,cAAc,GAAGqB,QAAQ;IAC9B,IAAI,CAACO,UAAU,EAAE;EACnB;EAEAA,UAAUA,CAAA;IACR,IAAI,CAAC5B,cAAc,CAAC6B,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAChC,MAAMC,MAAM,GAAGF,CAAC,CAAC,IAAI,CAAC1B,MAA2B,CAAW;MAC5D,MAAM6B,MAAM,GAAGF,CAAC,CAAC,IAAI,CAAC3B,MAA2B,CAAW;MAE5D,IAAI,OAAO4B,MAAM,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC5D,OAAOD,MAAM,CAACE,aAAa,CAACD,MAAM,CAAC;MACrC;MAEA,OAAOA,MAAM,GAAGD,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC;EACJ;EAEAjB,mBAAmBA,CAAA;IACjB,MAAMoB,OAAO,GAAG,IAAI,CAACrC,mBAAmB,CAACsC,aAAa,CAAC,IAAI,CAACrC,MAAM,CAAC;IACnE,MAAMsC,MAAM,GAAG,IAAI,CAACvC,mBAAmB,CAACwC,YAAY,CAAC,IAAI,CAACvC,MAAM,CAAC;IAEjE,IAAI,CAACtB,SAAS,GAAG0D,OAAO,CAAC,CAAC,CAAC;IAC3B,IAAI,CAACpD,QAAQ,GAAGsD,MAAM,CAAC,CAAC,CAAC;EAC3B;EAEAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACxC,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC7C,aAAa,GAAG,CAAC,CAAC,CAAC2D,MAAM;EACpE;EAEAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC1C,MAAM,CAAC0B,MAAM,CAACC,KAAK,IAAIA,KAAK,CAAC7C,aAAa,GAAG,CAAC,CAAC,CAAC2D,MAAM;EACpE;EAEAhD,YAAYA,CAACC,MAAc;IACzB,IAAIA,MAAM,IAAI,QAAQ,EAAE;MACtB,OAAO,CAACA,MAAM,GAAG,QAAQ,EAAEX,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI;IAC9C,CAAC,MAAM,IAAIW,MAAM,IAAI,MAAM,EAAE;MAC3B,OAAO,CAACA,MAAM,GAAG,MAAM,EAAEX,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IAC3C,CAAC,MAAM,IAAIW,MAAM,IAAI,IAAI,EAAE;MACzB,OAAO,CAACA,MAAM,GAAG,IAAI,EAAEX,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;IACzC;IACA,OAAOW,MAAM,CAACiD,QAAQ,EAAE;EAC1B;;;uCAjGW9C,qBAAqB,EAAA3B,EAAA,CAAA0E,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;IAAA;EAAA;;;YAArBjD,qBAAqB;MAAAkD,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA/E,EAAA,CAAAgF,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzJxBtF,EAJN,CAAAE,cAAA,aAAuB,aAE0B,UACxC,YAC0C;UAAAF,EAAA,CAAAG,MAAA,oBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/DJ,EAAA,CAAAE,cAAA,WAA8B;UAAAF,EAAA,CAAAG,MAAA,mDAA4C;UAC5EH,EAD4E,CAAAI,YAAA,EAAI,EAC1E;UACNJ,EAAA,CAAAE,cAAA,gBAG0B;UAFxBF,EAAA,CAAAwF,UAAA,mBAAAC,uDAAA;YAAA,OAASF,GAAA,CAAArC,WAAA,EAAa;UAAA,EAAC;UAGvBlD,EAAA,CAAA0F,UAAA,IAAAC,4CAAA,kBAAe;UAGf3F,EAAA,CAAAG,MAAA,gBACF;UACFH,EADE,CAAAI,YAAA,EAAS,EACL;UAKFJ,EAFJ,CAAAE,cAAA,cAAmD,cAC/B,aACmC;UAAAF,EAAA,CAAAG,MAAA,uBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAGnEJ,EAFJ,CAAAE,cAAA,cAAuB,eACa,gBACJ;UAAAF,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAChDJ,EAAA,CAAAE,cAAA,gBAA0B;UAAAF,EAAA,CAAAG,MAAA,IAAmB;UAC/CH,EAD+C,CAAAI,YAAA,EAAO,EAChD;UAEJJ,EADF,CAAAE,cAAA,eAAkC,gBACJ;UAAAF,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAC3CJ,EAAA,CAAAE,cAAA,gBAAyC;UAAAF,EAAA,CAAAG,MAAA,IAAuB;UAClEH,EADkE,CAAAI,YAAA,EAAO,EACnE;UAEJJ,EADF,CAAAE,cAAA,eAAkC,gBACJ;UAAAF,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAC1CJ,EAAA,CAAAE,cAAA,gBAAuC;UAAAF,EAAA,CAAAG,MAAA,IAAsB;UAGnEH,EAHmE,CAAAI,YAAA,EAAO,EAChE,EACF,EACF;UAGJJ,EADF,CAAAE,cAAA,cAAkB,aACmC;UAAAF,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClEJ,EAAA,CAAA0F,UAAA,KAAAE,6CAAA,cAAiB;UAOnB5F,EAAA,CAAAI,YAAA,EAAM;UAGJJ,EADF,CAAAE,cAAA,cAAkB,aACmC;UAAAF,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjEJ,EAAA,CAAA0F,UAAA,KAAAG,6CAAA,cAAgB;UAQpB7F,EADE,CAAAI,YAAA,EAAM,EACF;UAMAJ,EAHN,CAAAE,cAAA,cAAkB,eAC+B,WACxC,iBACyD;UAAAF,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACpFJ,EAAA,CAAAE,cAAA,kBAAoF;UAA5EF,EAAA,CAAA8F,gBAAA,2BAAAC,gEAAAC,MAAA;YAAAhG,EAAA,CAAAiG,kBAAA,CAAAV,GAAA,CAAAtD,cAAA,EAAA+D,MAAA,MAAAT,GAAA,CAAAtD,cAAA,GAAA+D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAA4B;UAAChG,EAAA,CAAAwF,UAAA,2BAAAO,gEAAA;YAAA,OAAiBR,GAAA,CAAAxC,YAAA,EAAc;UAAA,EAAC;UACnE/C,EAAA,CAAAE,cAAA,kBAAiB;UAAAF,EAAA,CAAAG,MAAA,mBAAW;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACrCJ,EAAA,CAAAkG,gBAAA,KAAAC,qCAAA,sBAAAnG,EAAA,CAAAoG,yBAAA,CAEC;UAELpG,EADE,CAAAI,YAAA,EAAS,EACL;UAEJJ,EADF,CAAAE,cAAA,WAAK,iBACyD;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC1EJ,EAAA,CAAAE,cAAA,iBAKgB;UAHdF,EAAA,CAAA8F,gBAAA,2BAAAO,+DAAAL,MAAA;YAAAhG,EAAA,CAAAiG,kBAAA,CAAAV,GAAA,CAAArD,UAAA,EAAA8D,MAAA,MAAAT,GAAA,CAAArD,UAAA,GAAA8D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UACxBhG,EAAA,CAAAwF,UAAA,2BAAAa,+DAAA;YAAA,OAAiBd,GAAA,CAAAxC,YAAA,EAAc;UAAA,EAAC;UAGpC/C,EANE,CAAAI,YAAA,EAKgB,EACZ;UAEJJ,EADF,CAAAE,cAAA,WAAK,iBACyD;UAAAF,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UAC3EJ,EAAA,CAAAE,cAAA,kBAA0E;UAAlEF,EAAA,CAAA8F,gBAAA,2BAAAQ,gEAAAN,MAAA;YAAAhG,EAAA,CAAAiG,kBAAA,CAAAV,GAAA,CAAApD,MAAA,EAAA6D,MAAA,MAAAT,GAAA,CAAApD,MAAA,GAAA6D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAoB;UAAChG,EAAA,CAAAwF,UAAA,2BAAAc,gEAAA;YAAA,OAAiBf,GAAA,CAAA5B,UAAA,EAAY;UAAA,EAAC;UACzD3D,EAAA,CAAAE,cAAA,kBAAqB;UAAAF,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAClCJ,EAAA,CAAAE,cAAA,kBAAsB;UAAAF,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACpCJ,EAAA,CAAAE,cAAA,kBAAuB;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACtCJ,EAAA,CAAAE,cAAA,kBAA8B;UAAAF,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAC/CJ,EAAA,CAAAE,cAAA,kBAAuB;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAIrCH,EAJqC,CAAAI,YAAA,EAAS,EAC/B,EACL,EACF,EACF;UAQIJ,EALV,CAAAE,cAAA,cAAkB,eACa,iBACN,iBACO,UACpB,UACE;UAAAF,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACfJ,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,sBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAClBJ,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACfJ,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjBJ,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACfJ,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,uBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACxBJ,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAEdH,EAFc,CAAAI,YAAA,EAAK,EACZ,EACC;UACRJ,EAAA,CAAAE,cAAA,iBAAwC;UACtCF,EAAA,CAAAkG,gBAAA,KAAAK,qCAAA,oBAAAC,UAAA,SAAAC,0CAAA,aAgCC;UAITzG,EAHM,CAAAI,YAAA,EAAQ,EACF,EACJ,EACF;UAGNJ,EAAA,CAAAE,cAAA,eAA+C;UAC7CF,EAAA,CAAAG,MAAA,IACF;;UACFH,EADE,CAAAI,YAAA,EAAM,EACF;;;UAjJAJ,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAAe,UAAA,aAAAwE,GAAA,CAAAnD,OAAA,CAAoB;UAEpBpC,EAAA,CAAAK,SAAA,EAEC;UAFDL,EAAA,CAAA0G,aAAA,CAAAnB,GAAA,CAAAnD,OAAA,UAEC;UAY6BpC,EAAA,CAAAK,SAAA,IAAmB;UAAnBL,EAAA,CAAAM,iBAAA,CAAAiF,GAAA,CAAAzD,MAAA,CAAAyC,MAAA,CAAmB;UAIJvE,EAAA,CAAAK,SAAA,GAAuB;UAAvBL,EAAA,CAAAM,iBAAA,CAAAiF,GAAA,CAAAjB,eAAA,GAAuB;UAIzBtE,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAM,iBAAA,CAAAiF,GAAA,CAAAf,cAAA,GAAsB;UAOjExE,EAAA,CAAAK,SAAA,GAMC;UANDL,EAAA,CAAA0G,aAAA,CAAAnB,GAAA,CAAA/E,SAAA,WAMC;UAKDR,EAAA,CAAAK,SAAA,GAMC;UANDL,EAAA,CAAA0G,aAAA,CAAAnB,GAAA,CAAAzE,QAAA,WAMC;UASSd,EAAA,CAAAK,SAAA,GAA4B;UAA5BL,EAAA,CAAA2G,gBAAA,YAAApB,GAAA,CAAAtD,cAAA,CAA4B;UAElCjC,EAAA,CAAAK,SAAA,GAEC;UAFDL,EAAA,CAAA4G,UAAA,CAAArB,GAAA,CAAAvD,gBAAA,CAEC;UAODhC,EAAA,CAAAK,SAAA,GAAwB;UAAxBL,EAAA,CAAA2G,gBAAA,YAAApB,GAAA,CAAArD,UAAA,CAAwB;UAOlBlC,EAAA,CAAAK,SAAA,GAAoB;UAApBL,EAAA,CAAA2G,gBAAA,YAAApB,GAAA,CAAApD,MAAA,CAAoB;UA4B1BnC,EAAA,CAAAK,SAAA,IAgCC;UAhCDL,EAAA,CAAA4G,UAAA,CAAArB,GAAA,CAAAxD,cAAA,CAgCC;UAQP/B,EAAA,CAAAK,SAAA,GACF;UADEL,EAAA,CAAAW,kBAAA,oBAAAX,EAAA,CAAA6G,WAAA,SAAAtB,GAAA,CAAAlD,WAAA,iBACF;;;qBA3JMvC,YAAY,EAAAgH,EAAA,CAAAC,QAAA,EAAEhH,WAAW,EAAAiH,EAAA,CAAAC,cAAA,EAAAD,EAAA,CAAAE,uBAAA,EAAAF,EAAA,CAAAG,oBAAA,EAAAH,EAAA,CAAAI,0BAAA,EAAAJ,EAAA,CAAAK,eAAA,EAAAL,EAAA,CAAAM,OAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}