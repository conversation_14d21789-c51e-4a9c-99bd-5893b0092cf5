/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export { HttpBackend, HttpHandler } from './src/backend';
export { HttpClient } from './src/client';
export { HttpContext, HttpContextToken } from './src/context';
export { FetchBackend } from './src/fetch';
export { HttpHeaders } from './src/headers';
export { HTTP_INTERCEPTORS, HttpInterceptorHandler as ɵHttpInterceptorHandler, HttpInterceptorHandler as ɵHttpInterceptingHandler, } from './src/interceptor';
export { JsonpClientBackend, JsonpInterceptor } from './src/jsonp';
export { HttpClientJsonpModule, HttpClientModule, HttpClientXsrfModule } from './src/module';
export { HttpParams, HttpUrlEncodingCodec, } from './src/params';
export { HttpFeatureKind, provideHttpClient, withFet<PERSON>, withInterceptors, withInterceptorsFromDi, withJsonpSupport, withNoXsrfProtection, withRequestsMadeViaParent, withXsrfConfiguration, } from './src/provider';
export { HttpRequest } from './src/request';
export { HttpErrorResponse, HttpEventType, HttpHeaderResponse, HttpResponse, HttpResponseBase, HttpStatusCode, } from './src/response';
export { withHttpTransferCache as ɵwithHttpTransferCache, HTTP_TRANSFER_CACHE_ORIGIN_MAP, } from './src/transfer_cache';
export { HttpXhrBackend } from './src/xhr';
export { HttpXsrfTokenExtractor } from './src/xsrf';
// Private exports
export * from './src/private_export';
//# sourceMappingURL=data:application/json;base64,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