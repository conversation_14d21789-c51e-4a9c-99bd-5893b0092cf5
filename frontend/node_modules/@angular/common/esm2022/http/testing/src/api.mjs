/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/**
 * Controller to be injected into tests, that allows for mocking and flushing
 * of requests.
 *
 * @publicApi
 */
export class HttpTestingController {
}
//# sourceMappingURL=data:application/json;base64,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