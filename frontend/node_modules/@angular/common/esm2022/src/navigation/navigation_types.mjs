/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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