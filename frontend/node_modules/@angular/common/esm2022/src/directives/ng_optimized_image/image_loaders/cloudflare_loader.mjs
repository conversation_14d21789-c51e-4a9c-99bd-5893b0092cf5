/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { PLACEHOLDER_QUALITY } from './constants';
import { createImageLoader } from './image_loader';
/**
 * Function that generates an ImageLoader for [Cloudflare Image
 * Resizing](https://developers.cloudflare.com/images/image-resizing/) and turns it into an Angular
 * provider. Note: Cloudflare has multiple image products - this provider is specifically for
 * Cloudflare Image Resizing; it will not work with Cloudflare Images or Cloudflare Polish.
 *
 * @param path Your domain name, e.g. https://mysite.com
 * @returns Provider that provides an ImageLoader function
 *
 * @publicApi
 */
export const provideCloudflareLoader = createImageLoader(createCloudflareUrl, ngDevMode ? ['https://<ZONE>/cdn-cgi/image/<OPTIONS>/<SOURCE-IMAGE>'] : undefined);
function createCloudflareUrl(path, config) {
    let params = `format=auto`;
    if (config.width) {
        params += `,width=${config.width}`;
    }
    // When requesting a placeholder image we ask for a low quality image to reduce the load time.
    if (config.isPlaceholder) {
        params += `,quality=${PLACEHOLDER_QUALITY}`;
    }
    // Cloudflare image URLs format:
    // https://developers.cloudflare.com/images/image-resizing/url-format/
    return `${path}/cdn-cgi/image/${params}/${config.src}`;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY2xvdWRmbGFyZV9sb2FkZXIuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb21tb24vc3JjL2RpcmVjdGl2ZXMvbmdfb3B0aW1pemVkX2ltYWdlL2ltYWdlX2xvYWRlcnMvY2xvdWRmbGFyZV9sb2FkZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsT0FBTyxFQUFDLG1CQUFtQixFQUFDLE1BQU0sYUFBYSxDQUFDO0FBQ2hELE9BQU8sRUFBQyxpQkFBaUIsRUFBb0IsTUFBTSxnQkFBZ0IsQ0FBQztBQUVwRTs7Ozs7Ozs7OztHQVVHO0FBQ0gsTUFBTSxDQUFDLE1BQU0sdUJBQXVCLEdBQUcsaUJBQWlCLENBQ3RELG1CQUFtQixFQUNuQixTQUFTLENBQUMsQ0FBQyxDQUFDLENBQUMsdURBQXVELENBQUMsQ0FBQyxDQUFDLENBQUMsU0FBUyxDQUNsRixDQUFDO0FBRUYsU0FBUyxtQkFBbUIsQ0FBQyxJQUFZLEVBQUUsTUFBeUI7SUFDbEUsSUFBSSxNQUFNLEdBQUcsYUFBYSxDQUFDO0lBQzNCLElBQUksTUFBTSxDQUFDLEtBQUssRUFBRSxDQUFDO1FBQ2pCLE1BQU0sSUFBSSxVQUFVLE1BQU0sQ0FBQyxLQUFLLEVBQUUsQ0FBQztJQUNyQyxDQUFDO0lBRUQsOEZBQThGO0lBQzlGLElBQUksTUFBTSxDQUFDLGFBQWEsRUFBRSxDQUFDO1FBQ3pCLE1BQU0sSUFBSSxZQUFZLG1CQUFtQixFQUFFLENBQUM7SUFDOUMsQ0FBQztJQUVELGdDQUFnQztJQUNoQyxzRUFBc0U7SUFDdEUsT0FBTyxHQUFHLElBQUksa0JBQWtCLE1BQU0sSUFBSSxNQUFNLENBQUMsR0FBRyxFQUFFLENBQUM7QUFDekQsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmRldi9saWNlbnNlXG4gKi9cblxuaW1wb3J0IHtQTEFDRUhPTERFUl9RVUFMSVRZfSBmcm9tICcuL2NvbnN0YW50cyc7XG5pbXBvcnQge2NyZWF0ZUltYWdlTG9hZGVyLCBJbWFnZUxvYWRlckNvbmZpZ30gZnJvbSAnLi9pbWFnZV9sb2FkZXInO1xuXG4vKipcbiAqIEZ1bmN0aW9uIHRoYXQgZ2VuZXJhdGVzIGFuIEltYWdlTG9hZGVyIGZvciBbQ2xvdWRmbGFyZSBJbWFnZVxuICogUmVzaXppbmddKGh0dHBzOi8vZGV2ZWxvcGVycy5jbG91ZGZsYXJlLmNvbS9pbWFnZXMvaW1hZ2UtcmVzaXppbmcvKSBhbmQgdHVybnMgaXQgaW50byBhbiBBbmd1bGFyXG4gKiBwcm92aWRlci4gTm90ZTogQ2xvdWRmbGFyZSBoYXMgbXVsdGlwbGUgaW1hZ2UgcHJvZHVjdHMgLSB0aGlzIHByb3ZpZGVyIGlzIHNwZWNpZmljYWxseSBmb3JcbiAqIENsb3VkZmxhcmUgSW1hZ2UgUmVzaXppbmc7IGl0IHdpbGwgbm90IHdvcmsgd2l0aCBDbG91ZGZsYXJlIEltYWdlcyBvciBDbG91ZGZsYXJlIFBvbGlzaC5cbiAqXG4gKiBAcGFyYW0gcGF0aCBZb3VyIGRvbWFpbiBuYW1lLCBlLmcuIGh0dHBzOi8vbXlzaXRlLmNvbVxuICogQHJldHVybnMgUHJvdmlkZXIgdGhhdCBwcm92aWRlcyBhbiBJbWFnZUxvYWRlciBmdW5jdGlvblxuICpcbiAqIEBwdWJsaWNBcGlcbiAqL1xuZXhwb3J0IGNvbnN0IHByb3ZpZGVDbG91ZGZsYXJlTG9hZGVyID0gY3JlYXRlSW1hZ2VMb2FkZXIoXG4gIGNyZWF0ZUNsb3VkZmxhcmVVcmwsXG4gIG5nRGV2TW9kZSA/IFsnaHR0cHM6Ly88Wk9ORT4vY2RuLWNnaS9pbWFnZS88T1BUSU9OUz4vPFNPVVJDRS1JTUFHRT4nXSA6IHVuZGVmaW5lZCxcbik7XG5cbmZ1bmN0aW9uIGNyZWF0ZUNsb3VkZmxhcmVVcmwocGF0aDogc3RyaW5nLCBjb25maWc6IEltYWdlTG9hZGVyQ29uZmlnKSB7XG4gIGxldCBwYXJhbXMgPSBgZm9ybWF0PWF1dG9gO1xuICBpZiAoY29uZmlnLndpZHRoKSB7XG4gICAgcGFyYW1zICs9IGAsd2lkdGg9JHtjb25maWcud2lkdGh9YDtcbiAgfVxuXG4gIC8vIFdoZW4gcmVxdWVzdGluZyBhIHBsYWNlaG9sZGVyIGltYWdlIHdlIGFzayBmb3IgYSBsb3cgcXVhbGl0eSBpbWFnZSB0byByZWR1Y2UgdGhlIGxvYWQgdGltZS5cbiAgaWYgKGNvbmZpZy5pc1BsYWNlaG9sZGVyKSB7XG4gICAgcGFyYW1zICs9IGAscXVhbGl0eT0ke1BMQUNFSE9MREVSX1FVQUxJVFl9YDtcbiAgfVxuXG4gIC8vIENsb3VkZmxhcmUgaW1hZ2UgVVJMcyBmb3JtYXQ6XG4gIC8vIGh0dHBzOi8vZGV2ZWxvcGVycy5jbG91ZGZsYXJlLmNvbS9pbWFnZXMvaW1hZ2UtcmVzaXppbmcvdXJsLWZvcm1hdC9cbiAgcmV0dXJuIGAke3BhdGh9L2Nkbi1jZ2kvaW1hZ2UvJHtwYXJhbXN9LyR7Y29uZmlnLnNyY31gO1xufVxuIl19