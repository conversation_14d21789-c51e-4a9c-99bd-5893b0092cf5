/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export { ɵIMAGE_CONFIG as IMAGE_CONFIG } from '@angular/core';
// These exports represent the set of symbols exposed as a public API.
export { provideCloudflareLoader } from './image_loaders/cloudflare_loader';
export { provideCloudinaryLoader } from './image_loaders/cloudinary_loader';
export { IMAGE_LOADER } from './image_loaders/image_loader';
export { provideImageKitLoader } from './image_loaders/imagekit_loader';
export { provideImgixLoader } from './image_loaders/imgix_loader';
export { provideNetlifyLoader } from './image_loaders/netlify_loader';
export { NgOptimizedImage } from './ng_optimized_image';
export { PRECONNECT_CHECK_BLOCKLIST } from './preconnect_link_checker';
//# sourceMappingURL=data:application/json;base64,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