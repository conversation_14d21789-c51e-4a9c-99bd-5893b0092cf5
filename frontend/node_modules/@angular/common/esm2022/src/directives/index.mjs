/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { NgClass } from './ng_class';
import { NgComponentOutlet } from './ng_component_outlet';
import { <PERSON><PERSON><PERSON>, NgForOf, NgForOfContext } from './ng_for_of';
import { NgIf, NgIfContext } from './ng_if';
import { NgPlural, NgPluralCase } from './ng_plural';
import { NgStyle } from './ng_style';
import { NgSwitch, NgSwitchCase, NgSwitchDefault } from './ng_switch';
import { NgTemplateOutlet } from './ng_template_outlet';
export { NgClass, NgComponentOutlet, NgFor, NgForOf, NgForOfContext, NgIf, NgIfContext, NgPlural, NgPluralCase, NgStyle, NgSwitch, NgSwitchCase, Ng<PERSON><PERSON>Default, NgTemplateOutlet, };
/**
 * A collection of Angular directives that are likely to be used in each and every Angular
 * application.
 */
export const COMMON_DIRECTIVES = [
    NgClass,
    NgComponentOutlet,
    NgForOf,
    NgIf,
    NgTemplateOutlet,
    NgStyle,
    NgSwitch,
    NgSwitchCase,
    NgSwitchDefault,
    NgPlural,
    NgPluralCase,
];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5kZXguanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb21tb24vc3JjL2RpcmVjdGl2ZXMvaW5kZXgudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBSUgsT0FBTyxFQUFDLE9BQU8sRUFBQyxNQUFNLFlBQVksQ0FBQztBQUNuQyxPQUFPLEVBQUMsaUJBQWlCLEVBQUMsTUFBTSx1QkFBdUIsQ0FBQztBQUN4RCxPQUFPLEVBQUMsS0FBSyxFQUFFLE9BQU8sRUFBRSxjQUFjLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFDM0QsT0FBTyxFQUFDLElBQUksRUFBRSxXQUFXLEVBQUMsTUFBTSxTQUFTLENBQUM7QUFDMUMsT0FBTyxFQUFDLFFBQVEsRUFBRSxZQUFZLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFDbkQsT0FBTyxFQUFDLE9BQU8sRUFBQyxNQUFNLFlBQVksQ0FBQztBQUNuQyxPQUFPLEVBQUMsUUFBUSxFQUFFLFlBQVksRUFBRSxlQUFlLEVBQUMsTUFBTSxhQUFhLENBQUM7QUFDcEUsT0FBTyxFQUFDLGdCQUFnQixFQUFDLE1BQU0sc0JBQXNCLENBQUM7QUFFdEQsT0FBTyxFQUNMLE9BQU8sRUFDUCxpQkFBaUIsRUFDakIsS0FBSyxFQUNMLE9BQU8sRUFDUCxjQUFjLEVBQ2QsSUFBSSxFQUNKLFdBQVcsRUFDWCxRQUFRLEVBQ1IsWUFBWSxFQUNaLE9BQU8sRUFDUCxRQUFRLEVBQ1IsWUFBWSxFQUNaLGVBQWUsRUFDZixnQkFBZ0IsR0FDakIsQ0FBQztBQUVGOzs7R0FHRztBQUNILE1BQU0sQ0FBQyxNQUFNLGlCQUFpQixHQUFlO0lBQzNDLE9BQU87SUFDUCxpQkFBaUI7SUFDakIsT0FBTztJQUNQLElBQUk7SUFDSixnQkFBZ0I7SUFDaEIsT0FBTztJQUNQLFFBQVE7SUFDUixZQUFZO0lBQ1osZUFBZTtJQUNmLFFBQVE7SUFDUixZQUFZO0NBQ2IsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmRldi9saWNlbnNlXG4gKi9cblxuaW1wb3J0IHtQcm92aWRlcn0gZnJvbSAnQGFuZ3VsYXIvY29yZSc7XG5cbmltcG9ydCB7TmdDbGFzc30gZnJvbSAnLi9uZ19jbGFzcyc7XG5pbXBvcnQge05nQ29tcG9uZW50T3V0bGV0fSBmcm9tICcuL25nX2NvbXBvbmVudF9vdXRsZXQnO1xuaW1wb3J0IHtOZ0ZvciwgTmdGb3JPZiwgTmdGb3JPZkNvbnRleHR9IGZyb20gJy4vbmdfZm9yX29mJztcbmltcG9ydCB7TmdJZiwgTmdJZkNvbnRleHR9IGZyb20gJy4vbmdfaWYnO1xuaW1wb3J0IHtOZ1BsdXJhbCwgTmdQbHVyYWxDYXNlfSBmcm9tICcuL25nX3BsdXJhbCc7XG5pbXBvcnQge05nU3R5bGV9IGZyb20gJy4vbmdfc3R5bGUnO1xuaW1wb3J0IHtOZ1N3aXRjaCwgTmdTd2l0Y2hDYXNlLCBOZ1N3aXRjaERlZmF1bHR9IGZyb20gJy4vbmdfc3dpdGNoJztcbmltcG9ydCB7TmdUZW1wbGF0ZU91dGxldH0gZnJvbSAnLi9uZ190ZW1wbGF0ZV9vdXRsZXQnO1xuXG5leHBvcnQge1xuICBOZ0NsYXNzLFxuICBOZ0NvbXBvbmVudE91dGxldCxcbiAgTmdGb3IsXG4gIE5nRm9yT2YsXG4gIE5nRm9yT2ZDb250ZXh0LFxuICBOZ0lmLFxuICBOZ0lmQ29udGV4dCxcbiAgTmdQbHVyYWwsXG4gIE5nUGx1cmFsQ2FzZSxcbiAgTmdTdHlsZSxcbiAgTmdTd2l0Y2gsXG4gIE5nU3dpdGNoQ2FzZSxcbiAgTmdTd2l0Y2hEZWZhdWx0LFxuICBOZ1RlbXBsYXRlT3V0bGV0LFxufTtcblxuLyoqXG4gKiBBIGNvbGxlY3Rpb24gb2YgQW5ndWxhciBkaXJlY3RpdmVzIHRoYXQgYXJlIGxpa2VseSB0byBiZSB1c2VkIGluIGVhY2ggYW5kIGV2ZXJ5IEFuZ3VsYXJcbiAqIGFwcGxpY2F0aW9uLlxuICovXG5leHBvcnQgY29uc3QgQ09NTU9OX0RJUkVDVElWRVM6IFByb3ZpZGVyW10gPSBbXG4gIE5nQ2xhc3MsXG4gIE5nQ29tcG9uZW50T3V0bGV0LFxuICBOZ0Zvck9mLFxuICBOZ0lmLFxuICBOZ1RlbXBsYXRlT3V0bGV0LFxuICBOZ1N0eWxlLFxuICBOZ1N3aXRjaCxcbiAgTmdTd2l0Y2hDYXNlLFxuICBOZ1N3aXRjaERlZmF1bHQsXG4gIE5nUGx1cmFsLFxuICBOZ1BsdXJhbENhc2UsXG5dO1xuIl19