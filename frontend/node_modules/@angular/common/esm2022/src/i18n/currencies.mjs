/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/** @internal */
export const CURRENCIES_EN = { "ADP": [undefined, undefined, 0], "AFN": [undefined, "؋", 0], "ALL": [undefined, undefined, 0], "AMD": [undefined, "֏", 2], "AOA": [undefined, "Kz"], "ARS": [undefined, "$"], "AUD": ["A$", "$"], "AZN": [undefined, "₼"], "BAM": [undefined, "KM"], "BBD": [undefined, "$"], "BDT": [undefined, "৳"], "BHD": [undefined, undefined, 3], "BIF": [undefined, undefined, 0], "BMD": [undefined, "$"], "BND": [undefined, "$"], "BOB": [undefined, "Bs"], "BRL": ["R$"], "BSD": [undefined, "$"], "BWP": [undefined, "P"], "BYN": [undefined, undefined, 2], "BYR": [undefined, undefined, 0], "BZD": [undefined, "$"], "CAD": ["CA$", "$", 2], "CHF": [undefined, undefined, 2], "CLF": [undefined, undefined, 4], "CLP": [undefined, "$", 0], "CNY": ["CN¥", "¥"], "COP": [undefined, "$", 2], "CRC": [undefined, "₡", 2], "CUC": [undefined, "$"], "CUP": [undefined, "$"], "CZK": [undefined, "Kč", 2], "DJF": [undefined, undefined, 0], "DKK": [undefined, "kr", 2], "DOP": [undefined, "$"], "EGP": [undefined, "E£"], "ESP": [undefined, "₧", 0], "EUR": ["€"], "FJD": [undefined, "$"], "FKP": [undefined, "£"], "GBP": ["£"], "GEL": [undefined, "₾"], "GHS": [undefined, "GH₵"], "GIP": [undefined, "£"], "GNF": [undefined, "FG", 0], "GTQ": [undefined, "Q"], "GYD": [undefined, "$", 2], "HKD": ["HK$", "$"], "HNL": [undefined, "L"], "HRK": [undefined, "kn"], "HUF": [undefined, "Ft", 2], "IDR": [undefined, "Rp", 2], "ILS": ["₪"], "INR": ["₹"], "IQD": [undefined, undefined, 0], "IRR": [undefined, undefined, 0], "ISK": [undefined, "kr", 0], "ITL": [undefined, undefined, 0], "JMD": [undefined, "$"], "JOD": [undefined, undefined, 3], "JPY": ["¥", undefined, 0], "KHR": [undefined, "៛"], "KMF": [undefined, "CF", 0], "KPW": [undefined, "₩", 0], "KRW": ["₩", undefined, 0], "KWD": [undefined, undefined, 3], "KYD": [undefined, "$"], "KZT": [undefined, "₸"], "LAK": [undefined, "₭", 0], "LBP": [undefined, "L£", 0], "LKR": [undefined, "Rs"], "LRD": [undefined, "$"], "LTL": [undefined, "Lt"], "LUF": [undefined, undefined, 0], "LVL": [undefined, "Ls"], "LYD": [undefined, undefined, 3], "MGA": [undefined, "Ar", 0], "MGF": [undefined, undefined, 0], "MMK": [undefined, "K", 0], "MNT": [undefined, "₮", 2], "MRO": [undefined, undefined, 0], "MUR": [undefined, "Rs", 2], "MXN": ["MX$", "$"], "MYR": [undefined, "RM"], "NAD": [undefined, "$"], "NGN": [undefined, "₦"], "NIO": [undefined, "C$"], "NOK": [undefined, "kr", 2], "NPR": [undefined, "Rs"], "NZD": ["NZ$", "$"], "OMR": [undefined, undefined, 3], "PHP": ["₱"], "PKR": [undefined, "Rs", 2], "PLN": [undefined, "zł"], "PYG": [undefined, "₲", 0], "RON": [undefined, "lei"], "RSD": [undefined, undefined, 0], "RUB": [undefined, "₽"], "RWF": [undefined, "RF", 0], "SBD": [undefined, "$"], "SEK": [undefined, "kr", 2], "SGD": [undefined, "$"], "SHP": [undefined, "£"], "SLE": [undefined, undefined, 2], "SLL": [undefined, undefined, 0], "SOS": [undefined, undefined, 0], "SRD": [undefined, "$"], "SSP": [undefined, "£"], "STD": [undefined, undefined, 0], "STN": [undefined, "Db"], "SYP": [undefined, "£", 0], "THB": [undefined, "฿"], "TMM": [undefined, undefined, 0], "TND": [undefined, undefined, 3], "TOP": [undefined, "T$"], "TRL": [undefined, undefined, 0], "TRY": [undefined, "₺"], "TTD": [undefined, "$"], "TWD": ["NT$", "$", 2], "TZS": [undefined, undefined, 2], "UAH": [undefined, "₴"], "UGX": [undefined, undefined, 0], "USD": ["$"], "UYI": [undefined, undefined, 0], "UYU": [undefined, "$"], "UYW": [undefined, undefined, 4], "UZS": [undefined, undefined, 2], "VEF": [undefined, "Bs", 2], "VND": ["₫", undefined, 0], "VUV": [undefined, undefined, 0], "XAF": ["FCFA", undefined, 0], "XCD": ["EC$", "$"], "XOF": ["F CFA", undefined, 0], "XPF": ["CFPF", undefined, 0], "XXX": ["¤"], "YER": [undefined, undefined, 0], "ZAR": [undefined, "R"], "ZMK": [undefined, undefined, 0], "ZMW": [undefined, "ZK"], "ZWD": [undefined, undefined, 0] };
//# sourceMappingURL=data:application/json;base64,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