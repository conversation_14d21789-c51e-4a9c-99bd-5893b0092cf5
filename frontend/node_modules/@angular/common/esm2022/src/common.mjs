/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/**
 * @module
 * @description
 * Entry point for all public APIs of the common package.
 */
export * from './private_export';
export * from './location/index';
export { formatDate } from './i18n/format_date';
export { formatCurrency, formatNumber, formatPercent } from './i18n/format_number';
export { NgLocaleLocalization, NgLocalization } from './i18n/localization';
export { registerLocaleData } from './i18n/locale_data';
export { Plural, NumberFormatStyle, FormStyle, TranslationWidth, FormatWidth, NumberSymbol, WeekDay, getNumberOfCurrencyDigits, getCurrencySymbol, getLocaleDayPeriods, getLocaleDayNames, getLocaleMonthNames, getLocaleId, getLocaleEraNames, getLocaleWeekEndRange, getLocaleFirstDayOfWeek, getLocaleDateFormat, getLocaleDateTimeFormat, getLocaleExtraDayPeriodRules, getLocaleExtraDayPeriods, getLocalePluralCase, getLocaleTimeFormat, getLocaleNumberSymbol, getLocaleNumberFormat, getLocaleCurrencyCode, getLocaleCurrencyName, getLocaleCurrencySymbol, getLocaleDirection, } from './i18n/locale_data_api';
export { parseCookieValue as ɵparseCookieValue } from './cookie';
export { CommonModule } from './common_module';
export { NgClass, NgFor, NgForOf, NgForOfContext, NgIf, NgIfContext, NgPlural, NgPluralCase, NgStyle, NgSwitch, NgSwitchCase, NgSwitchDefault, NgTemplateOutlet, NgComponentOutlet, } from './directives/index';
export { DOCUMENT } from './dom_tokens';
export { AsyncPipe, DatePipe, DATE_PIPE_DEFAULT_TIMEZONE, DATE_PIPE_DEFAULT_OPTIONS, I18nPluralPipe, I18nSelectPipe, JsonPipe, LowerCasePipe, CurrencyPipe, DecimalPipe, PercentPipe, SlicePipe, UpperCasePipe, TitleCasePipe, KeyValuePipe, } from './pipes/index';
export { PLATFORM_BROWSER_ID as ɵPLATFORM_BROWSER_ID, PLATFORM_SERVER_ID as ɵPLATFORM_SERVER_ID, isPlatformBrowser, isPlatformServer, } from './platform_id';
export { VERSION } from './version';
export { ViewportScroller, NullViewportScroller as ɵNullViewportScroller } from './viewport_scroller';
export { XhrFactory } from './xhr';
export { IMAGE_CONFIG, IMAGE_LOADER, NgOptimizedImage, PRECONNECT_CHECK_BLOCKLIST, provideCloudflareLoader, provideCloudinaryLoader, provideImageKitLoader, provideImgixLoader, provideNetlifyLoader, } from './directives/ng_optimized_image';
export { normalizeQueryParams as ɵnormalizeQueryParams } from './location/util';
//# sourceMappingURL=data:application/json;base64,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