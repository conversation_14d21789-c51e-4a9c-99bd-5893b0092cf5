/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/**
 * @module
 * @description
 * This module provides a set of common Pipes.
 */
import { AsyncPipe } from './async_pipe';
import { LowerCasePipe, TitleCasePipe, UpperCasePipe } from './case_conversion_pipes';
import { DATE_PIPE_DEFAULT_OPTIONS, DATE_PIPE_DEFAULT_TIMEZONE, DatePipe } from './date_pipe';
import { I18nPluralPipe } from './i18n_plural_pipe';
import { I18nSelectPipe } from './i18n_select_pipe';
import { JsonPipe } from './json_pipe';
import { KeyValuePipe } from './keyvalue_pipe';
import { CurrencyPipe, DecimalPipe, PercentPipe } from './number_pipe';
import { SlicePipe } from './slice_pipe';
export { AsyncPipe, CurrencyPipe, DATE_PIPE_DEFAULT_OPTIONS, DATE_PIPE_DEFAULT_TIMEZONE, DatePipe, DecimalPipe, I18nPluralPipe, I18nSelectPipe, JsonPipe, KeyValuePipe, LowerCasePipe, PercentPipe, SlicePipe, TitleCasePipe, UpperCasePipe, };
/**
 * A collection of Angular pipes that are likely to be used in each and every application.
 */
export const COMMON_PIPES = [
    AsyncPipe,
    UpperCasePipe,
    LowerCasePipe,
    JsonPipe,
    SlicePipe,
    DecimalPipe,
    PercentPipe,
    TitleCasePipe,
    CurrencyPipe,
    DatePipe,
    I18nPluralPipe,
    I18nSelectPipe,
    KeyValuePipe,
];
//# sourceMappingURL=data:application/json;base64,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