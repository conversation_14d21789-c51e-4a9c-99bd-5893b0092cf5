/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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