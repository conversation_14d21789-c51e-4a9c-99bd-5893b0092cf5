{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../packages/common/src/navigation/platform_navigation.ts", "../../../../../../packages/common/testing/src/navigation/fake_navigation.ts", "../../../../../../packages/common/testing/src/mock_platform_location.ts", "../../../../../../packages/common/testing/src/navigation/provide_fake_platform_navigation.ts", "../../../../../../packages/common/testing/src/location_mock.ts", "../../../../../../packages/common/testing/src/mock_location_strategy.ts", "../../../../../../packages/common/testing/src/provide_location_mocks.ts", "../../../../../../packages/common/testing/src/testing.ts", "../../../../../../packages/common/testing/public_api.ts", "../../../../../../packages/common/testing/index.ts", "../../../../../../packages/common/testing/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable} from '@angular/core';\n\nimport {\n  NavigateEvent,\n  Navigation,\n  NavigationCurrentEntryChangeEvent,\n  NavigationHistoryEntry,\n  NavigationNavigateOptions,\n  NavigationOptions,\n  NavigationReloadOptions,\n  NavigationResult,\n  NavigationTransition,\n  NavigationUpdateCurrentEntryOptions,\n} from './navigation_types';\n\n/**\n * This class wraps the platform Navigation API which allows server-specific and test\n * implementations.\n */\n@Injectable({providedIn: 'platform', useFactory: () => (window as any).navigation})\nexport abstract class PlatformNavigation implements Navigation {\n  abstract entries(): NavigationHistoryEntry[];\n  abstract currentEntry: NavigationHistoryEntry | null;\n  abstract updateCurrentEntry(options: NavigationUpdateCurrentEntryOptions): void;\n  abstract transition: NavigationTransition | null;\n  abstract canGoBack: boolean;\n  abstract canGoForward: boolean;\n  abstract navigate(url: string, options?: NavigationNavigateOptions | undefined): NavigationResult;\n  abstract reload(options?: NavigationReloadOptions | undefined): NavigationResult;\n  abstract traverseTo(key: string, options?: NavigationOptions | undefined): NavigationResult;\n  abstract back(options?: NavigationOptions | undefined): NavigationResult;\n  abstract forward(options?: NavigationOptions | undefined): NavigationResult;\n  abstract onnavigate: ((this: Navigation, ev: NavigateEvent) => any) | null;\n  abstract onnavigatesuccess: ((this: Navigation, ev: Event) => any) | null;\n  abstract onnavigateerror: ((this: Navigation, ev: ErrorEvent) => any) | null;\n  abstract oncurrententrychange:\n    | ((this: Navigation, ev: NavigationCurrentEntryChangeEvent) => any)\n    | null;\n  abstract addEventListener(type: unknown, listener: unknown, options?: unknown): void;\n  abstract removeEventListener(type: unknown, listener: unknown, options?: unknown): void;\n  abstract dispatchEvent(event: Event): boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  NavigateEvent,\n  Navigation,\n  NavigationCurrentEntryChangeEvent,\n  NavigationDestination,\n  NavigationHistoryEntry,\n  NavigationInterceptOptions,\n  NavigationNavigateOptions,\n  NavigationOptions,\n  NavigationReloadOptions,\n  NavigationResult,\n  NavigationTransition,\n  NavigationTypeString,\n  NavigationUpdateCurrentEntryOptions,\n} from './navigation_types';\n\n/**\n * Fake implementation of user agent history and navigation behavior. This is a\n * high-fidelity implementation of browser behavior that attempts to emulate\n * things like traversal delay.\n */\nexport class FakeNavigation implements Navigation {\n  /**\n   * The fake implementation of an entries array. Only same-document entries\n   * allowed.\n   */\n  private readonly entriesArr: FakeNavigationHistoryEntry[] = [];\n\n  /**\n   * The current active entry index into `entriesArr`.\n   */\n  private currentEntryIndex = 0;\n\n  /**\n   * The current navigate event.\n   */\n  private navigateEvent: InternalFakeNavigateEvent | undefined = undefined;\n\n  /**\n   * A Map of pending traversals, so that traversals to the same entry can be\n   * re-used.\n   */\n  private readonly traversalQueue = new Map<string, InternalNavigationResult>();\n\n  /**\n   * A Promise that resolves when the previous traversals have finished. Used to\n   * simulate the cross-process communication necessary for traversals.\n   */\n  private nextTraversal = Promise.resolve();\n\n  /**\n   * A prospective current active entry index, which includes unresolved\n   * traversals. Used by `go` to determine where navigations are intended to go.\n   */\n  private prospectiveEntryIndex = 0;\n\n  /**\n   * A test-only option to make traversals synchronous, rather than emulate\n   * cross-process communication.\n   */\n  private synchronousTraversals = false;\n\n  /** Whether to allow a call to setInitialEntryForTesting. */\n  private canSetInitialEntry = true;\n\n  /** `EventTarget` to dispatch events. */\n  private eventTarget: EventTarget = this.window.document.createElement('div');\n\n  /** The next unique id for created entries. Replace recreates this id. */\n  private nextId = 0;\n\n  /** The next unique key for created entries. Replace inherits this id. */\n  private nextKey = 0;\n\n  /** Whether this fake is disposed. */\n  private disposed = false;\n\n  /** Equivalent to `navigation.currentEntry`. */\n  get currentEntry(): FakeNavigationHistoryEntry {\n    return this.entriesArr[this.currentEntryIndex];\n  }\n\n  get canGoBack(): boolean {\n    return this.currentEntryIndex > 0;\n  }\n\n  get canGoForward(): boolean {\n    return this.currentEntryIndex < this.entriesArr.length - 1;\n  }\n\n  constructor(\n    private readonly window: Window,\n    startURL: `http${string}`,\n  ) {\n    // First entry.\n    this.setInitialEntryForTesting(startURL);\n  }\n\n  /**\n   * Sets the initial entry.\n   */\n  private setInitialEntryForTesting(\n    url: `http${string}`,\n    options: {historyState: unknown; state?: unknown} = {historyState: null},\n  ) {\n    if (!this.canSetInitialEntry) {\n      throw new Error(\n        'setInitialEntryForTesting can only be called before any ' + 'navigation has occurred',\n      );\n    }\n    const currentInitialEntry = this.entriesArr[0];\n    this.entriesArr[0] = new FakeNavigationHistoryEntry(new URL(url).toString(), {\n      index: 0,\n      key: currentInitialEntry?.key ?? String(this.nextKey++),\n      id: currentInitialEntry?.id ?? String(this.nextId++),\n      sameDocument: true,\n      historyState: options?.historyState,\n      state: options.state,\n    });\n  }\n\n  /** Returns whether the initial entry is still eligible to be set. */\n  canSetInitialEntryForTesting(): boolean {\n    return this.canSetInitialEntry;\n  }\n\n  /**\n   * Sets whether to emulate traversals as synchronous rather than\n   * asynchronous.\n   */\n  setSynchronousTraversalsForTesting(synchronousTraversals: boolean) {\n    this.synchronousTraversals = synchronousTraversals;\n  }\n\n  /** Equivalent to `navigation.entries()`. */\n  entries(): FakeNavigationHistoryEntry[] {\n    return this.entriesArr.slice();\n  }\n\n  /** Equivalent to `navigation.navigate()`. */\n  navigate(url: string, options?: NavigationNavigateOptions): FakeNavigationResult {\n    const fromUrl = new URL(this.currentEntry.url!);\n    const toUrl = new URL(url, this.currentEntry.url!);\n\n    let navigationType: NavigationTypeString;\n    if (!options?.history || options.history === 'auto') {\n      // Auto defaults to push, but if the URLs are the same, is a replace.\n      if (fromUrl.toString() === toUrl.toString()) {\n        navigationType = 'replace';\n      } else {\n        navigationType = 'push';\n      }\n    } else {\n      navigationType = options.history;\n    }\n\n    const hashChange = isHashChange(fromUrl, toUrl);\n\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      state: options?.state,\n      sameDocument: hashChange,\n      historyState: null,\n    });\n    const result = new InternalNavigationResult();\n\n    this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for navigate().\n      userInitiated: false,\n      hashChange,\n      info: options?.info,\n    });\n\n    return {\n      committed: result.committed,\n      finished: result.finished,\n    };\n  }\n\n  /** Equivalent to `history.pushState()`. */\n  pushState(data: unknown, title: string, url?: string): void {\n    this.pushOrReplaceState('push', data, title, url);\n  }\n\n  /** Equivalent to `history.replaceState()`. */\n  replaceState(data: unknown, title: string, url?: string): void {\n    this.pushOrReplaceState('replace', data, title, url);\n  }\n\n  private pushOrReplaceState(\n    navigationType: NavigationTypeString,\n    data: unknown,\n    _title: string,\n    url?: string,\n  ): void {\n    const fromUrl = new URL(this.currentEntry.url!);\n    const toUrl = url ? new URL(url, this.currentEntry.url!) : fromUrl;\n\n    const hashChange = isHashChange(fromUrl, toUrl);\n\n    const destination = new FakeNavigationDestination({\n      url: toUrl.toString(),\n      sameDocument: true,\n      historyState: data,\n    });\n    const result = new InternalNavigationResult();\n\n    this.userAgentNavigate(destination, result, {\n      navigationType,\n      cancelable: true,\n      canIntercept: true,\n      // Always false for pushState() or replaceState().\n      userInitiated: false,\n      hashChange,\n      skipPopState: true,\n    });\n  }\n\n  /** Equivalent to `navigation.traverseTo()`. */\n  traverseTo(key: string, options?: NavigationOptions): FakeNavigationResult {\n    const fromUrl = new URL(this.currentEntry.url!);\n    const entry = this.findEntry(key);\n    if (!entry) {\n      const domException = new DOMException('Invalid key', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished,\n      };\n    }\n    if (entry === this.currentEntry) {\n      return {\n        committed: Promise.resolve(this.currentEntry),\n        finished: Promise.resolve(this.currentEntry),\n      };\n    }\n    if (this.traversalQueue.has(entry.key)) {\n      const existingResult = this.traversalQueue.get(entry.key)!;\n      return {\n        committed: existingResult.committed,\n        finished: existingResult.finished,\n      };\n    }\n\n    const hashChange = isHashChange(fromUrl, new URL(entry.url!, this.currentEntry.url!));\n    const destination = new FakeNavigationDestination({\n      url: entry.url!,\n      state: entry.getState(),\n      historyState: entry.getHistoryState(),\n      key: entry.key,\n      id: entry.id,\n      index: entry.index,\n      sameDocument: entry.sameDocument,\n    });\n    this.prospectiveEntryIndex = entry.index;\n    const result = new InternalNavigationResult();\n    this.traversalQueue.set(entry.key, result);\n    this.runTraversal(() => {\n      this.traversalQueue.delete(entry.key);\n      this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for traverseTo().\n        userInitiated: false,\n        hashChange,\n        info: options?.info,\n      });\n    });\n    return {\n      committed: result.committed,\n      finished: result.finished,\n    };\n  }\n\n  /** Equivalent to `navigation.back()`. */\n  back(options?: NavigationOptions): FakeNavigationResult {\n    if (this.currentEntryIndex === 0) {\n      const domException = new DOMException('Cannot go back', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished,\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex - 1];\n    return this.traverseTo(entry.key, options);\n  }\n\n  /** Equivalent to `navigation.forward()`. */\n  forward(options?: NavigationOptions): FakeNavigationResult {\n    if (this.currentEntryIndex === this.entriesArr.length - 1) {\n      const domException = new DOMException('Cannot go forward', 'InvalidStateError');\n      const committed = Promise.reject(domException);\n      const finished = Promise.reject(domException);\n      committed.catch(() => {});\n      finished.catch(() => {});\n      return {\n        committed,\n        finished,\n      };\n    }\n    const entry = this.entriesArr[this.currentEntryIndex + 1];\n    return this.traverseTo(entry.key, options);\n  }\n\n  /**\n   * Equivalent to `history.go()`.\n   * Note that this method does not actually work precisely to how Chrome\n   * does, instead choosing a simpler model with less unexpected behavior.\n   * Chrome has a few edge case optimizations, for instance with repeated\n   * `back(); forward()` chains it collapses certain traversals.\n   */\n  go(direction: number): void {\n    const targetIndex = this.prospectiveEntryIndex + direction;\n    if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n      return;\n    }\n    this.prospectiveEntryIndex = targetIndex;\n    this.runTraversal(() => {\n      // Check again that destination is in the entries array.\n      if (targetIndex >= this.entriesArr.length || targetIndex < 0) {\n        return;\n      }\n      const fromUrl = new URL(this.currentEntry.url!);\n      const entry = this.entriesArr[targetIndex];\n      const hashChange = isHashChange(fromUrl, new URL(entry.url!, this.currentEntry.url!));\n      const destination = new FakeNavigationDestination({\n        url: entry.url!,\n        state: entry.getState(),\n        historyState: entry.getHistoryState(),\n        key: entry.key,\n        id: entry.id,\n        index: entry.index,\n        sameDocument: entry.sameDocument,\n      });\n      const result = new InternalNavigationResult();\n      this.userAgentNavigate(destination, result, {\n        navigationType: 'traverse',\n        cancelable: true,\n        canIntercept: true,\n        // Always false for go().\n        userInitiated: false,\n        hashChange,\n      });\n    });\n  }\n\n  /** Runs a traversal synchronously or asynchronously */\n  private runTraversal(traversal: () => void) {\n    if (this.synchronousTraversals) {\n      traversal();\n      return;\n    }\n\n    // Each traversal occupies a single timeout resolution.\n    // This means that Promises added to commit and finish should resolve\n    // before the next traversal.\n    this.nextTraversal = this.nextTraversal.then(() => {\n      return new Promise<void>((resolve) => {\n        setTimeout(() => {\n          resolve();\n          traversal();\n        });\n      });\n    });\n  }\n\n  /** Equivalent to `navigation.addEventListener()`. */\n  addEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: AddEventListenerOptions | boolean,\n  ) {\n    this.eventTarget.addEventListener(type, callback, options);\n  }\n\n  /** Equivalent to `navigation.removeEventListener()`. */\n  removeEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: EventListenerOptions | boolean,\n  ) {\n    this.eventTarget.removeEventListener(type, callback, options);\n  }\n\n  /** Equivalent to `navigation.dispatchEvent()` */\n  dispatchEvent(event: Event): boolean {\n    return this.eventTarget.dispatchEvent(event);\n  }\n\n  /** Cleans up resources. */\n  dispose() {\n    // Recreate eventTarget to release current listeners.\n    // `document.createElement` because NodeJS `EventTarget` is incompatible with Domino's `Event`.\n    this.eventTarget = this.window.document.createElement('div');\n    this.disposed = true;\n  }\n\n  /** Returns whether this fake is disposed. */\n  isDisposed() {\n    return this.disposed;\n  }\n\n  /** Implementation for all navigations and traversals. */\n  private userAgentNavigate(\n    destination: FakeNavigationDestination,\n    result: InternalNavigationResult,\n    options: InternalNavigateOptions,\n  ) {\n    // The first navigation should disallow any future calls to set the initial\n    // entry.\n    this.canSetInitialEntry = false;\n    if (this.navigateEvent) {\n      this.navigateEvent.cancel(new DOMException('Navigation was aborted', 'AbortError'));\n      this.navigateEvent = undefined;\n    }\n\n    const navigateEvent = createFakeNavigateEvent({\n      navigationType: options.navigationType,\n      cancelable: options.cancelable,\n      canIntercept: options.canIntercept,\n      userInitiated: options.userInitiated,\n      hashChange: options.hashChange,\n      signal: result.signal,\n      destination,\n      info: options.info,\n      sameDocument: destination.sameDocument,\n      skipPopState: options.skipPopState,\n      result,\n      userAgentCommit: () => {\n        this.userAgentCommit();\n      },\n    });\n\n    this.navigateEvent = navigateEvent;\n    this.eventTarget.dispatchEvent(navigateEvent);\n    navigateEvent.dispatchedNavigateEvent();\n    if (navigateEvent.commitOption === 'immediate') {\n      navigateEvent.commit(/* internal= */ true);\n    }\n  }\n\n  /** Implementation to commit a navigation. */\n  private userAgentCommit() {\n    if (!this.navigateEvent) {\n      return;\n    }\n    const from = this.currentEntry;\n    if (!this.navigateEvent.sameDocument) {\n      const error = new Error('Cannot navigate to a non-same-document URL.');\n      this.navigateEvent.cancel(error);\n      throw error;\n    }\n    if (\n      this.navigateEvent.navigationType === 'push' ||\n      this.navigateEvent.navigationType === 'replace'\n    ) {\n      this.userAgentPushOrReplace(this.navigateEvent.destination, {\n        navigationType: this.navigateEvent.navigationType,\n      });\n    } else if (this.navigateEvent.navigationType === 'traverse') {\n      this.userAgentTraverse(this.navigateEvent.destination);\n    }\n    this.navigateEvent.userAgentNavigated(this.currentEntry);\n    const currentEntryChangeEvent = createFakeNavigationCurrentEntryChangeEvent({\n      from,\n      navigationType: this.navigateEvent.navigationType,\n    });\n    this.eventTarget.dispatchEvent(currentEntryChangeEvent);\n    if (!this.navigateEvent.skipPopState) {\n      const popStateEvent = createPopStateEvent({\n        state: this.navigateEvent.destination.getHistoryState(),\n      });\n      this.window.dispatchEvent(popStateEvent);\n    }\n  }\n\n  /** Implementation for a push or replace navigation. */\n  private userAgentPushOrReplace(\n    destination: FakeNavigationDestination,\n    {navigationType}: {navigationType: NavigationTypeString},\n  ) {\n    if (navigationType === 'push') {\n      this.currentEntryIndex++;\n      this.prospectiveEntryIndex = this.currentEntryIndex;\n    }\n    const index = this.currentEntryIndex;\n    const key = navigationType === 'push' ? String(this.nextKey++) : this.currentEntry.key;\n    const entry = new FakeNavigationHistoryEntry(destination.url, {\n      id: String(this.nextId++),\n      key,\n      index,\n      sameDocument: true,\n      state: destination.getState(),\n      historyState: destination.getHistoryState(),\n    });\n    if (navigationType === 'push') {\n      this.entriesArr.splice(index, Infinity, entry);\n    } else {\n      this.entriesArr[index] = entry;\n    }\n  }\n\n  /** Implementation for a traverse navigation. */\n  private userAgentTraverse(destination: FakeNavigationDestination) {\n    this.currentEntryIndex = destination.index;\n  }\n\n  /** Utility method for finding entries with the given `key`. */\n  private findEntry(key: string) {\n    for (const entry of this.entriesArr) {\n      if (entry.key === key) return entry;\n    }\n    return undefined;\n  }\n\n  set onnavigate(_handler: ((this: Navigation, ev: NavigateEvent) => any) | null) {\n    throw new Error('unimplemented');\n  }\n\n  get onnavigate(): ((this: Navigation, ev: NavigateEvent) => any) | null {\n    throw new Error('unimplemented');\n  }\n\n  set oncurrententrychange(\n    _handler: ((this: Navigation, ev: NavigationCurrentEntryChangeEvent) => any) | null,\n  ) {\n    throw new Error('unimplemented');\n  }\n\n  get oncurrententrychange():\n    | ((this: Navigation, ev: NavigationCurrentEntryChangeEvent) => any)\n    | null {\n    throw new Error('unimplemented');\n  }\n\n  set onnavigatesuccess(_handler: ((this: Navigation, ev: Event) => any) | null) {\n    throw new Error('unimplemented');\n  }\n\n  get onnavigatesuccess(): ((this: Navigation, ev: Event) => any) | null {\n    throw new Error('unimplemented');\n  }\n\n  set onnavigateerror(_handler: ((this: Navigation, ev: ErrorEvent) => any) | null) {\n    throw new Error('unimplemented');\n  }\n\n  get onnavigateerror(): ((this: Navigation, ev: ErrorEvent) => any) | null {\n    throw new Error('unimplemented');\n  }\n\n  get transition(): NavigationTransition | null {\n    throw new Error('unimplemented');\n  }\n\n  updateCurrentEntry(_options: NavigationUpdateCurrentEntryOptions): void {\n    throw new Error('unimplemented');\n  }\n\n  reload(_options?: NavigationReloadOptions): NavigationResult {\n    throw new Error('unimplemented');\n  }\n}\n\n/**\n * Fake equivalent of the `NavigationResult` interface with\n * `FakeNavigationHistoryEntry`.\n */\ninterface FakeNavigationResult extends NavigationResult {\n  readonly committed: Promise<FakeNavigationHistoryEntry>;\n  readonly finished: Promise<FakeNavigationHistoryEntry>;\n}\n\n/**\n * Fake equivalent of `NavigationHistoryEntry`.\n */\nexport class FakeNavigationHistoryEntry implements NavigationHistoryEntry {\n  readonly sameDocument;\n\n  readonly id: string;\n  readonly key: string;\n  readonly index: number;\n  private readonly state: unknown;\n  private readonly historyState: unknown;\n\n  // tslint:disable-next-line:no-any\n  ondispose: ((this: NavigationHistoryEntry, ev: Event) => any) | null = null;\n\n  constructor(\n    readonly url: string | null,\n    {\n      id,\n      key,\n      index,\n      sameDocument,\n      state,\n      historyState,\n    }: {\n      id: string;\n      key: string;\n      index: number;\n      sameDocument: boolean;\n      historyState: unknown;\n      state?: unknown;\n    },\n  ) {\n    this.id = id;\n    this.key = key;\n    this.index = index;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n  }\n\n  getState(): unknown {\n    // Budget copy.\n    return this.state ? JSON.parse(JSON.stringify(this.state)) : this.state;\n  }\n\n  getHistoryState(): unknown {\n    // Budget copy.\n    return this.historyState ? JSON.parse(JSON.stringify(this.historyState)) : this.historyState;\n  }\n\n  addEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: AddEventListenerOptions | boolean,\n  ) {\n    throw new Error('unimplemented');\n  }\n\n  removeEventListener(\n    type: string,\n    callback: EventListenerOrEventListenerObject,\n    options?: EventListenerOptions | boolean,\n  ) {\n    throw new Error('unimplemented');\n  }\n\n  dispatchEvent(event: Event): boolean {\n    throw new Error('unimplemented');\n  }\n}\n\n/** `NavigationInterceptOptions` with experimental commit option. */\nexport interface ExperimentalNavigationInterceptOptions extends NavigationInterceptOptions {\n  commit?: 'immediate' | 'after-transition';\n}\n\n/** `NavigateEvent` with experimental commit function. */\nexport interface ExperimentalNavigateEvent extends NavigateEvent {\n  intercept(options?: ExperimentalNavigationInterceptOptions): void;\n\n  commit(): void;\n}\n\n/**\n * Fake equivalent of `NavigateEvent`.\n */\nexport interface FakeNavigateEvent extends ExperimentalNavigateEvent {\n  readonly destination: FakeNavigationDestination;\n}\n\ninterface InternalFakeNavigateEvent extends FakeNavigateEvent {\n  readonly sameDocument: boolean;\n  readonly skipPopState?: boolean;\n  readonly commitOption: 'after-transition' | 'immediate';\n  readonly result: InternalNavigationResult;\n\n  commit(internal?: boolean): void;\n  cancel(reason: Error): void;\n  dispatchedNavigateEvent(): void;\n  userAgentNavigated(entry: FakeNavigationHistoryEntry): void;\n}\n\n/**\n * Create a fake equivalent of `NavigateEvent`. This is not a class because ES5\n * transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigateEvent({\n  cancelable,\n  canIntercept,\n  userInitiated,\n  hashChange,\n  navigationType,\n  signal,\n  destination,\n  info,\n  sameDocument,\n  skipPopState,\n  result,\n  userAgentCommit,\n}: {\n  cancelable: boolean;\n  canIntercept: boolean;\n  userInitiated: boolean;\n  hashChange: boolean;\n  navigationType: NavigationTypeString;\n  signal: AbortSignal;\n  destination: FakeNavigationDestination;\n  info: unknown;\n  sameDocument: boolean;\n  skipPopState?: boolean;\n  result: InternalNavigationResult;\n  userAgentCommit: () => void;\n}) {\n  const event = new Event('navigate', {bubbles: false, cancelable}) as {\n    -readonly [P in keyof InternalFakeNavigateEvent]: InternalFakeNavigateEvent[P];\n  };\n  event.canIntercept = canIntercept;\n  event.userInitiated = userInitiated;\n  event.hashChange = hashChange;\n  event.navigationType = navigationType;\n  event.signal = signal;\n  event.destination = destination;\n  event.info = info;\n  event.downloadRequest = null;\n  event.formData = null;\n\n  event.sameDocument = sameDocument;\n  event.skipPopState = skipPopState;\n  event.commitOption = 'immediate';\n\n  let handlerFinished: Promise<void> | undefined = undefined;\n  let interceptCalled = false;\n  let dispatchedNavigateEvent = false;\n  let commitCalled = false;\n\n  event.intercept = function (\n    this: InternalFakeNavigateEvent,\n    options?: ExperimentalNavigationInterceptOptions,\n  ): void {\n    interceptCalled = true;\n    event.sameDocument = true;\n    const handler = options?.handler;\n    if (handler) {\n      handlerFinished = handler();\n    }\n    if (options?.commit) {\n      event.commitOption = options.commit;\n    }\n    if (options?.focusReset !== undefined || options?.scroll !== undefined) {\n      throw new Error('unimplemented');\n    }\n  };\n\n  event.scroll = function (this: InternalFakeNavigateEvent): void {\n    throw new Error('unimplemented');\n  };\n\n  event.commit = function (this: InternalFakeNavigateEvent, internal = false) {\n    if (!internal && !interceptCalled) {\n      throw new DOMException(\n        `Failed to execute 'commit' on 'NavigateEvent': intercept() must be ` +\n          `called before commit().`,\n        'InvalidStateError',\n      );\n    }\n    if (!dispatchedNavigateEvent) {\n      throw new DOMException(\n        `Failed to execute 'commit' on 'NavigateEvent': commit() may not be ` +\n          `called during event dispatch.`,\n        'InvalidStateError',\n      );\n    }\n    if (commitCalled) {\n      throw new DOMException(\n        `Failed to execute 'commit' on 'NavigateEvent': commit() already ` + `called.`,\n        'InvalidStateError',\n      );\n    }\n    commitCalled = true;\n\n    userAgentCommit();\n  };\n\n  // Internal only.\n  event.cancel = function (this: InternalFakeNavigateEvent, reason: Error) {\n    result.committedReject(reason);\n    result.finishedReject(reason);\n  };\n\n  // Internal only.\n  event.dispatchedNavigateEvent = function (this: InternalFakeNavigateEvent) {\n    dispatchedNavigateEvent = true;\n    if (event.commitOption === 'after-transition') {\n      // If handler finishes before commit, call commit.\n      handlerFinished?.then(\n        () => {\n          if (!commitCalled) {\n            event.commit(/* internal */ true);\n          }\n        },\n        () => {},\n      );\n    }\n    Promise.all([result.committed, handlerFinished]).then(\n      ([entry]) => {\n        result.finishedResolve(entry);\n      },\n      (reason) => {\n        result.finishedReject(reason);\n      },\n    );\n  };\n\n  // Internal only.\n  event.userAgentNavigated = function (\n    this: InternalFakeNavigateEvent,\n    entry: FakeNavigationHistoryEntry,\n  ) {\n    result.committedResolve(entry);\n  };\n\n  return event as InternalFakeNavigateEvent;\n}\n\n/** Fake equivalent of `NavigationCurrentEntryChangeEvent`. */\nexport interface FakeNavigationCurrentEntryChangeEvent extends NavigationCurrentEntryChangeEvent {\n  readonly from: FakeNavigationHistoryEntry;\n}\n\n/**\n * Create a fake equivalent of `NavigationCurrentEntryChange`. This does not use\n * a class because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createFakeNavigationCurrentEntryChangeEvent({\n  from,\n  navigationType,\n}: {\n  from: FakeNavigationHistoryEntry;\n  navigationType: NavigationTypeString;\n}) {\n  const event = new Event('currententrychange', {\n    bubbles: false,\n    cancelable: false,\n  }) as {\n    -readonly [P in keyof NavigationCurrentEntryChangeEvent]: NavigationCurrentEntryChangeEvent[P];\n  };\n  event.from = from;\n  event.navigationType = navigationType;\n  return event as FakeNavigationCurrentEntryChangeEvent;\n}\n\n/**\n * Create a fake equivalent of `PopStateEvent`. This does not use a class\n * because ES5 transpiled JavaScript cannot extend native Event.\n */\nfunction createPopStateEvent({state}: {state: unknown}) {\n  const event = new Event('popstate', {\n    bubbles: false,\n    cancelable: false,\n  }) as {-readonly [P in keyof PopStateEvent]: PopStateEvent[P]};\n  event.state = state;\n  return event as PopStateEvent;\n}\n\n/**\n * Fake equivalent of `NavigationDestination`.\n */\nexport class FakeNavigationDestination implements NavigationDestination {\n  readonly url: string;\n  readonly sameDocument: boolean;\n  readonly key: string | null;\n  readonly id: string | null;\n  readonly index: number;\n\n  private readonly state?: unknown;\n  private readonly historyState: unknown;\n\n  constructor({\n    url,\n    sameDocument,\n    historyState,\n    state,\n    key = null,\n    id = null,\n    index = -1,\n  }: {\n    url: string;\n    sameDocument: boolean;\n    historyState: unknown;\n    state?: unknown;\n    key?: string | null;\n    id?: string | null;\n    index?: number;\n  }) {\n    this.url = url;\n    this.sameDocument = sameDocument;\n    this.state = state;\n    this.historyState = historyState;\n    this.key = key;\n    this.id = id;\n    this.index = index;\n  }\n\n  getState(): unknown {\n    return this.state;\n  }\n\n  getHistoryState(): unknown {\n    return this.historyState;\n  }\n}\n\n/** Utility function to determine whether two UrlLike have the same hash. */\nfunction isHashChange(from: URL, to: URL): boolean {\n  return (\n    to.hash !== from.hash &&\n    to.hostname === from.hostname &&\n    to.pathname === from.pathname &&\n    to.search === from.search\n  );\n}\n\n/** Internal utility class for representing the result of a navigation.  */\nclass InternalNavigationResult {\n  committedResolve!: (entry: FakeNavigationHistoryEntry) => void;\n  committedReject!: (reason: Error) => void;\n  finishedResolve!: (entry: FakeNavigationHistoryEntry) => void;\n  finishedReject!: (reason: Error) => void;\n  readonly committed: Promise<FakeNavigationHistoryEntry>;\n  readonly finished: Promise<FakeNavigationHistoryEntry>;\n  get signal(): AbortSignal {\n    return this.abortController.signal;\n  }\n  private readonly abortController = new AbortController();\n\n  constructor() {\n    this.committed = new Promise<FakeNavigationHistoryEntry>((resolve, reject) => {\n      this.committedResolve = resolve;\n      this.committedReject = reject;\n    });\n\n    this.finished = new Promise<FakeNavigationHistoryEntry>(async (resolve, reject) => {\n      this.finishedResolve = resolve;\n      this.finishedReject = (reason: Error) => {\n        reject(reason);\n        this.abortController.abort(reason);\n      };\n    });\n    // All rejections are handled.\n    this.committed.catch(() => {});\n    this.finished.catch(() => {});\n  }\n}\n\n/** Internal options for performing a navigate. */\ninterface InternalNavigateOptions {\n  navigationType: NavigationTypeString;\n  cancelable: boolean;\n  canIntercept: boolean;\n  userInitiated: boolean;\n  hashChange: boolean;\n  info?: unknown;\n  skipPopState?: boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DOCUMENT,\n  LocationChangeEvent,\n  LocationChangeListener,\n  PlatformLocation,\n  ɵPlatformNavigation as PlatformNavigation,\n} from '@angular/common';\nimport {Inject, inject, Injectable, InjectionToken, Optional} from '@angular/core';\nimport {Subject} from 'rxjs';\n\nimport {FakeNavigation} from './navigation/fake_navigation';\n\n/**\n * Parser from https://tools.ietf.org/html/rfc3986#appendix-B\n * ^(([^:/?#]+):)?(//([^/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?\n *  12            3  4          5       6  7        8 9\n *\n * Example: http://www.ics.uci.edu/pub/ietf/uri/#Related\n *\n * Results in:\n *\n * $1 = http:\n * $2 = http\n * $3 = //www.ics.uci.edu\n * $4 = www.ics.uci.edu\n * $5 = /pub/ietf/uri/\n * $6 = <undefined>\n * $7 = <undefined>\n * $8 = #Related\n * $9 = Related\n */\nconst urlParse = /^(([^:\\/?#]+):)?(\\/\\/([^\\/?#]*))?([^?#]*)(\\?([^#]*))?(#(.*))?/;\n\nfunction parseUrl(urlStr: string, baseHref: string) {\n  const verifyProtocol = /^((http[s]?|ftp):\\/\\/)/;\n  let serverBase: string | undefined;\n\n  // URL class requires full URL. If the URL string doesn't start with protocol, we need to add\n  // an arbitrary base URL which can be removed afterward.\n  if (!verifyProtocol.test(urlStr)) {\n    serverBase = 'http://empty.com/';\n  }\n  let parsedUrl: {\n    protocol: string;\n    hostname: string;\n    port: string;\n    pathname: string;\n    search: string;\n    hash: string;\n  };\n  try {\n    parsedUrl = new URL(urlStr, serverBase);\n  } catch (e) {\n    const result = urlParse.exec(serverBase || '' + urlStr);\n    if (!result) {\n      throw new Error(`Invalid URL: ${urlStr} with base: ${baseHref}`);\n    }\n    const hostSplit = result[4].split(':');\n    parsedUrl = {\n      protocol: result[1],\n      hostname: hostSplit[0],\n      port: hostSplit[1] || '',\n      pathname: result[5],\n      search: result[6],\n      hash: result[8],\n    };\n  }\n  if (parsedUrl.pathname && parsedUrl.pathname.indexOf(baseHref) === 0) {\n    parsedUrl.pathname = parsedUrl.pathname.substring(baseHref.length);\n  }\n  return {\n    hostname: (!serverBase && parsedUrl.hostname) || '',\n    protocol: (!serverBase && parsedUrl.protocol) || '',\n    port: (!serverBase && parsedUrl.port) || '',\n    pathname: parsedUrl.pathname || '/',\n    search: parsedUrl.search || '',\n    hash: parsedUrl.hash || '',\n  };\n}\n\n/**\n * Mock platform location config\n *\n * @publicApi\n */\nexport interface MockPlatformLocationConfig {\n  startUrl?: string;\n  appBaseHref?: string;\n}\n\n/**\n * Provider for mock platform location config\n *\n * @publicApi\n */\nexport const MOCK_PLATFORM_LOCATION_CONFIG = new InjectionToken<MockPlatformLocationConfig>(\n  'MOCK_PLATFORM_LOCATION_CONFIG',\n);\n\n/**\n * Mock implementation of URL state.\n *\n * @publicApi\n */\n@Injectable()\nexport class MockPlatformLocation implements PlatformLocation {\n  private baseHref: string = '';\n  private hashUpdate = new Subject<LocationChangeEvent>();\n  private popStateSubject = new Subject<LocationChangeEvent>();\n  private urlChangeIndex: number = 0;\n  private urlChanges: {\n    hostname: string;\n    protocol: string;\n    port: string;\n    pathname: string;\n    search: string;\n    hash: string;\n    state: unknown;\n  }[] = [{hostname: '', protocol: '', port: '', pathname: '/', search: '', hash: '', state: null}];\n\n  constructor(\n    @Inject(MOCK_PLATFORM_LOCATION_CONFIG) @Optional() config?: MockPlatformLocationConfig,\n  ) {\n    if (config) {\n      this.baseHref = config.appBaseHref || '';\n\n      const parsedChanges = this.parseChanges(\n        null,\n        config.startUrl || 'http://_empty_/',\n        this.baseHref,\n      );\n      this.urlChanges[0] = {...parsedChanges};\n    }\n  }\n\n  get hostname() {\n    return this.urlChanges[this.urlChangeIndex].hostname;\n  }\n  get protocol() {\n    return this.urlChanges[this.urlChangeIndex].protocol;\n  }\n  get port() {\n    return this.urlChanges[this.urlChangeIndex].port;\n  }\n  get pathname() {\n    return this.urlChanges[this.urlChangeIndex].pathname;\n  }\n  get search() {\n    return this.urlChanges[this.urlChangeIndex].search;\n  }\n  get hash() {\n    return this.urlChanges[this.urlChangeIndex].hash;\n  }\n  get state() {\n    return this.urlChanges[this.urlChangeIndex].state;\n  }\n\n  getBaseHrefFromDOM(): string {\n    return this.baseHref;\n  }\n\n  onPopState(fn: LocationChangeListener): VoidFunction {\n    const subscription = this.popStateSubject.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n\n  onHashChange(fn: LocationChangeListener): VoidFunction {\n    const subscription = this.hashUpdate.subscribe(fn);\n    return () => subscription.unsubscribe();\n  }\n\n  get href(): string {\n    let url = `${this.protocol}//${this.hostname}${this.port ? ':' + this.port : ''}`;\n    url += `${this.pathname === '/' ? '' : this.pathname}${this.search}${this.hash}`;\n    return url;\n  }\n\n  get url(): string {\n    return `${this.pathname}${this.search}${this.hash}`;\n  }\n\n  private parseChanges(state: unknown, url: string, baseHref: string = '') {\n    // When the `history.state` value is stored, it is always copied.\n    state = JSON.parse(JSON.stringify(state));\n    return {...parseUrl(url, baseHref), state};\n  }\n\n  replaceState(state: any, title: string, newUrl: string): void {\n    const {pathname, search, state: parsedState, hash} = this.parseChanges(state, newUrl);\n\n    this.urlChanges[this.urlChangeIndex] = {\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState,\n    };\n  }\n\n  pushState(state: any, title: string, newUrl: string): void {\n    const {pathname, search, state: parsedState, hash} = this.parseChanges(state, newUrl);\n    if (this.urlChangeIndex > 0) {\n      this.urlChanges.splice(this.urlChangeIndex + 1);\n    }\n    this.urlChanges.push({\n      ...this.urlChanges[this.urlChangeIndex],\n      pathname,\n      search,\n      hash,\n      state: parsedState,\n    });\n    this.urlChangeIndex = this.urlChanges.length - 1;\n  }\n\n  forward(): void {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex < this.urlChanges.length) {\n      this.urlChangeIndex++;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n\n  back(): void {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    if (this.urlChangeIndex > 0) {\n      this.urlChangeIndex--;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n\n  historyGo(relativePosition: number = 0): void {\n    const oldUrl = this.url;\n    const oldHash = this.hash;\n    const nextPageIndex = this.urlChangeIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this.urlChanges.length) {\n      this.urlChangeIndex = nextPageIndex;\n    }\n    this.emitEvents(oldHash, oldUrl);\n  }\n\n  getState(): unknown {\n    return this.state;\n  }\n\n  /**\n   * Browsers are inconsistent in when they fire events and perform the state updates\n   * The most easiest thing to do in our mock is synchronous and that happens to match\n   * Firefox and Chrome, at least somewhat closely\n   *\n   * https://github.com/WICG/navigation-api#watching-for-navigations\n   * https://docs.google.com/document/d/1Pdve-DJ1JCGilj9Yqf5HxRJyBKSel5owgOvUJqTauwU/edit#heading=h.3ye4v71wsz94\n   * popstate is always sent before hashchange:\n   * https://developer.mozilla.org/en-US/docs/Web/API/Window/popstate_event#when_popstate_is_sent\n   */\n  private emitEvents(oldHash: string, oldUrl: string) {\n    this.popStateSubject.next({\n      type: 'popstate',\n      state: this.getState(),\n      oldUrl,\n      newUrl: this.url,\n    } as LocationChangeEvent);\n    if (oldHash !== this.hash) {\n      this.hashUpdate.next({\n        type: 'hashchange',\n        state: null,\n        oldUrl,\n        newUrl: this.url,\n      } as LocationChangeEvent);\n    }\n  }\n}\n\n/**\n * Mock implementation of URL state.\n */\n@Injectable()\nexport class FakeNavigationPlatformLocation implements PlatformLocation {\n  private _platformNavigation = inject(PlatformNavigation) as FakeNavigation;\n  private window = inject(DOCUMENT).defaultView!;\n\n  constructor() {\n    if (!(this._platformNavigation instanceof FakeNavigation)) {\n      throw new Error(\n        'FakePlatformNavigation cannot be used without FakeNavigation. Use ' +\n          '`provideFakeNavigation` to have all these services provided together.',\n      );\n    }\n  }\n\n  private config = inject(MOCK_PLATFORM_LOCATION_CONFIG, {optional: true});\n  getBaseHrefFromDOM(): string {\n    return this.config?.appBaseHref ?? '';\n  }\n\n  onPopState(fn: LocationChangeListener): VoidFunction {\n    this.window.addEventListener('popstate', fn);\n    return () => this.window.removeEventListener('popstate', fn);\n  }\n\n  onHashChange(fn: LocationChangeListener): VoidFunction {\n    this.window.addEventListener('hashchange', fn as any);\n    return () => this.window.removeEventListener('hashchange', fn as any);\n  }\n\n  get href(): string {\n    return this._platformNavigation.currentEntry.url!;\n  }\n  get protocol(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).protocol;\n  }\n  get hostname(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).hostname;\n  }\n  get port(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).port;\n  }\n  get pathname(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).pathname;\n  }\n  get search(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).search;\n  }\n  get hash(): string {\n    return new URL(this._platformNavigation.currentEntry.url!).hash;\n  }\n\n  pushState(state: any, title: string, url: string): void {\n    this._platformNavigation.pushState(state, title, url);\n  }\n\n  replaceState(state: any, title: string, url: string): void {\n    this._platformNavigation.replaceState(state, title, url);\n  }\n\n  forward(): void {\n    this._platformNavigation.forward();\n  }\n\n  back(): void {\n    this._platformNavigation.back();\n  }\n\n  historyGo(relativePosition: number = 0): void {\n    this._platformNavigation.go(relativePosition);\n  }\n\n  getState(): unknown {\n    return this._platformNavigation.currentEntry.getHistoryState();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT, PlatformLocation} from '@angular/common';\nimport {inject, Provider} from '@angular/core';\n\n// @ng_package: ignore-cross-repo-import\nimport {PlatformNavigation} from '../../../src/navigation/platform_navigation';\nimport {\n  FakeNavigationPlatformLocation,\n  MOCK_PLATFORM_LOCATION_CONFIG,\n} from '../mock_platform_location';\n\nimport {FakeNavigation} from './fake_navigation';\n\n/**\n * Return a provider for the `FakeNavigation` in place of the real Navigation API.\n */\nexport function provideFakePlatformNavigation(): Provider[] {\n  return [\n    {\n      provide: PlatformNavigation,\n      useFactory: () => {\n        const config = inject(MOCK_PLATFORM_LOCATION_CONFIG, {optional: true});\n        return new FakeNavigation(\n          inject(DOCUMENT).defaultView!,\n          (config?.startUrl as `http${string}`) ?? 'http://_empty_/',\n        );\n      },\n    },\n    {provide: PlatformLocation, useClass: FakeNavigationPlatformLocation},\n  ];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Location,\n  LocationStrategy,\n  ɵnormalizeQueryParams as normalizeQueryParams,\n} from '@angular/common';\nimport {EventEmitter, Injectable} from '@angular/core';\nimport {SubscriptionLike} from 'rxjs';\n\n/**\n * A spy for {@link Location} that allows tests to fire simulated location events.\n *\n * @publicApi\n */\n@Injectable()\nexport class SpyLocation implements Location {\n  urlChanges: string[] = [];\n  private _history: LocationState[] = [new LocationState('', '', null)];\n  private _historyIndex: number = 0;\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  /** @internal */\n  _basePath: string = '';\n  /** @internal */\n  _locationStrategy: LocationStrategy = null!;\n  /** @internal */\n  _urlChangeListeners: ((url: string, state: unknown) => void)[] = [];\n  /** @internal */\n  _urlChangeSubscription: SubscriptionLike | null = null;\n\n  /** @nodoc */\n  ngOnDestroy(): void {\n    this._urlChangeSubscription?.unsubscribe();\n    this._urlChangeListeners = [];\n  }\n\n  setInitialPath(url: string) {\n    this._history[this._historyIndex].path = url;\n  }\n\n  setBaseHref(url: string) {\n    this._basePath = url;\n  }\n\n  path(): string {\n    return this._history[this._historyIndex].path;\n  }\n\n  getState(): unknown {\n    return this._history[this._historyIndex].state;\n  }\n\n  isCurrentPathEqualTo(path: string, query: string = ''): boolean {\n    const givenPath = path.endsWith('/') ? path.substring(0, path.length - 1) : path;\n    const currPath = this.path().endsWith('/')\n      ? this.path().substring(0, this.path().length - 1)\n      : this.path();\n\n    return currPath == givenPath + (query.length > 0 ? '?' + query : '');\n  }\n\n  simulateUrlPop(pathname: string) {\n    this._subject.emit({'url': pathname, 'pop': true, 'type': 'popstate'});\n  }\n\n  simulateHashChange(pathname: string) {\n    const path = this.prepareExternalUrl(pathname);\n    this.pushHistory(path, '', null);\n\n    this.urlChanges.push('hash: ' + pathname);\n    // the browser will automatically fire popstate event before each `hashchange` event, so we need\n    // to simulate it.\n    this._subject.emit({'url': pathname, 'pop': true, 'type': 'popstate'});\n    this._subject.emit({'url': pathname, 'pop': true, 'type': 'hashchange'});\n  }\n\n  prepareExternalUrl(url: string): string {\n    if (url.length > 0 && !url.startsWith('/')) {\n      url = '/' + url;\n    }\n    return this._basePath + url;\n  }\n\n  go(path: string, query: string = '', state: any = null) {\n    path = this.prepareExternalUrl(path);\n\n    this.pushHistory(path, query, state);\n\n    const locationState = this._history[this._historyIndex - 1];\n    if (locationState.path == path && locationState.query == query) {\n      return;\n    }\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push(url);\n    this._notifyUrlChangeListeners(path + normalizeQueryParams(query), state);\n  }\n\n  replaceState(path: string, query: string = '', state: any = null) {\n    path = this.prepareExternalUrl(path);\n\n    const history = this._history[this._historyIndex];\n\n    history.state = state;\n\n    if (history.path == path && history.query == query) {\n      return;\n    }\n\n    history.path = path;\n    history.query = query;\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.urlChanges.push('replace: ' + url);\n    this._notifyUrlChangeListeners(path + normalizeQueryParams(query), state);\n  }\n\n  forward() {\n    if (this._historyIndex < this._history.length - 1) {\n      this._historyIndex++;\n      this._subject.emit({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate',\n      });\n    }\n  }\n\n  back() {\n    if (this._historyIndex > 0) {\n      this._historyIndex--;\n      this._subject.emit({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate',\n      });\n    }\n  }\n\n  historyGo(relativePosition: number = 0): void {\n    const nextPageIndex = this._historyIndex + relativePosition;\n    if (nextPageIndex >= 0 && nextPageIndex < this._history.length) {\n      this._historyIndex = nextPageIndex;\n      this._subject.emit({\n        'url': this.path(),\n        'state': this.getState(),\n        'pop': true,\n        'type': 'popstate',\n      });\n    }\n  }\n\n  onUrlChange(fn: (url: string, state: unknown) => void): VoidFunction {\n    this._urlChangeListeners.push(fn);\n\n    this._urlChangeSubscription ??= this.subscribe((v) => {\n      this._notifyUrlChangeListeners(v.url, v.state);\n    });\n\n    return () => {\n      const fnIndex = this._urlChangeListeners.indexOf(fn);\n      this._urlChangeListeners.splice(fnIndex, 1);\n\n      if (this._urlChangeListeners.length === 0) {\n        this._urlChangeSubscription?.unsubscribe();\n        this._urlChangeSubscription = null;\n      }\n    };\n  }\n\n  /** @internal */\n  _notifyUrlChangeListeners(url: string = '', state: unknown) {\n    this._urlChangeListeners.forEach((fn) => fn(url, state));\n  }\n\n  subscribe(\n    onNext: (value: any) => void,\n    onThrow?: ((error: any) => void) | null,\n    onReturn?: (() => void) | null,\n  ): SubscriptionLike {\n    return this._subject.subscribe({next: onNext, error: onThrow, complete: onReturn});\n  }\n\n  normalize(url: string): string {\n    return null!;\n  }\n\n  private pushHistory(path: string, query: string, state: any) {\n    if (this._historyIndex > 0) {\n      this._history.splice(this._historyIndex + 1);\n    }\n    this._history.push(new LocationState(path, query, state));\n    this._historyIndex = this._history.length - 1;\n  }\n}\n\nclass LocationState {\n  constructor(\n    public path: string,\n    public query: string,\n    public state: any,\n  ) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {LocationStrategy} from '@angular/common';\nimport {EventEmitter, Injectable} from '@angular/core';\n\n/**\n * A mock implementation of {@link LocationStrategy} that allows tests to fire simulated\n * location events.\n *\n * @publicApi\n */\n@Injectable()\nexport class MockLocationStrategy extends LocationStrategy {\n  internalBaseHref: string = '/';\n  internalPath: string = '/';\n  internalTitle: string = '';\n  urlChanges: string[] = [];\n  /** @internal */\n  _subject: EventEmitter<any> = new EventEmitter();\n  private stateChanges: any[] = [];\n  constructor() {\n    super();\n  }\n\n  simulatePopState(url: string): void {\n    this.internalPath = url;\n    this._subject.emit(new _MockPopStateEvent(this.path()));\n  }\n\n  override path(includeHash: boolean = false): string {\n    return this.internalPath;\n  }\n\n  override prepareExternalUrl(internal: string): string {\n    if (internal.startsWith('/') && this.internalBaseHref.endsWith('/')) {\n      return this.internalBaseHref + internal.substring(1);\n    }\n    return this.internalBaseHref + internal;\n  }\n\n  override pushState(ctx: any, title: string, path: string, query: string): void {\n    // Add state change to changes array\n    this.stateChanges.push(ctx);\n\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push(externalUrl);\n  }\n\n  override replaceState(ctx: any, title: string, path: string, query: string): void {\n    // Reset the last index of stateChanges to the ctx (state) object\n    this.stateChanges[(this.stateChanges.length || 1) - 1] = ctx;\n\n    this.internalTitle = title;\n\n    const url = path + (query.length > 0 ? '?' + query : '');\n    this.internalPath = url;\n\n    const externalUrl = this.prepareExternalUrl(url);\n    this.urlChanges.push('replace: ' + externalUrl);\n  }\n\n  override onPopState(fn: (value: any) => void): void {\n    this._subject.subscribe({next: fn});\n  }\n\n  override getBaseHref(): string {\n    return this.internalBaseHref;\n  }\n\n  override back(): void {\n    if (this.urlChanges.length > 0) {\n      this.urlChanges.pop();\n      this.stateChanges.pop();\n      const nextUrl = this.urlChanges.length > 0 ? this.urlChanges[this.urlChanges.length - 1] : '';\n      this.simulatePopState(nextUrl);\n    }\n  }\n\n  override forward(): void {\n    throw 'not implemented';\n  }\n\n  override getState(): unknown {\n    return this.stateChanges[(this.stateChanges.length || 1) - 1];\n  }\n}\n\nclass _MockPopStateEvent {\n  pop: boolean = true;\n  type: string = 'popstate';\n  constructor(public newUrl: string) {}\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Location, LocationStrategy} from '@angular/common';\nimport {Provider} from '@angular/core';\n\nimport {SpyLocation} from './location_mock';\nimport {MockLocationStrategy} from './mock_location_strategy';\n\n/**\n * Returns mock providers for the `Location` and `LocationStrategy` classes.\n * The mocks are helpful in tests to fire simulated location events.\n *\n * @publicApi\n */\nexport function provideLocationMocks(): Provider[] {\n  return [\n    {provide: Location, useClass: SpyLocation},\n    {provide: LocationStrategy, useClass: MockLocationStrategy},\n  ];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the common/testing package.\n */\n\nexport * from './private_export';\nexport {SpyLocation} from './location_mock';\nexport {MockLocationStrategy} from './mock_location_strategy';\nexport {\n  MOCK_PLATFORM_LOCATION_CONFIG,\n  MockPlatformLocation,\n  MockPlatformLocationConfig,\n} from './mock_platform_location';\nexport {provideLocationMocks} from './provide_location_mocks';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/testing';\n\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["PlatformNavigation", "normalizeQueryParams"], "mappings": ";;;;;;;;;;;AAuBA;;;AAGG;MAEmB,kBAAkB,CAAA;yHAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAlB,kBAAkB,EAAA,UAAA,EADf,UAAU,EAAc,UAAA,EAAA,MAAO,MAAc,CAAC,UAAU,EAAA,CAAA,CAAA,EAAA;;sGAC3D,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBADvC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,MAAO,MAAc,CAAC,UAAU,EAAC,CAAA;;;ACHlF;;;;AAIG;MACU,cAAc,CAAA;;AAyDzB,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;KAChD;AAED,IAAA,IAAI,SAAS,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;KACnC;AAED,IAAA,IAAI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;KAC5D;IAED,WACmB,CAAA,MAAc,EAC/B,QAAyB,EAAA;QADR,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;AArEjC;;;AAGG;QACc,IAAU,CAAA,UAAA,GAAiC,EAAE,CAAC;AAE/D;;AAEG;QACK,IAAiB,CAAA,iBAAA,GAAG,CAAC,CAAC;AAE9B;;AAEG;QACK,IAAa,CAAA,aAAA,GAA0C,SAAS,CAAC;AAEzE;;;AAGG;AACc,QAAA,IAAA,CAAA,cAAc,GAAG,IAAI,GAAG,EAAoC,CAAC;AAE9E;;;AAGG;AACK,QAAA,IAAA,CAAA,aAAa,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;AAE1C;;;AAGG;QACK,IAAqB,CAAA,qBAAA,GAAG,CAAC,CAAC;AAElC;;;AAGG;QACK,IAAqB,CAAA,qBAAA,GAAG,KAAK,CAAC;;QAG9B,IAAkB,CAAA,kBAAA,GAAG,IAAI,CAAC;;QAG1B,IAAW,CAAA,WAAA,GAAgB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;;QAGrE,IAAM,CAAA,MAAA,GAAG,CAAC,CAAC;;QAGX,IAAO,CAAA,OAAA,GAAG,CAAC,CAAC;;QAGZ,IAAQ,CAAA,QAAA,GAAG,KAAK,CAAC;;AAoBvB,QAAA,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;KAC1C;AAED;;AAEG;IACK,yBAAyB,CAC/B,GAAoB,EACpB,OAAA,GAAoD,EAAC,YAAY,EAAE,IAAI,EAAC,EAAA;AAExE,QAAA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;AAC5B,YAAA,MAAM,IAAI,KAAK,CACb,0DAA0D,GAAG,yBAAyB,CACvF,CAAC;SACH;QACD,MAAM,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC/C,QAAA,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,0BAA0B,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,EAAE;AAC3E,YAAA,KAAK,EAAE,CAAC;YACR,GAAG,EAAE,mBAAmB,EAAE,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACvD,EAAE,EAAE,mBAAmB,EAAE,EAAE,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;AACpD,YAAA,YAAY,EAAE,IAAI;YAClB,YAAY,EAAE,OAAO,EAAE,YAAY;YACnC,KAAK,EAAE,OAAO,CAAC,KAAK;AACrB,SAAA,CAAC,CAAC;KACJ;;IAGD,4BAA4B,GAAA;QAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC;KAChC;AAED;;;AAGG;AACH,IAAA,kCAAkC,CAAC,qBAA8B,EAAA;AAC/D,QAAA,IAAI,CAAC,qBAAqB,GAAG,qBAAqB,CAAC;KACpD;;IAGD,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,CAAC;KAChC;;IAGD,QAAQ,CAAC,GAAW,EAAE,OAAmC,EAAA;QACvD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC;AAChD,QAAA,MAAM,KAAK,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC;AAEnD,QAAA,IAAI,cAAoC,CAAC;QACzC,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,MAAM,EAAE;;YAEnD,IAAI,OAAO,CAAC,QAAQ,EAAE,KAAK,KAAK,CAAC,QAAQ,EAAE,EAAE;gBAC3C,cAAc,GAAG,SAAS,CAAC;aAC5B;iBAAM;gBACL,cAAc,GAAG,MAAM,CAAC;aACzB;SACF;aAAM;AACL,YAAA,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC;SAClC;QAED,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAEhD,QAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;AAChD,YAAA,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE;YACrB,KAAK,EAAE,OAAO,EAAE,KAAK;AACrB,YAAA,YAAY,EAAE,UAAU;AACxB,YAAA,YAAY,EAAE,IAAI;AACnB,SAAA,CAAC,CAAC;AACH,QAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,EAAE,CAAC;AAE9C,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;YAC1C,cAAc;AACd,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,YAAY,EAAE,IAAI;;AAElB,YAAA,aAAa,EAAE,KAAK;YACpB,UAAU;YACV,IAAI,EAAE,OAAO,EAAE,IAAI;AACpB,SAAA,CAAC,CAAC;QAEH,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC1B,CAAC;KACH;;AAGD,IAAA,SAAS,CAAC,IAAa,EAAE,KAAa,EAAE,GAAY,EAAA;QAClD,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KACnD;;AAGD,IAAA,YAAY,CAAC,IAAa,EAAE,KAAa,EAAE,GAAY,EAAA;QACrD,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KACtD;AAEO,IAAA,kBAAkB,CACxB,cAAoC,EACpC,IAAa,EACb,MAAc,EACd,GAAY,EAAA;QAEZ,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC;QAChD,MAAM,KAAK,GAAG,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,GAAG,OAAO,CAAC;QAEnE,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAEhD,QAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;AAChD,YAAA,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE;AACrB,YAAA,YAAY,EAAE,IAAI;AAClB,YAAA,YAAY,EAAE,IAAI;AACnB,SAAA,CAAC,CAAC;AACH,QAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,EAAE,CAAC;AAE9C,QAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;YAC1C,cAAc;AACd,YAAA,UAAU,EAAE,IAAI;AAChB,YAAA,YAAY,EAAE,IAAI;;AAElB,YAAA,aAAa,EAAE,KAAK;YACpB,UAAU;AACV,YAAA,YAAY,EAAE,IAAI;AACnB,SAAA,CAAC,CAAC;KACJ;;IAGD,UAAU,CAAC,GAAW,EAAE,OAA2B,EAAA;QACjD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC;QAChD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE;YACV,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,aAAa,EAAE,mBAAmB,CAAC,CAAC;YAC1E,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC/C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC9C,SAAS,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAC;YAC1B,QAAQ,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAC;YACzB,OAAO;gBACL,SAAS;gBACT,QAAQ;aACT,CAAC;SACH;AACD,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,YAAY,EAAE;YAC/B,OAAO;gBACL,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;gBAC7C,QAAQ,EAAE,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC;aAC7C,CAAC;SACH;QACD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACtC,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAE,CAAC;YAC3D,OAAO;gBACL,SAAS,EAAE,cAAc,CAAC,SAAS;gBACnC,QAAQ,EAAE,cAAc,CAAC,QAAQ;aAClC,CAAC;SACH;QAED,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,GAAI,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,CAAC;AACtF,QAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;YAChD,GAAG,EAAE,KAAK,CAAC,GAAI;AACf,YAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACvB,YAAA,YAAY,EAAE,KAAK,CAAC,eAAe,EAAE;YACrC,GAAG,EAAE,KAAK,CAAC,GAAG;YACd,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,YAAY,EAAE,KAAK,CAAC,YAAY;AACjC,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC,KAAK,CAAC;AACzC,QAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,EAAE,CAAC;QAC9C,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC3C,QAAA,IAAI,CAAC,YAAY,CAAC,MAAK;YACrB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACtC,YAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;AAC1C,gBAAA,cAAc,EAAE,UAAU;AAC1B,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,YAAY,EAAE,IAAI;;AAElB,gBAAA,aAAa,EAAE,KAAK;gBACpB,UAAU;gBACV,IAAI,EAAE,OAAO,EAAE,IAAI;AACpB,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;QACH,OAAO;YACL,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC1B,CAAC;KACH;;AAGD,IAAA,IAAI,CAAC,OAA2B,EAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,EAAE;YAChC,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,CAAC;YAC7E,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC/C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC9C,SAAS,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAC;YAC1B,QAAQ,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAC;YACzB,OAAO;gBACL,SAAS;gBACT,QAAQ;aACT,CAAC;SACH;AACD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KAC5C;;AAGD,IAAA,OAAO,CAAC,OAA2B,EAAA;AACjC,QAAA,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACzD,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC,mBAAmB,EAAE,mBAAmB,CAAC,CAAC;YAChF,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC/C,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC9C,SAAS,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAC;YAC1B,QAAQ,CAAC,KAAK,CAAC,MAAO,GAAC,CAAC,CAAC;YACzB,OAAO;gBACL,SAAS;gBACT,QAAQ;aACT,CAAC;SACH;AACD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;QAC1D,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;KAC5C;AAED;;;;;;AAMG;AACH,IAAA,EAAE,CAAC,SAAiB,EAAA;AAClB,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;AAC3D,QAAA,IAAI,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,WAAW,GAAG,CAAC,EAAE;YAC5D,OAAO;SACR;AACD,QAAA,IAAI,CAAC,qBAAqB,GAAG,WAAW,CAAC;AACzC,QAAA,IAAI,CAAC,YAAY,CAAC,MAAK;;AAErB,YAAA,IAAI,WAAW,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,WAAW,GAAG,CAAC,EAAE;gBAC5D,OAAO;aACR;YACD,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC;YAChD,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAC3C,MAAM,UAAU,GAAG,YAAY,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,KAAK,CAAC,GAAI,EAAE,IAAI,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,CAAC;AACtF,YAAA,MAAM,WAAW,GAAG,IAAI,yBAAyB,CAAC;gBAChD,GAAG,EAAE,KAAK,CAAC,GAAI;AACf,gBAAA,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;AACvB,gBAAA,YAAY,EAAE,KAAK,CAAC,eAAe,EAAE;gBACrC,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,YAAY,EAAE,KAAK,CAAC,YAAY;AACjC,aAAA,CAAC,CAAC;AACH,YAAA,MAAM,MAAM,GAAG,IAAI,wBAAwB,EAAE,CAAC;AAC9C,YAAA,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,MAAM,EAAE;AAC1C,gBAAA,cAAc,EAAE,UAAU;AAC1B,gBAAA,UAAU,EAAE,IAAI;AAChB,gBAAA,YAAY,EAAE,IAAI;;AAElB,gBAAA,aAAa,EAAE,KAAK;gBACpB,UAAU;AACX,aAAA,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;;AAGO,IAAA,YAAY,CAAC,SAAqB,EAAA;AACxC,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAC9B,YAAA,SAAS,EAAE,CAAC;YACZ,OAAO;SACR;;;;QAKD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAK;AAChD,YAAA,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,KAAI;gBACnC,UAAU,CAAC,MAAK;AACd,oBAAA,OAAO,EAAE,CAAC;AACV,oBAAA,SAAS,EAAE,CAAC;AACd,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC,CAAC;AACL,SAAC,CAAC,CAAC;KACJ;;AAGD,IAAA,gBAAgB,CACd,IAAY,EACZ,QAA4C,EAC5C,OAA2C,EAAA;QAE3C,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;KAC5D;;AAGD,IAAA,mBAAmB,CACjB,IAAY,EACZ,QAA4C,EAC5C,OAAwC,EAAA;QAExC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC;KAC/D;;AAGD,IAAA,aAAa,CAAC,KAAY,EAAA;QACxB,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KAC9C;;IAGD,OAAO,GAAA;;;AAGL,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC7D,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;KACtB;;IAGD,UAAU,GAAA;QACR,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;;AAGO,IAAA,iBAAiB,CACvB,WAAsC,EACtC,MAAgC,EAChC,OAAgC,EAAA;;;AAIhC,QAAA,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC;AAChC,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,YAAY,CAAC,wBAAwB,EAAE,YAAY,CAAC,CAAC,CAAC;AACpF,YAAA,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;SAChC;QAED,MAAM,aAAa,GAAG,uBAAuB,CAAC;YAC5C,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,WAAW;YACX,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,YAAY,EAAE,WAAW,CAAC,YAAY;YACtC,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,MAAM;YACN,eAAe,EAAE,MAAK;gBACpB,IAAI,CAAC,eAAe,EAAE,CAAC;aACxB;AACF,SAAA,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;QAC9C,aAAa,CAAC,uBAAuB,EAAE,CAAC;AACxC,QAAA,IAAI,aAAa,CAAC,YAAY,KAAK,WAAW,EAAE;AAC9C,YAAA,aAAa,CAAC,MAAM,iBAAiB,IAAI,CAAC,CAAC;SAC5C;KACF;;IAGO,eAAe,GAAA;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvB,OAAO;SACR;AACD,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;AACpC,YAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AACvE,YAAA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACjC,YAAA,MAAM,KAAK,CAAC;SACb;AACD,QAAA,IACE,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,MAAM;AAC5C,YAAA,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,SAAS,EAC/C;YACA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE;AAC1D,gBAAA,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;AAClD,aAAA,CAAC,CAAC;SACJ;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,KAAK,UAAU,EAAE;YAC3D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACzD,MAAM,uBAAuB,GAAG,2CAA2C,CAAC;YAC1E,IAAI;AACJ,YAAA,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,cAAc;AAClD,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;AACxD,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE;YACpC,MAAM,aAAa,GAAG,mBAAmB,CAAC;gBACxC,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,eAAe,EAAE;AACxD,aAAA,CAAC,CAAC;AACH,YAAA,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;SAC1C;KACF;;AAGO,IAAA,sBAAsB,CAC5B,WAAsC,EACtC,EAAC,cAAc,EAAyC,EAAA;AAExD,QAAA,IAAI,cAAc,KAAK,MAAM,EAAE;YAC7B,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACzB,YAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,iBAAiB,CAAC;SACrD;AACD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACrC,MAAM,GAAG,GAAG,cAAc,KAAK,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QACvF,MAAM,KAAK,GAAG,IAAI,0BAA0B,CAAC,WAAW,CAAC,GAAG,EAAE;AAC5D,YAAA,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,GAAG;YACH,KAAK;AACL,YAAA,YAAY,EAAE,IAAI;AAClB,YAAA,KAAK,EAAE,WAAW,CAAC,QAAQ,EAAE;AAC7B,YAAA,YAAY,EAAE,WAAW,CAAC,eAAe,EAAE;AAC5C,SAAA,CAAC,CAAC;AACH,QAAA,IAAI,cAAc,KAAK,MAAM,EAAE;YAC7B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;SAChD;aAAM;AACL,YAAA,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;SAChC;KACF;;AAGO,IAAA,iBAAiB,CAAC,WAAsC,EAAA;AAC9D,QAAA,IAAI,CAAC,iBAAiB,GAAG,WAAW,CAAC,KAAK,CAAC;KAC5C;;AAGO,IAAA,SAAS,CAAC,GAAW,EAAA;AAC3B,QAAA,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,UAAU,EAAE;AACnC,YAAA,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG;AAAE,gBAAA,OAAO,KAAK,CAAC;SACrC;AACD,QAAA,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,UAAU,CAAC,QAA+D,EAAA;AAC5E,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AAED,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;IAED,IAAI,oBAAoB,CACtB,QAAmF,EAAA;AAEnF,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AAED,IAAA,IAAI,oBAAoB,GAAA;AAGtB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;IAED,IAAI,iBAAiB,CAAC,QAAuD,EAAA;AAC3E,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AAED,IAAA,IAAI,iBAAiB,GAAA;AACnB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;IAED,IAAI,eAAe,CAAC,QAA4D,EAAA;AAC9E,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AAED,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AAED,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AAED,IAAA,kBAAkB,CAAC,QAA6C,EAAA;AAC9D,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AAED,IAAA,MAAM,CAAC,QAAkC,EAAA;AACvC,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AACF,CAAA;AAWD;;AAEG;MACU,0BAA0B,CAAA;AAYrC,IAAA,WAAA,CACW,GAAkB,EAC3B,EACE,EAAE,EACF,GAAG,EACH,KAAK,EACL,YAAY,EACZ,KAAK,EACL,YAAY,GAQb,EAAA;QAfQ,IAAG,CAAA,GAAA,GAAH,GAAG,CAAe;;QAH7B,IAAS,CAAA,SAAA,GAA8D,IAAI,CAAC;AAoB1E,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACb,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACf,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;KAClC;IAED,QAAQ,GAAA;;QAEN,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;KACzE;IAED,eAAe,GAAA;;QAEb,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC;KAC9F;AAED,IAAA,gBAAgB,CACd,IAAY,EACZ,QAA4C,EAC5C,OAA2C,EAAA;AAE3C,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AAED,IAAA,mBAAmB,CACjB,IAAY,EACZ,QAA4C,EAC5C,OAAwC,EAAA;AAExC,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AAED,IAAA,aAAa,CAAC,KAAY,EAAA;AACxB,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;KAClC;AACF,CAAA;AAiCD;;;AAGG;AACH,SAAS,uBAAuB,CAAC,EAC/B,UAAU,EACV,YAAY,EACZ,aAAa,EACb,UAAU,EACV,cAAc,EACd,MAAM,EACN,WAAW,EACX,IAAI,EACJ,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,eAAe,GAchB,EAAA;AACC,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,UAAU,EAAC,CAE/D,CAAC;AACF,IAAA,KAAK,CAAC,YAAY,GAAG,YAAY,CAAC;AAClC,IAAA,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;AACpC,IAAA,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;AAC9B,IAAA,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;AACtC,IAAA,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AACtB,IAAA,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;AAChC,IAAA,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AAClB,IAAA,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC;AAC7B,IAAA,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AAEtB,IAAA,KAAK,CAAC,YAAY,GAAG,YAAY,CAAC;AAClC,IAAA,KAAK,CAAC,YAAY,GAAG,YAAY,CAAC;AAClC,IAAA,KAAK,CAAC,YAAY,GAAG,WAAW,CAAC;IAEjC,IAAI,eAAe,GAA8B,SAAS,CAAC;IAC3D,IAAI,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAI,uBAAuB,GAAG,KAAK,CAAC;IACpC,IAAI,YAAY,GAAG,KAAK,CAAC;AAEzB,IAAA,KAAK,CAAC,SAAS,GAAG,UAEhB,OAAgD,EAAA;QAEhD,eAAe,GAAG,IAAI,CAAC;AACvB,QAAA,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;AAC1B,QAAA,MAAM,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC;QACjC,IAAI,OAAO,EAAE;YACX,eAAe,GAAG,OAAO,EAAE,CAAC;SAC7B;AACD,QAAA,IAAI,OAAO,EAAE,MAAM,EAAE;AACnB,YAAA,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC;SACrC;AACD,QAAA,IAAI,OAAO,EAAE,UAAU,KAAK,SAAS,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS,EAAE;AACtE,YAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;SAClC;AACH,KAAC,CAAC;IAEF,KAAK,CAAC,MAAM,GAAG,YAAA;AACb,QAAA,MAAM,IAAI,KAAK,CAAC,eAAe,CAAC,CAAC;AACnC,KAAC,CAAC;AAEF,IAAA,KAAK,CAAC,MAAM,GAAG,UAA2C,QAAQ,GAAG,KAAK,EAAA;AACxE,QAAA,IAAI,CAAC,QAAQ,IAAI,CAAC,eAAe,EAAE;YACjC,MAAM,IAAI,YAAY,CACpB,CAAqE,mEAAA,CAAA;gBACnE,CAAyB,uBAAA,CAAA,EAC3B,mBAAmB,CACpB,CAAC;SACH;QACD,IAAI,CAAC,uBAAuB,EAAE;YAC5B,MAAM,IAAI,YAAY,CACpB,CAAqE,mEAAA,CAAA;gBACnE,CAA+B,6BAAA,CAAA,EACjC,mBAAmB,CACpB,CAAC;SACH;QACD,IAAI,YAAY,EAAE;YAChB,MAAM,IAAI,YAAY,CACpB,CAAA,gEAAA,CAAkE,GAAG,CAAS,OAAA,CAAA,EAC9E,mBAAmB,CACpB,CAAC;SACH;QACD,YAAY,GAAG,IAAI,CAAC;AAEpB,QAAA,eAAe,EAAE,CAAC;AACpB,KAAC,CAAC;;AAGF,IAAA,KAAK,CAAC,MAAM,GAAG,UAA2C,MAAa,EAAA;AACrE,QAAA,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAC/B,QAAA,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAChC,KAAC,CAAC;;IAGF,KAAK,CAAC,uBAAuB,GAAG,YAAA;QAC9B,uBAAuB,GAAG,IAAI,CAAC;AAC/B,QAAA,IAAI,KAAK,CAAC,YAAY,KAAK,kBAAkB,EAAE;;AAE7C,YAAA,eAAe,EAAE,IAAI,CACnB,MAAK;gBACH,IAAI,CAAC,YAAY,EAAE;AACjB,oBAAA,KAAK,CAAC,MAAM,gBAAgB,IAAI,CAAC,CAAC;iBACnC;AACH,aAAC,EACD,MAAO,GAAC,CACT,CAAC;SACH;AACD,QAAA,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,CACnD,CAAC,CAAC,KAAK,CAAC,KAAI;AACV,YAAA,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAChC,SAAC,EACD,CAAC,MAAM,KAAI;AACT,YAAA,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAChC,SAAC,CACF,CAAC;AACJ,KAAC,CAAC;;AAGF,IAAA,KAAK,CAAC,kBAAkB,GAAG,UAEzB,KAAiC,EAAA;AAEjC,QAAA,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;AACjC,KAAC,CAAC;AAEF,IAAA,OAAO,KAAkC,CAAC;AAC5C,CAAC;AAOD;;;AAGG;AACH,SAAS,2CAA2C,CAAC,EACnD,IAAI,EACJ,cAAc,GAIf,EAAA;AACC,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,EAAE;AAC5C,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,UAAU,EAAE,KAAK;AAClB,KAAA,CAEA,CAAC;AACF,IAAA,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AAClB,IAAA,KAAK,CAAC,cAAc,GAAG,cAAc,CAAC;AACtC,IAAA,OAAO,KAA8C,CAAC;AACxD,CAAC;AAED;;;AAGG;AACH,SAAS,mBAAmB,CAAC,EAAC,KAAK,EAAmB,EAAA;AACpD,IAAA,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,UAAU,EAAE;AAClC,QAAA,OAAO,EAAE,KAAK;AACd,QAAA,UAAU,EAAE,KAAK;AAClB,KAAA,CAA6D,CAAC;AAC/D,IAAA,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACpB,IAAA,OAAO,KAAsB,CAAC;AAChC,CAAC;AAED;;AAEG;MACU,yBAAyB,CAAA;IAUpC,WAAY,CAAA,EACV,GAAG,EACH,YAAY,EACZ,YAAY,EACZ,KAAK,EACL,GAAG,GAAG,IAAI,EACV,EAAE,GAAG,IAAI,EACT,KAAK,GAAG,CAAC,CAAC,GASX,EAAA;AACC,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACf,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACjC,QAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACf,QAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACb,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACpB;IAED,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IAED,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;AACF,CAAA;AAED;AACA,SAAS,YAAY,CAAC,IAAS,EAAE,EAAO,EAAA;AACtC,IAAA,QACE,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI;AACrB,QAAA,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;AAC7B,QAAA,EAAE,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ;AAC7B,QAAA,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EACzB;AACJ,CAAC;AAED;AACA,MAAM,wBAAwB,CAAA;AAO5B,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;KACpC;AAGD,IAAA,WAAA,GAAA;AAFiB,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC;QAGvD,IAAI,CAAC,SAAS,GAAG,IAAI,OAAO,CAA6B,CAAC,OAAO,EAAE,MAAM,KAAI;AAC3E,YAAA,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC;AAChC,YAAA,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC;AAChC,SAAC,CAAC,CAAC;AAEH,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,OAAO,CAA6B,OAAO,OAAO,EAAE,MAAM,KAAI;AAChF,YAAA,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC;AAC/B,YAAA,IAAI,CAAC,cAAc,GAAG,CAAC,MAAa,KAAI;gBACtC,MAAM,CAAC,MAAM,CAAC,CAAC;AACf,gBAAA,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AACrC,aAAC,CAAC;AACJ,SAAC,CAAC,CAAC;;QAEH,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAK,GAAG,CAAC,CAAC;QAC/B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAK,GAAG,CAAC,CAAC;KAC/B;AACF;;ACj7BD;;;;;;;;;;;;;;;;;;AAkBG;AACH,MAAM,QAAQ,GAAG,+DAA+D,CAAC;AAEjF,SAAS,QAAQ,CAAC,MAAc,EAAE,QAAgB,EAAA;IAChD,MAAM,cAAc,GAAG,wBAAwB,CAAC;AAChD,IAAA,IAAI,UAA8B,CAAC;;;IAInC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QAChC,UAAU,GAAG,mBAAmB,CAAC;KAClC;AACD,IAAA,IAAI,SAOH,CAAC;AACF,IAAA,IAAI;QACF,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;KACzC;IAAC,OAAO,CAAC,EAAE;AACV,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,CAAA,aAAA,EAAgB,MAAM,CAAe,YAAA,EAAA,QAAQ,CAAE,CAAA,CAAC,CAAC;SAClE;QACD,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACvC,QAAA,SAAS,GAAG;AACV,YAAA,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;AACnB,YAAA,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC;AACtB,YAAA,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;AACxB,YAAA,QAAQ,EAAE,MAAM,CAAC,CAAC,CAAC;AACnB,YAAA,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;AACjB,YAAA,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC;SAChB,CAAC;KACH;AACD,IAAA,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;AACpE,QAAA,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;KACpE;IACD,OAAO;QACL,QAAQ,EAAE,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,QAAQ,KAAK,EAAE;QACnD,QAAQ,EAAE,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,QAAQ,KAAK,EAAE;QACnD,IAAI,EAAE,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,IAAI,KAAK,EAAE;AAC3C,QAAA,QAAQ,EAAE,SAAS,CAAC,QAAQ,IAAI,GAAG;AACnC,QAAA,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,EAAE;AAC9B,QAAA,IAAI,EAAE,SAAS,CAAC,IAAI,IAAI,EAAE;KAC3B,CAAC;AACJ,CAAC;AAYD;;;;AAIG;MACU,6BAA6B,GAAG,IAAI,cAAc,CAC7D,+BAA+B,EAC/B;AAEF;;;;AAIG;MAEU,oBAAoB,CAAA;AAe/B,IAAA,WAAA,CACqD,MAAmC,EAAA;QAfhF,IAAQ,CAAA,QAAA,GAAW,EAAE,CAAC;AACtB,QAAA,IAAA,CAAA,UAAU,GAAG,IAAI,OAAO,EAAuB,CAAC;AAChD,QAAA,IAAA,CAAA,eAAe,GAAG,IAAI,OAAO,EAAuB,CAAC;QACrD,IAAc,CAAA,cAAA,GAAW,CAAC,CAAC;AAC3B,QAAA,IAAA,CAAA,UAAU,GAQZ,CAAC,EAAC,QAAQ,EAAE,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAC,CAAC,CAAC;QAK/F,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;AAEzC,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CACrC,IAAI,EACJ,MAAM,CAAC,QAAQ,IAAI,iBAAiB,EACpC,IAAI,CAAC,QAAQ,CACd,CAAC;YACF,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,EAAC,GAAG,aAAa,EAAC,CAAC;SACzC;KACF;AAED,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC;KACtD;AACD,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC;KACtD;AACD,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC;KAClD;AACD,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,QAAQ,CAAC;KACtD;AACD,IAAA,IAAI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC;KACpD;AACD,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC;KAClD;AACD,IAAA,IAAI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC;KACnD;IAED,kBAAkB,GAAA;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;AAED,IAAA,UAAU,CAAC,EAA0B,EAAA;QACnC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AACxD,QAAA,OAAO,MAAM,YAAY,CAAC,WAAW,EAAE,CAAC;KACzC;AAED,IAAA,YAAY,CAAC,EAA0B,EAAA;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AACnD,QAAA,OAAO,MAAM,YAAY,CAAC,WAAW,EAAE,CAAC;KACzC;AAED,IAAA,IAAI,IAAI,GAAA;QACN,IAAI,GAAG,GAAG,CAAA,EAAG,IAAI,CAAC,QAAQ,CAAA,EAAA,EAAK,IAAI,CAAC,QAAQ,CAAA,EAAG,IAAI,CAAC,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,EAAE,CAAA,CAAE,CAAC;QAClF,GAAG,IAAI,CAAG,EAAA,IAAI,CAAC,QAAQ,KAAK,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAA,EAAG,IAAI,CAAC,MAAM,CAAA,EAAG,IAAI,CAAC,IAAI,CAAA,CAAE,CAAC;AACjF,QAAA,OAAO,GAAG,CAAC;KACZ;AAED,IAAA,IAAI,GAAG,GAAA;AACL,QAAA,OAAO,CAAG,EAAA,IAAI,CAAC,QAAQ,CAAG,EAAA,IAAI,CAAC,MAAM,CAAG,EAAA,IAAI,CAAC,IAAI,EAAE,CAAC;KACrD;AAEO,IAAA,YAAY,CAAC,KAAc,EAAE,GAAW,EAAE,WAAmB,EAAE,EAAA;;AAErE,QAAA,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QAC1C,OAAO,EAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,KAAK,EAAC,CAAC;KAC5C;AAED,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,MAAc,EAAA;QACpD,MAAM,EAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAEtF,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG;AACrC,YAAA,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC;YACvC,QAAQ;YACR,MAAM;YACN,IAAI;AACJ,YAAA,KAAK,EAAE,WAAW;SACnB,CAAC;KACH;AAED,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,MAAc,EAAA;QACjD,MAAM,EAAC,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACtF,QAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,CAAC;SACjD;AACD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACnB,YAAA,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC;YACvC,QAAQ;YACR,MAAM;YACN,IAAI;AACJ,YAAA,KAAK,EAAE,WAAW;AACnB,SAAA,CAAC,CAAC;QACH,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;KAClD;IAED,OAAO,GAAA;AACL,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;YAChD,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;AACD,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KAClC;IAED,IAAI,GAAA;AACF,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,cAAc,GAAG,CAAC,EAAE;YAC3B,IAAI,CAAC,cAAc,EAAE,CAAC;SACvB;AACD,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KAClC;IAED,SAAS,CAAC,mBAA2B,CAAC,EAAA;AACpC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;AACxB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC;AAC1B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,GAAG,gBAAgB,CAAC;AAC7D,QAAA,IAAI,aAAa,IAAI,CAAC,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAChE,YAAA,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;SACrC;AACD,QAAA,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;KAClC;IAED,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;AAED;;;;;;;;;AASG;IACK,UAAU,CAAC,OAAe,EAAE,MAAc,EAAA;AAChD,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;AACxB,YAAA,IAAI,EAAE,UAAU;AAChB,YAAA,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;YACtB,MAAM;YACN,MAAM,EAAE,IAAI,CAAC,GAAG;AACM,SAAA,CAAC,CAAC;AAC1B,QAAA,IAAI,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE;AACzB,YAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;AACnB,gBAAA,IAAI,EAAE,YAAY;AAClB,gBAAA,KAAK,EAAE,IAAI;gBACX,MAAM;gBACN,MAAM,EAAE,IAAI,CAAC,GAAG;AACM,aAAA,CAAC,CAAC;SAC3B;KACF;AAtKU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,kBAgBrB,6BAA6B,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAhB5B,oBAAoB,EAAA,CAAA,CAAA,EAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;;0BAiBN,MAAM;2BAAC,6BAA6B,CAAA;;0BAAG,QAAQ;;AAyJpD;;AAEG;MAEU,8BAA8B,CAAA;AAIzC,IAAA,WAAA,GAAA;AAHQ,QAAA,IAAA,CAAA,mBAAmB,GAAG,MAAM,CAACA,mBAAkB,CAAmB,CAAC;AACnE,QAAA,IAAA,CAAA,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAY,CAAC;QAWvC,IAAM,CAAA,MAAA,GAAG,MAAM,CAAC,6BAA6B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;QARvE,IAAI,EAAE,IAAI,CAAC,mBAAmB,YAAY,cAAc,CAAC,EAAE;YACzD,MAAM,IAAI,KAAK,CACb,oEAAoE;AAClE,gBAAA,uEAAuE,CAC1E,CAAC;SACH;KACF;IAGD,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE,WAAW,IAAI,EAAE,CAAC;KACvC;AAED,IAAA,UAAU,CAAC,EAA0B,EAAA;QACnC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAA,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;KAC9D;AAED,IAAA,YAAY,CAAC,EAA0B,EAAA;QACrC,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,EAAE,EAAS,CAAC,CAAC;AACtD,QAAA,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,YAAY,EAAE,EAAS,CAAC,CAAC;KACvE;AAED,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC;KACnD;AACD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,QAAQ,CAAC;KACrE;AACD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,QAAQ,CAAC;KACrE;AACD,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,IAAI,CAAC;KACjE;AACD,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,QAAQ,CAAC;KACrE;AACD,IAAA,IAAI,MAAM,GAAA;AACR,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,MAAM,CAAC;KACnE;AACD,IAAA,IAAI,IAAI,GAAA;AACN,QAAA,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,GAAI,CAAC,CAAC,IAAI,CAAC;KACjE;AAED,IAAA,SAAS,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAA;QAC9C,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KACvD;AAED,IAAA,YAAY,CAAC,KAAU,EAAE,KAAa,EAAE,GAAW,EAAA;QACjD,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;KAC1D;IAED,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;KACpC;IAED,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,CAAC;KACjC;IAED,SAAS,CAAC,mBAA2B,CAAC,EAAA;AACpC,QAAA,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC;KAC/C;IAED,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,eAAe,EAAE,CAAC;KAChE;yHAxEU,8BAA8B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAA9B,8BAA8B,EAAA,CAAA,CAAA,EAAA;;sGAA9B,8BAA8B,EAAA,UAAA,EAAA,CAAA;kBAD1C,UAAU;;;ACzQX;;AAEG;SACa,6BAA6B,GAAA;IAC3C,OAAO;AACL,QAAA;AACE,YAAA,OAAO,EAAE,kBAAkB;YAC3B,UAAU,EAAE,MAAK;AACf,gBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,6BAA6B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AACvE,gBAAA,OAAO,IAAI,cAAc,CACvB,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAY,EAC5B,MAAM,EAAE,QAA4B,IAAI,iBAAiB,CAC3D,CAAC;aACH;AACF,SAAA;AACD,QAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,8BAA8B,EAAC;KACtE,CAAC;AACJ;;ACrBA;;;;AAIG;MAEU,WAAW,CAAA;AADxB,IAAA,WAAA,GAAA;QAEE,IAAU,CAAA,UAAA,GAAa,EAAE,CAAC;AAClB,QAAA,IAAA,CAAA,QAAQ,GAAoB,CAAC,IAAI,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;QAC9D,IAAa,CAAA,aAAA,GAAW,CAAC,CAAC;;AAElC,QAAA,IAAA,CAAA,QAAQ,GAAsB,IAAI,YAAY,EAAE,CAAC;;QAEjD,IAAS,CAAA,SAAA,GAAW,EAAE,CAAC;;QAEvB,IAAiB,CAAA,iBAAA,GAAqB,IAAK,CAAC;;QAE5C,IAAmB,CAAA,mBAAA,GAA8C,EAAE,CAAC;;QAEpE,IAAsB,CAAA,sBAAA,GAA4B,IAAI,CAAC;AAwKxD,KAAA;;IArKC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE,CAAC;AAC3C,QAAA,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;KAC/B;AAED,IAAA,cAAc,CAAC,GAAW,EAAA;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,GAAG,GAAG,CAAC;KAC9C;AAED,IAAA,WAAW,CAAC,GAAW,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;KACtB;IAED,IAAI,GAAA;QACF,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;KAC/C;IAED,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC;KAChD;AAED,IAAA,oBAAoB,CAAC,IAAY,EAAE,KAAA,GAAgB,EAAE,EAAA;QACnD,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;QACjF,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC;AACxC,cAAE,IAAI,CAAC,IAAI,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;AAClD,cAAE,IAAI,CAAC,IAAI,EAAE,CAAC;QAEhB,OAAO,QAAQ,IAAI,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC;KACtE;AAED,IAAA,cAAc,CAAC,QAAgB,EAAA;AAC7B,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAC,CAAC,CAAC;KACxE;AAED,IAAA,kBAAkB,CAAC,QAAgB,EAAA;QACjC,MAAM,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAC/C,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;QAEjC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC;;;AAG1C,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,UAAU,EAAC,CAAC,CAAC;AACvE,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,YAAY,EAAC,CAAC,CAAC;KAC1E;AAED,IAAA,kBAAkB,CAAC,GAAW,EAAA;AAC5B,QAAA,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAA,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;SACjB;AACD,QAAA,OAAO,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;KAC7B;AAED,IAAA,EAAE,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,QAAa,IAAI,EAAA;AACpD,QAAA,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAErC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;AAErC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;AAC5D,QAAA,IAAI,aAAa,CAAC,IAAI,IAAI,IAAI,IAAI,aAAa,CAAC,KAAK,IAAI,KAAK,EAAE;YAC9D,OAAO;SACR;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC;AACzD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,GAAGC,qBAAoB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;KAC3E;AAED,IAAA,YAAY,CAAC,IAAY,EAAE,QAAgB,EAAE,EAAE,QAAa,IAAI,EAAA;AAC9D,QAAA,IAAI,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAErC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAElD,QAAA,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAEtB,QAAA,IAAI,OAAO,CAAC,IAAI,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,IAAI,KAAK,EAAE;YAClD,OAAO;SACR;AAED,QAAA,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;AACpB,QAAA,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QAEtB,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC;QACzD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,GAAG,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,GAAGA,qBAAoB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;KAC3E;IAED,OAAO,GAAA;AACL,QAAA,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACjD,IAAI,CAAC,aAAa,EAAE,CAAC;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AAClB,gBAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;AACxB,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC,CAAC;SACJ;KACF;IAED,IAAI,GAAA;AACF,QAAA,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,aAAa,EAAE,CAAC;AACrB,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AAClB,gBAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;AACxB,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC,CAAC;SACJ;KACF;IAED,SAAS,CAAC,mBAA2B,CAAC,EAAA;AACpC,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC;AAC5D,QAAA,IAAI,aAAa,IAAI,CAAC,IAAI,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;AAC9D,YAAA,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACnC,YAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;AACjB,gBAAA,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE;AAClB,gBAAA,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE;AACxB,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,MAAM,EAAE,UAAU;AACnB,aAAA,CAAC,CAAC;SACJ;KACF;AAED,IAAA,WAAW,CAAC,EAAyC,EAAA;AACnD,QAAA,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAElC,IAAI,CAAC,sBAAsB,KAAK,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KAAI;YACnD,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AACjD,SAAC,CAAC,CAAC;AAEH,QAAA,OAAO,MAAK;YACV,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACrD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;YAE5C,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;AACzC,gBAAA,IAAI,CAAC,sBAAsB,EAAE,WAAW,EAAE,CAAC;AAC3C,gBAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;aACpC;AACH,SAAC,CAAC;KACH;;AAGD,IAAA,yBAAyB,CAAC,GAAA,GAAc,EAAE,EAAE,KAAc,EAAA;AACxD,QAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;KAC1D;AAED,IAAA,SAAS,CACP,MAA4B,EAC5B,OAAuC,EACvC,QAA8B,EAAA;QAE9B,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAC,CAAC,CAAC;KACpF;AAED,IAAA,SAAS,CAAC,GAAW,EAAA;AACnB,QAAA,OAAO,IAAK,CAAC;KACd;AAEO,IAAA,WAAW,CAAC,IAAY,EAAE,KAAa,EAAE,KAAU,EAAA;AACzD,QAAA,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;SAC9C;AACD,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;KAC/C;yHApLU,WAAW,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAX,WAAW,EAAA,CAAA,CAAA,EAAA;;sGAAX,WAAW,EAAA,UAAA,EAAA,CAAA;kBADvB,UAAU;;AAwLX,MAAM,aAAa,CAAA;AACjB,IAAA,WAAA,CACS,IAAY,EACZ,KAAa,EACb,KAAU,EAAA;QAFV,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;QACZ,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QACb,IAAK,CAAA,KAAA,GAAL,KAAK,CAAK;KACf;AACL;;ACxMD;;;;;AAKG;AAEG,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;AAQxD,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE,CAAC;QARV,IAAgB,CAAA,gBAAA,GAAW,GAAG,CAAC;QAC/B,IAAY,CAAA,YAAA,GAAW,GAAG,CAAC;QAC3B,IAAa,CAAA,aAAA,GAAW,EAAE,CAAC;QAC3B,IAAU,CAAA,UAAA,GAAa,EAAE,CAAC;;AAE1B,QAAA,IAAA,CAAA,QAAQ,GAAsB,IAAI,YAAY,EAAE,CAAC;QACzC,IAAY,CAAA,YAAA,GAAU,EAAE,CAAC;KAGhC;AAED,IAAA,gBAAgB,CAAC,GAAW,EAAA;AAC1B,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;AACxB,QAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;KACzD;IAEQ,IAAI,CAAC,cAAuB,KAAK,EAAA;QACxC,OAAO,IAAI,CAAC,YAAY,CAAC;KAC1B;AAEQ,IAAA,kBAAkB,CAAC,QAAgB,EAAA;AAC1C,QAAA,IAAI,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;YACnE,OAAO,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;SACtD;AACD,QAAA,OAAO,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;KACzC;AAEQ,IAAA,SAAS,CAAC,GAAQ,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAA;;AAErE,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAE5B,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC;AACzD,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QAExB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;AACjD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;KACnC;AAEQ,IAAA,YAAY,CAAC,GAAQ,EAAE,KAAa,EAAE,IAAY,EAAE,KAAa,EAAA;;AAExE,QAAA,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;AAE7D,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC;AACzD,QAAA,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC;QAExB,MAAM,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC;QACjD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,CAAC;KACjD;AAEQ,IAAA,UAAU,CAAC,EAAwB,EAAA;QAC1C,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAC,IAAI,EAAE,EAAE,EAAC,CAAC,CAAC;KACrC;IAEQ,WAAW,GAAA;QAClB,OAAO,IAAI,CAAC,gBAAgB,CAAC;KAC9B;IAEQ,IAAI,GAAA;QACX,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;AACxB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AAC9F,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;SAChC;KACF;IAEQ,OAAO,GAAA;AACd,QAAA,MAAM,iBAAiB,CAAC;KACzB;IAEQ,QAAQ,GAAA;AACf,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;KAC/D;yHA7EU,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAApB,oBAAoB,EAAA,CAAA,CAAA,EAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;;AAiFX,MAAM,kBAAkB,CAAA;AAGtB,IAAA,WAAA,CAAmB,MAAc,EAAA;QAAd,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QAFjC,IAAG,CAAA,GAAA,GAAY,IAAI,CAAC;QACpB,IAAI,CAAA,IAAA,GAAW,UAAU,CAAC;KACW;AACtC;;ACxFD;;;;;AAKG;SACa,oBAAoB,GAAA;IAClC,OAAO;AACL,QAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAC;AAC1C,QAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;KAC5D,CAAC;AACJ;;ACjBA;;;;AAIG;;ACJH;;;;AAIG;AAGH;;ACPA;;ACRA;;AAEG;;;;"}