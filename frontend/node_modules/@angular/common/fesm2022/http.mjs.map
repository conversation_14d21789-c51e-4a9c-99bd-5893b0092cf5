{"version": 3, "file": "http.mjs", "sources": ["../../../../../../packages/common/http/src/backend.ts", "../../../../../../packages/common/http/src/headers.ts", "../../../../../../packages/common/http/src/params.ts", "../../../../../../packages/common/http/src/context.ts", "../../../../../../packages/common/http/src/request.ts", "../../../../../../packages/common/http/src/response.ts", "../../../../../../packages/common/http/src/client.ts", "../../../../../../packages/common/http/src/fetch.ts", "../../../../../../packages/common/http/src/interceptor.ts", "../../../../../../packages/common/http/src/jsonp.ts", "../../../../../../packages/common/http/src/xhr.ts", "../../../../../../packages/common/http/src/xsrf.ts", "../../../../../../packages/common/http/src/provider.ts", "../../../../../../packages/common/http/src/module.ts", "../../../../../../packages/common/http/src/transfer_cache.ts", "../../../../../../packages/common/http/index.ts", "../../../../../../packages/common/http/http.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Observable} from 'rxjs';\n\nimport {HttpRequest} from './request';\nimport {HttpEvent} from './response';\n\n/**\n * Transforms an `HttpRequest` into a stream of `HttpEvent`s, one of which will likely be a\n * `HttpResponse`.\n *\n * `HttpHandler` is injectable. When injected, the handler instance dispatches requests to the\n * first interceptor in the chain, which dispatches to the second, etc, eventually reaching the\n * `HttpBackend`.\n *\n * In an `HttpInterceptor`, the `HttpHandler` parameter is the next interceptor in the chain.\n *\n * @publicApi\n */\nexport abstract class HttpHandler {\n  abstract handle(req: HttpRequest<any>): Observable<HttpEvent<any>>;\n}\n\n/**\n * A final `HttpHandler` which will dispatch the request via browser HTTP APIs to a backend.\n *\n * Interceptors sit between the `HttpClient` interface and the `HttpBackend`.\n *\n * When injected, `HttpBackend` dispatches requests directly to the backend, without going\n * through the interceptor chain.\n *\n * @publicApi\n */\nexport abstract class HttpBackend implements HttpHandler {\n  abstract handle(req: HttpRequest<any>): Observable<HttpEvent<any>>;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\ninterface Update {\n  name: string;\n  value?: string | string[];\n  op: 'a' | 's' | 'd';\n}\n\n/**\n * Represents the header configuration options for an HTTP request.\n * Instances are immutable. Modifying methods return a cloned\n * instance with the change. The original object is never changed.\n *\n * @publicApi\n */\nexport class HttpHeaders {\n  /**\n   * Internal map of lowercase header names to values.\n   */\n  // TODO(issue/24571): remove '!'.\n  private headers!: Map<string, string[]>;\n\n  /**\n   * Internal map of lowercased header names to the normalized\n   * form of the name (the form seen first).\n   */\n  private normalizedNames: Map<string, string> = new Map();\n\n  /**\n   * Complete the lazy initialization of this object (needed before reading).\n   */\n  private lazyInit!: HttpHeaders | Function | null;\n\n  /**\n   * Queued updates to be materialized the next initialization.\n   */\n  private lazyUpdate: Update[] | null = null;\n\n  /**  Constructs a new HTTP header object with the given values.*/\n\n  constructor(\n    headers?: string | {[name: string]: string | number | (string | number)[]} | Headers,\n  ) {\n    if (!headers) {\n      this.headers = new Map<string, string[]>();\n    } else if (typeof headers === 'string') {\n      this.lazyInit = () => {\n        this.headers = new Map<string, string[]>();\n        headers.split('\\n').forEach((line) => {\n          const index = line.indexOf(':');\n          if (index > 0) {\n            const name = line.slice(0, index);\n            const key = name.toLowerCase();\n            const value = line.slice(index + 1).trim();\n            this.maybeSetNormalizedName(name, key);\n            if (this.headers.has(key)) {\n              this.headers.get(key)!.push(value);\n            } else {\n              this.headers.set(key, [value]);\n            }\n          }\n        });\n      };\n    } else if (typeof Headers !== 'undefined' && headers instanceof Headers) {\n      this.headers = new Map<string, string[]>();\n      headers.forEach((values: string, name: string) => {\n        this.setHeaderEntries(name, values);\n      });\n    } else {\n      this.lazyInit = () => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          assertValidHeaders(headers);\n        }\n        this.headers = new Map<string, string[]>();\n        Object.entries(headers).forEach(([name, values]) => {\n          this.setHeaderEntries(name, values);\n        });\n      };\n    }\n  }\n\n  /**\n   * Checks for existence of a given header.\n   *\n   * @param name The header name to check for existence.\n   *\n   * @returns True if the header exists, false otherwise.\n   */\n  has(name: string): boolean {\n    this.init();\n\n    return this.headers.has(name.toLowerCase());\n  }\n\n  /**\n   * Retrieves the first value of a given header.\n   *\n   * @param name The header name.\n   *\n   * @returns The value string if the header exists, null otherwise\n   */\n  get(name: string): string | null {\n    this.init();\n\n    const values = this.headers.get(name.toLowerCase());\n    return values && values.length > 0 ? values[0] : null;\n  }\n\n  /**\n   * Retrieves the names of the headers.\n   *\n   * @returns A list of header names.\n   */\n  keys(): string[] {\n    this.init();\n\n    return Array.from(this.normalizedNames.values());\n  }\n\n  /**\n   * Retrieves a list of values for a given header.\n   *\n   * @param name The header name from which to retrieve values.\n   *\n   * @returns A string of values if the header exists, null otherwise.\n   */\n  getAll(name: string): string[] | null {\n    this.init();\n\n    return this.headers.get(name.toLowerCase()) || null;\n  }\n\n  /**\n   * Appends a new value to the existing set of values for a header\n   * and returns them in a clone of the original instance.\n   *\n   * @param name The header name for which to append the values.\n   * @param value The value to append.\n   *\n   * @returns A clone of the HTTP headers object with the value appended to the given header.\n   */\n\n  append(name: string, value: string | string[]): HttpHeaders {\n    return this.clone({name, value, op: 'a'});\n  }\n  /**\n   * Sets or modifies a value for a given header in a clone of the original instance.\n   * If the header already exists, its value is replaced with the given value\n   * in the returned object.\n   *\n   * @param name The header name.\n   * @param value The value or values to set or override for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the newly set header value.\n   */\n  set(name: string, value: string | string[]): HttpHeaders {\n    return this.clone({name, value, op: 's'});\n  }\n  /**\n   * Deletes values for a given header in a clone of the original instance.\n   *\n   * @param name The header name.\n   * @param value The value or values to delete for the given header.\n   *\n   * @returns A clone of the HTTP headers object with the given value deleted.\n   */\n  delete(name: string, value?: string | string[]): HttpHeaders {\n    return this.clone({name, value, op: 'd'});\n  }\n\n  private maybeSetNormalizedName(name: string, lcName: string): void {\n    if (!this.normalizedNames.has(lcName)) {\n      this.normalizedNames.set(lcName, name);\n    }\n  }\n\n  private init(): void {\n    if (!!this.lazyInit) {\n      if (this.lazyInit instanceof HttpHeaders) {\n        this.copyFrom(this.lazyInit);\n      } else {\n        this.lazyInit();\n      }\n      this.lazyInit = null;\n      if (!!this.lazyUpdate) {\n        this.lazyUpdate.forEach((update) => this.applyUpdate(update));\n        this.lazyUpdate = null;\n      }\n    }\n  }\n\n  private copyFrom(other: HttpHeaders) {\n    other.init();\n    Array.from(other.headers.keys()).forEach((key) => {\n      this.headers.set(key, other.headers.get(key)!);\n      this.normalizedNames.set(key, other.normalizedNames.get(key)!);\n    });\n  }\n\n  private clone(update: Update): HttpHeaders {\n    const clone = new HttpHeaders();\n    clone.lazyInit = !!this.lazyInit && this.lazyInit instanceof HttpHeaders ? this.lazyInit : this;\n    clone.lazyUpdate = (this.lazyUpdate || []).concat([update]);\n    return clone;\n  }\n\n  private applyUpdate(update: Update): void {\n    const key = update.name.toLowerCase();\n    switch (update.op) {\n      case 'a':\n      case 's':\n        let value = update.value!;\n        if (typeof value === 'string') {\n          value = [value];\n        }\n        if (value.length === 0) {\n          return;\n        }\n        this.maybeSetNormalizedName(update.name, key);\n        const base = (update.op === 'a' ? this.headers.get(key) : undefined) || [];\n        base.push(...value);\n        this.headers.set(key, base);\n        break;\n      case 'd':\n        const toDelete = update.value as string | undefined;\n        if (!toDelete) {\n          this.headers.delete(key);\n          this.normalizedNames.delete(key);\n        } else {\n          let existing = this.headers.get(key);\n          if (!existing) {\n            return;\n          }\n          existing = existing.filter((value) => toDelete.indexOf(value) === -1);\n          if (existing.length === 0) {\n            this.headers.delete(key);\n            this.normalizedNames.delete(key);\n          } else {\n            this.headers.set(key, existing);\n          }\n        }\n        break;\n    }\n  }\n\n  private setHeaderEntries(name: string, values: any) {\n    const headerValues = (Array.isArray(values) ? values : [values]).map((value) =>\n      value.toString(),\n    );\n    const key = name.toLowerCase();\n    this.headers.set(key, headerValues);\n    this.maybeSetNormalizedName(name, key);\n  }\n\n  /**\n   * @internal\n   */\n  forEach(fn: (name: string, values: string[]) => void) {\n    this.init();\n    Array.from(this.normalizedNames.keys()).forEach((key) =>\n      fn(this.normalizedNames.get(key)!, this.headers.get(key)!),\n    );\n  }\n}\n\n/**\n * Verifies that the headers object has the right shape: the values\n * must be either strings, numbers or arrays. Throws an error if an invalid\n * header value is present.\n */\nfunction assertValidHeaders(\n  headers: Record<string, unknown> | Headers,\n): asserts headers is Record<string, string | string[] | number | number[]> {\n  for (const [key, value] of Object.entries(headers)) {\n    if (!(typeof value === 'string' || typeof value === 'number') && !Array.isArray(value)) {\n      throw new Error(\n        `Unexpected value of the \\`${key}\\` header provided. ` +\n          `Expecting either a string, a number or an array, but got: \\`${value}\\`.`,\n      );\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * A codec for encoding and decoding parameters in URLs.\n *\n * Used by `HttpParams`.\n *\n * @publicApi\n **/\nexport interface HttpParameterCodec {\n  encodeKey(key: string): string;\n  encodeValue(value: string): string;\n\n  decodeKey(key: string): string;\n  decodeValue(value: string): string;\n}\n\n/**\n * Provides encoding and decoding of URL parameter and query-string values.\n *\n * Serializes and parses URL parameter keys and values to encode and decode them.\n * If you pass URL query parameters without encoding,\n * the query parameters can be misinterpreted at the receiving end.\n *\n *\n * @publicApi\n */\nexport class HttpUrlEncodingCodec implements HttpParameterCodec {\n  /**\n   * Encodes a key name for a URL parameter or query-string.\n   * @param key The key name.\n   * @returns The encoded key name.\n   */\n  encodeKey(key: string): string {\n    return standardEncoding(key);\n  }\n\n  /**\n   * Encodes the value of a URL parameter or query-string.\n   * @param value The value.\n   * @returns The encoded value.\n   */\n  encodeValue(value: string): string {\n    return standardEncoding(value);\n  }\n\n  /**\n   * Decodes an encoded URL parameter or query-string key.\n   * @param key The encoded key name.\n   * @returns The decoded key name.\n   */\n  decodeKey(key: string): string {\n    return decodeURIComponent(key);\n  }\n\n  /**\n   * Decodes an encoded URL parameter or query-string value.\n   * @param value The encoded value.\n   * @returns The decoded value.\n   */\n  decodeValue(value: string) {\n    return decodeURIComponent(value);\n  }\n}\n\nfunction paramParser(rawParams: string, codec: HttpParameterCodec): Map<string, string[]> {\n  const map = new Map<string, string[]>();\n  if (rawParams.length > 0) {\n    // The `window.location.search` can be used while creating an instance of the `HttpParams` class\n    // (e.g. `new HttpParams({ fromString: window.location.search })`). The `window.location.search`\n    // may start with the `?` char, so we strip it if it's present.\n    const params: string[] = rawParams.replace(/^\\?/, '').split('&');\n    params.forEach((param: string) => {\n      const eqIdx = param.indexOf('=');\n      const [key, val]: string[] =\n        eqIdx == -1\n          ? [codec.decodeKey(param), '']\n          : [codec.decodeKey(param.slice(0, eqIdx)), codec.decodeValue(param.slice(eqIdx + 1))];\n      const list = map.get(key) || [];\n      list.push(val);\n      map.set(key, list);\n    });\n  }\n  return map;\n}\n\n/**\n * Encode input string with standard encodeURIComponent and then un-encode specific characters.\n */\nconst STANDARD_ENCODING_REGEX = /%(\\d[a-f0-9])/gi;\nconst STANDARD_ENCODING_REPLACEMENTS: {[x: string]: string} = {\n  '40': '@',\n  '3A': ':',\n  '24': '$',\n  '2C': ',',\n  '3B': ';',\n  '3D': '=',\n  '3F': '?',\n  '2F': '/',\n};\n\nfunction standardEncoding(v: string): string {\n  return encodeURIComponent(v).replace(\n    STANDARD_ENCODING_REGEX,\n    (s, t) => STANDARD_ENCODING_REPLACEMENTS[t] ?? s,\n  );\n}\n\nfunction valueToString(value: string | number | boolean): string {\n  return `${value}`;\n}\n\ninterface Update {\n  param: string;\n  value?: string | number | boolean;\n  op: 'a' | 'd' | 's';\n}\n\n/**\n * Options used to construct an `HttpParams` instance.\n *\n * @publicApi\n */\nexport interface HttpParamsOptions {\n  /**\n   * String representation of the HTTP parameters in URL-query-string format.\n   * Mutually exclusive with `fromObject`.\n   */\n  fromString?: string;\n\n  /** Object map of the HTTP parameters. Mutually exclusive with `fromString`. */\n  fromObject?: {\n    [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>;\n  };\n\n  /** Encoding codec used to parse and serialize the parameters. */\n  encoder?: HttpParameterCodec;\n}\n\n/**\n * An HTTP request/response body that represents serialized parameters,\n * per the MIME type `application/x-www-form-urlencoded`.\n *\n * This class is immutable; all mutation operations return a new instance.\n *\n * @publicApi\n */\nexport class HttpParams {\n  private map: Map<string, string[]> | null;\n  private encoder: HttpParameterCodec;\n  private updates: Update[] | null = null;\n  private cloneFrom: HttpParams | null = null;\n\n  constructor(options: HttpParamsOptions = {} as HttpParamsOptions) {\n    this.encoder = options.encoder || new HttpUrlEncodingCodec();\n    if (!!options.fromString) {\n      if (!!options.fromObject) {\n        throw new Error(`Cannot specify both fromString and fromObject.`);\n      }\n      this.map = paramParser(options.fromString, this.encoder);\n    } else if (!!options.fromObject) {\n      this.map = new Map<string, string[]>();\n      Object.keys(options.fromObject).forEach((key) => {\n        const value = (options.fromObject as any)[key];\n        // convert the values to strings\n        const values = Array.isArray(value) ? value.map(valueToString) : [valueToString(value)];\n        this.map!.set(key, values);\n      });\n    } else {\n      this.map = null;\n    }\n  }\n\n  /**\n   * Reports whether the body includes one or more values for a given parameter.\n   * @param param The parameter name.\n   * @returns True if the parameter has one or more values,\n   * false if it has no value or is not present.\n   */\n  has(param: string): boolean {\n    this.init();\n    return this.map!.has(param);\n  }\n\n  /**\n   * Retrieves the first value for a parameter.\n   * @param param The parameter name.\n   * @returns The first value of the given parameter,\n   * or `null` if the parameter is not present.\n   */\n  get(param: string): string | null {\n    this.init();\n    const res = this.map!.get(param);\n    return !!res ? res[0] : null;\n  }\n\n  /**\n   * Retrieves all values for a  parameter.\n   * @param param The parameter name.\n   * @returns All values in a string array,\n   * or `null` if the parameter not present.\n   */\n  getAll(param: string): string[] | null {\n    this.init();\n    return this.map!.get(param) || null;\n  }\n\n  /**\n   * Retrieves all the parameters for this body.\n   * @returns The parameter names in a string array.\n   */\n  keys(): string[] {\n    this.init();\n    return Array.from(this.map!.keys());\n  }\n\n  /**\n   * Appends a new value to existing values for a parameter.\n   * @param param The parameter name.\n   * @param value The new value to add.\n   * @return A new body with the appended value.\n   */\n  append(param: string, value: string | number | boolean): HttpParams {\n    return this.clone({param, value, op: 'a'});\n  }\n\n  /**\n   * Constructs a new body with appended values for the given parameter name.\n   * @param params parameters and values\n   * @return A new body with the new value.\n   */\n  appendAll(params: {\n    [param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>;\n  }): HttpParams {\n    const updates: Update[] = [];\n    Object.keys(params).forEach((param) => {\n      const value = params[param];\n      if (Array.isArray(value)) {\n        value.forEach((_value) => {\n          updates.push({param, value: _value, op: 'a'});\n        });\n      } else {\n        updates.push({param, value: value as string | number | boolean, op: 'a'});\n      }\n    });\n    return this.clone(updates);\n  }\n\n  /**\n   * Replaces the value for a parameter.\n   * @param param The parameter name.\n   * @param value The new value.\n   * @return A new body with the new value.\n   */\n  set(param: string, value: string | number | boolean): HttpParams {\n    return this.clone({param, value, op: 's'});\n  }\n\n  /**\n   * Removes a given value or all values from a parameter.\n   * @param param The parameter name.\n   * @param value The value to remove, if provided.\n   * @return A new body with the given value removed, or with all values\n   * removed if no value is specified.\n   */\n  delete(param: string, value?: string | number | boolean): HttpParams {\n    return this.clone({param, value, op: 'd'});\n  }\n\n  /**\n   * Serializes the body to an encoded string, where key-value pairs (separated by `=`) are\n   * separated by `&`s.\n   */\n  toString(): string {\n    this.init();\n    return (\n      this.keys()\n        .map((key) => {\n          const eKey = this.encoder.encodeKey(key);\n          // `a: ['1']` produces `'a=1'`\n          // `b: []` produces `''`\n          // `c: ['1', '2']` produces `'c=1&c=2'`\n          return this.map!.get(key)!\n            .map((value) => eKey + '=' + this.encoder.encodeValue(value))\n            .join('&');\n        })\n        // filter out empty values because `b: []` produces `''`\n        // which results in `a=1&&c=1&c=2` instead of `a=1&c=1&c=2` if we don't\n        .filter((param) => param !== '')\n        .join('&')\n    );\n  }\n\n  private clone(update: Update | Update[]): HttpParams {\n    const clone = new HttpParams({encoder: this.encoder} as HttpParamsOptions);\n    clone.cloneFrom = this.cloneFrom || this;\n    clone.updates = (this.updates || []).concat(update);\n    return clone;\n  }\n\n  private init() {\n    if (this.map === null) {\n      this.map = new Map<string, string[]>();\n    }\n    if (this.cloneFrom !== null) {\n      this.cloneFrom.init();\n      this.cloneFrom.keys().forEach((key) => this.map!.set(key, this.cloneFrom!.map!.get(key)!));\n      this.updates!.forEach((update) => {\n        switch (update.op) {\n          case 'a':\n          case 's':\n            const base = (update.op === 'a' ? this.map!.get(update.param) : undefined) || [];\n            base.push(valueToString(update.value!));\n            this.map!.set(update.param, base);\n            break;\n          case 'd':\n            if (update.value !== undefined) {\n              let base = this.map!.get(update.param) || [];\n              const idx = base.indexOf(valueToString(update.value));\n              if (idx !== -1) {\n                base.splice(idx, 1);\n              }\n              if (base.length > 0) {\n                this.map!.set(update.param, base);\n              } else {\n                this.map!.delete(update.param);\n              }\n            } else {\n              this.map!.delete(update.param);\n              break;\n            }\n        }\n      });\n      this.cloneFrom = this.updates = null;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * A token used to manipulate and access values stored in `HttpContext`.\n *\n * @publicApi\n */\nexport class HttpContextToken<T> {\n  constructor(public readonly defaultValue: () => T) {}\n}\n\n/**\n * Http context stores arbitrary user defined values and ensures type safety without\n * actually knowing the types. It is backed by a `Map` and guarantees that keys do not clash.\n *\n * This context is mutable and is shared between cloned requests unless explicitly specified.\n *\n * @usageNotes\n *\n * ### Usage Example\n *\n * ```typescript\n * // inside cache.interceptors.ts\n * export const IS_CACHE_ENABLED = new HttpContextToken<boolean>(() => false);\n *\n * export class CacheInterceptor implements HttpInterceptor {\n *\n *   intercept(req: HttpRequest<any>, delegate: HttpHandler): Observable<HttpEvent<any>> {\n *     if (req.context.get(IS_CACHE_ENABLED) === true) {\n *       return ...;\n *     }\n *     return delegate.handle(req);\n *   }\n * }\n *\n * // inside a service\n *\n * this.httpClient.get('/api/weather', {\n *   context: new HttpContext().set(IS_CACHE_ENABLED, true)\n * }).subscribe(...);\n * ```\n *\n * @publicApi\n */\nexport class HttpContext {\n  private readonly map = new Map<HttpContextToken<unknown>, unknown>();\n\n  /**\n   * Store a value in the context. If a value is already present it will be overwritten.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   * @param value The value to store.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  set<T>(token: HttpContextToken<T>, value: T): HttpContext {\n    this.map.set(token, value);\n    return this;\n  }\n\n  /**\n   * Retrieve the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns The stored value or default if one is defined.\n   */\n  get<T>(token: HttpContextToken<T>): T {\n    if (!this.map.has(token)) {\n      this.map.set(token, token.defaultValue());\n    }\n    return this.map.get(token) as T;\n  }\n\n  /**\n   * Delete the value associated with the given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns A reference to itself for easy chaining.\n   */\n  delete(token: HttpContextToken<unknown>): HttpContext {\n    this.map.delete(token);\n    return this;\n  }\n\n  /**\n   * Checks for existence of a given token.\n   *\n   * @param token The reference to an instance of `HttpContextToken`.\n   *\n   * @returns True if the token exists, false otherwise.\n   */\n  has(token: HttpContextToken<unknown>): boolean {\n    return this.map.has(token);\n  }\n\n  /**\n   * @returns a list of tokens currently stored in the context.\n   */\n  keys(): IterableIterator<HttpContextToken<unknown>> {\n    return this.map.keys();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HttpContext} from './context';\nimport {HttpHeaders} from './headers';\nimport {HttpParams} from './params';\n\n/**\n * Construction interface for `HttpRequest`s.\n *\n * All values are optional and will override default values if provided.\n */\ninterface HttpRequestInit {\n  headers?: HttpHeaders;\n  context?: HttpContext;\n  reportProgress?: boolean;\n  params?: HttpParams;\n  responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n  withCredentials?: boolean;\n  transferCache?: {includeHeaders?: string[]} | boolean;\n}\n\n/**\n * Determine whether the given HTTP method may include a body.\n */\nfunction mightHaveBody(method: string): boolean {\n  switch (method) {\n    case 'DELETE':\n    case 'GET':\n    case 'HEAD':\n    case 'OPTIONS':\n    case 'JSONP':\n      return false;\n    default:\n      return true;\n  }\n}\n\n/**\n * Safely assert whether the given value is an ArrayBuffer.\n *\n * In some execution environments ArrayBuffer is not defined.\n */\nfunction isArrayBuffer(value: any): value is ArrayBuffer {\n  return typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer;\n}\n\n/**\n * Safely assert whether the given value is a Blob.\n *\n * In some execution environments Blob is not defined.\n */\nfunction isBlob(value: any): value is Blob {\n  return typeof Blob !== 'undefined' && value instanceof Blob;\n}\n\n/**\n * Safely assert whether the given value is a FormData instance.\n *\n * In some execution environments FormData is not defined.\n */\nfunction isFormData(value: any): value is FormData {\n  return typeof FormData !== 'undefined' && value instanceof FormData;\n}\n\n/**\n * Safely assert whether the given value is a URLSearchParams instance.\n *\n * In some execution environments URLSearchParams is not defined.\n */\nfunction isUrlSearchParams(value: any): value is URLSearchParams {\n  return typeof URLSearchParams !== 'undefined' && value instanceof URLSearchParams;\n}\n\n/**\n * An outgoing HTTP request with an optional typed body.\n *\n * `HttpRequest` represents an outgoing request, including URL, method,\n * headers, body, and other request configuration options. Instances should be\n * assumed to be immutable. To modify a `HttpRequest`, the `clone`\n * method should be used.\n *\n * @publicApi\n */\nexport class HttpRequest<T> {\n  /**\n   * The request body, or `null` if one isn't set.\n   *\n   * Bodies are not enforced to be immutable, as they can include a reference to any\n   * user-defined data type. However, interceptors should take care to preserve\n   * idempotence by treating them as such.\n   */\n  readonly body: T | null = null;\n\n  /**\n   * Outgoing headers for this request.\n   */\n  // TODO(issue/24571): remove '!'.\n  readonly headers!: HttpHeaders;\n\n  /**\n   * Shared and mutable context that can be used by interceptors\n   */\n  readonly context!: HttpContext;\n\n  /**\n   * Whether this request should be made in a way that exposes progress events.\n   *\n   * Progress events are expensive (change detection runs on each event) and so\n   * they should only be requested if the consumer intends to monitor them.\n   *\n   * Note: The `FetchBackend` doesn't support progress report on uploads.\n   */\n  readonly reportProgress: boolean = false;\n\n  /**\n   * Whether this request should be sent with outgoing credentials (cookies).\n   */\n  readonly withCredentials: boolean = false;\n\n  /**\n   * The expected response type of the server.\n   *\n   * This is used to parse the response appropriately before returning it to\n   * the requestee.\n   */\n  readonly responseType: 'arraybuffer' | 'blob' | 'json' | 'text' = 'json';\n\n  /**\n   * The outgoing HTTP request method.\n   */\n  readonly method: string;\n\n  /**\n   * Outgoing URL parameters.\n   *\n   * To pass a string representation of HTTP parameters in the URL-query-string format,\n   * the `HttpParamsOptions`' `fromString` may be used. For example:\n   *\n   * ```\n   * new HttpParams({fromString: 'angular=awesome'})\n   * ```\n   */\n  // TODO(issue/24571): remove '!'.\n  readonly params!: HttpParams;\n\n  /**\n   * The outgoing URL with all URL parameters set.\n   */\n  readonly urlWithParams: string;\n\n  /**\n   * The HttpTransferCache option for the request\n   */\n  readonly transferCache?: {includeHeaders?: string[]} | boolean;\n\n  constructor(\n    method: 'GET' | 'HEAD',\n    url: string,\n    init?: {\n      headers?: HttpHeaders;\n      context?: HttpContext;\n      reportProgress?: boolean;\n      params?: HttpParams;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      /**\n       * This property accepts either a boolean to enable/disable transferring cache for eligible\n       * requests performed using `HttpClient`, or an object, which allows to configure cache\n       * parameters, such as which headers should be included (no headers are included by default).\n       *\n       * Setting this property will override the options passed to `provideClientHydration()` for this\n       * particular request\n       */\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  );\n  constructor(\n    method: 'DELETE' | 'JSONP' | 'OPTIONS',\n    url: string,\n    init?: {\n      headers?: HttpHeaders;\n      context?: HttpContext;\n      reportProgress?: boolean;\n      params?: HttpParams;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n    },\n  );\n  constructor(\n    method: 'POST',\n    url: string,\n    body: T | null,\n    init?: {\n      headers?: HttpHeaders;\n      context?: HttpContext;\n      reportProgress?: boolean;\n      params?: HttpParams;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      /**\n       * This property accepts either a boolean to enable/disable transferring cache for eligible\n       * requests performed using `HttpClient`, or an object, which allows to configure cache\n       * parameters, such as which headers should be included (no headers are included by default).\n       *\n       * Setting this property will override the options passed to `provideClientHydration()` for this\n       * particular request\n       */\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  );\n  constructor(\n    method: 'PUT' | 'PATCH',\n    url: string,\n    body: T | null,\n    init?: {\n      headers?: HttpHeaders;\n      context?: HttpContext;\n      reportProgress?: boolean;\n      params?: HttpParams;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n    },\n  );\n  constructor(\n    method: string,\n    url: string,\n    body: T | null,\n    init?: {\n      headers?: HttpHeaders;\n      context?: HttpContext;\n      reportProgress?: boolean;\n      params?: HttpParams;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      /**\n       * This property accepts either a boolean to enable/disable transferring cache for eligible\n       * requests performed using `HttpClient`, or an object, which allows to configure cache\n       * parameters, such as which headers should be included (no headers are included by default).\n       *\n       * Setting this property will override the options passed to `provideClientHydration()` for this\n       * particular request\n       */\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  );\n  constructor(\n    method: string,\n    readonly url: string,\n    third?:\n      | T\n      | {\n          headers?: HttpHeaders;\n          context?: HttpContext;\n          reportProgress?: boolean;\n          params?: HttpParams;\n          responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n          withCredentials?: boolean;\n          transferCache?: {includeHeaders?: string[]} | boolean;\n        }\n      | null,\n    fourth?: {\n      headers?: HttpHeaders;\n      context?: HttpContext;\n      reportProgress?: boolean;\n      params?: HttpParams;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ) {\n    this.method = method.toUpperCase();\n    // Next, need to figure out which argument holds the HttpRequestInit\n    // options, if any.\n    let options: HttpRequestInit | undefined;\n\n    // Check whether a body argument is expected. The only valid way to omit\n    // the body argument is to use a known no-body method like GET.\n    if (mightHaveBody(this.method) || !!fourth) {\n      // Body is the third argument, options are the fourth.\n      this.body = third !== undefined ? (third as T) : null;\n      options = fourth;\n    } else {\n      // No body required, options are the third argument. The body stays null.\n      options = third as HttpRequestInit;\n    }\n\n    // If options have been passed, interpret them.\n    if (options) {\n      // Normalize reportProgress and withCredentials.\n      this.reportProgress = !!options.reportProgress;\n      this.withCredentials = !!options.withCredentials;\n\n      // Override default response type of 'json' if one is provided.\n      if (!!options.responseType) {\n        this.responseType = options.responseType;\n      }\n\n      // Override headers if they're provided.\n      if (!!options.headers) {\n        this.headers = options.headers;\n      }\n\n      if (!!options.context) {\n        this.context = options.context;\n      }\n\n      if (!!options.params) {\n        this.params = options.params;\n      }\n\n      // We do want to assign transferCache even if it's falsy (false is valid value)\n      this.transferCache = options.transferCache;\n    }\n\n    // If no headers have been passed in, construct a new HttpHeaders instance.\n    this.headers ??= new HttpHeaders();\n\n    // If no context have been passed in, construct a new HttpContext instance.\n    this.context ??= new HttpContext();\n\n    // If no parameters have been passed in, construct a new HttpUrlEncodedParams instance.\n    if (!this.params) {\n      this.params = new HttpParams();\n      this.urlWithParams = url;\n    } else {\n      // Encode the parameters to a string in preparation for inclusion in the URL.\n      const params = this.params.toString();\n      if (params.length === 0) {\n        // No parameters, the visible URL is just the URL given at creation time.\n        this.urlWithParams = url;\n      } else {\n        // Does the URL already have query parameters? Look for '?'.\n        const qIdx = url.indexOf('?');\n        // There are 3 cases to handle:\n        // 1) No existing parameters -> append '?' followed by params.\n        // 2) '?' exists and is followed by existing query string ->\n        //    append '&' followed by params.\n        // 3) '?' exists at the end of the url -> append params directly.\n        // This basically amounts to determining the character, if any, with\n        // which to join the URL and parameters.\n        const sep: string = qIdx === -1 ? '?' : qIdx < url.length - 1 ? '&' : '';\n        this.urlWithParams = url + sep + params;\n      }\n    }\n  }\n\n  /**\n   * Transform the free-form body into a serialized format suitable for\n   * transmission to the server.\n   */\n  serializeBody(): ArrayBuffer | Blob | FormData | URLSearchParams | string | null {\n    // If no body is present, no need to serialize it.\n    if (this.body === null) {\n      return null;\n    }\n    // Check whether the body is already in a serialized form. If so,\n    // it can just be returned directly.\n    if (\n      typeof this.body === 'string' ||\n      isArrayBuffer(this.body) ||\n      isBlob(this.body) ||\n      isFormData(this.body) ||\n      isUrlSearchParams(this.body)\n    ) {\n      return this.body;\n    }\n    // Check whether the body is an instance of HttpUrlEncodedParams.\n    if (this.body instanceof HttpParams) {\n      return this.body.toString();\n    }\n    // Check whether the body is an object or array, and serialize with JSON if so.\n    if (\n      typeof this.body === 'object' ||\n      typeof this.body === 'boolean' ||\n      Array.isArray(this.body)\n    ) {\n      return JSON.stringify(this.body);\n    }\n    // Fall back on toString() for everything else.\n    return (this.body as any).toString();\n  }\n\n  /**\n   * Examine the body and attempt to infer an appropriate MIME type\n   * for it.\n   *\n   * If no such type can be inferred, this method will return `null`.\n   */\n  detectContentTypeHeader(): string | null {\n    // An empty body has no content type.\n    if (this.body === null) {\n      return null;\n    }\n    // FormData bodies rely on the browser's content type assignment.\n    if (isFormData(this.body)) {\n      return null;\n    }\n    // Blobs usually have their own content type. If it doesn't, then\n    // no type can be inferred.\n    if (isBlob(this.body)) {\n      return this.body.type || null;\n    }\n    // Array buffers have unknown contents and thus no type can be inferred.\n    if (isArrayBuffer(this.body)) {\n      return null;\n    }\n    // Technically, strings could be a form of JSON data, but it's safe enough\n    // to assume they're plain strings.\n    if (typeof this.body === 'string') {\n      return 'text/plain';\n    }\n    // `HttpUrlEncodedParams` has its own content-type.\n    if (this.body instanceof HttpParams) {\n      return 'application/x-www-form-urlencoded;charset=UTF-8';\n    }\n    // Arrays, objects, boolean and numbers will be encoded as JSON.\n    if (\n      typeof this.body === 'object' ||\n      typeof this.body === 'number' ||\n      typeof this.body === 'boolean'\n    ) {\n      return 'application/json';\n    }\n    // No type could be inferred.\n    return null;\n  }\n\n  clone(): HttpRequest<T>;\n  clone(update: {\n    headers?: HttpHeaders;\n    context?: HttpContext;\n    reportProgress?: boolean;\n    params?: HttpParams;\n    responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n    withCredentials?: boolean;\n    transferCache?: {includeHeaders?: string[]} | boolean;\n    body?: T | null;\n    method?: string;\n    url?: string;\n    setHeaders?: {[name: string]: string | string[]};\n    setParams?: {[param: string]: string};\n  }): HttpRequest<T>;\n  clone<V>(update: {\n    headers?: HttpHeaders;\n    context?: HttpContext;\n    reportProgress?: boolean;\n    params?: HttpParams;\n    responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n    withCredentials?: boolean;\n    transferCache?: {includeHeaders?: string[]} | boolean;\n    body?: V | null;\n    method?: string;\n    url?: string;\n    setHeaders?: {[name: string]: string | string[]};\n    setParams?: {[param: string]: string};\n  }): HttpRequest<V>;\n  clone(\n    update: {\n      headers?: HttpHeaders;\n      context?: HttpContext;\n      reportProgress?: boolean;\n      params?: HttpParams;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n      body?: any | null;\n      method?: string;\n      url?: string;\n      setHeaders?: {[name: string]: string | string[]};\n      setParams?: {[param: string]: string};\n    } = {},\n  ): HttpRequest<any> {\n    // For method, url, and responseType, take the current value unless\n    // it is overridden in the update hash.\n    const method = update.method || this.method;\n    const url = update.url || this.url;\n    const responseType = update.responseType || this.responseType;\n\n    // Carefully handle the transferCache to differentiate between\n    // `false` and `undefined` in the update args.\n    const transferCache = update.transferCache ?? this.transferCache;\n\n    // The body is somewhat special - a `null` value in update.body means\n    // whatever current body is present is being overridden with an empty\n    // body, whereas an `undefined` value in update.body implies no\n    // override.\n    const body = update.body !== undefined ? update.body : this.body;\n\n    // Carefully handle the boolean options to differentiate between\n    // `false` and `undefined` in the update args.\n    const withCredentials = update.withCredentials ?? this.withCredentials;\n    const reportProgress = update.reportProgress ?? this.reportProgress;\n\n    // Headers and params may be appended to if `setHeaders` or\n    // `setParams` are used.\n    let headers = update.headers || this.headers;\n    let params = update.params || this.params;\n\n    // Pass on context if needed\n    const context = update.context ?? this.context;\n\n    // Check whether the caller has asked to add headers.\n    if (update.setHeaders !== undefined) {\n      // Set every requested header.\n      headers = Object.keys(update.setHeaders).reduce(\n        (headers, name) => headers.set(name, update.setHeaders![name]),\n        headers,\n      );\n    }\n\n    // Check whether the caller has asked to set params.\n    if (update.setParams) {\n      // Set every requested param.\n      params = Object.keys(update.setParams).reduce(\n        (params, param) => params.set(param, update.setParams![param]),\n        params,\n      );\n    }\n\n    // Finally, construct the new HttpRequest using the pieces from above.\n    return new HttpRequest(method, url, body, {\n      params,\n      headers,\n      context,\n      reportProgress,\n      responseType,\n      withCredentials,\n      transferCache,\n    });\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HttpHeaders} from './headers';\n\n/**\n * Type enumeration for the different kinds of `HttpEvent`.\n *\n * @publicApi\n */\nexport enum HttpEventType {\n  /**\n   * The request was sent out over the wire.\n   */\n  Sent,\n\n  /**\n   * An upload progress event was received.\n   *\n   * Note: The `FetchBackend` doesn't support progress report on uploads.\n   */\n  UploadProgress,\n\n  /**\n   * The response status code and headers were received.\n   */\n  ResponseHeader,\n\n  /**\n   * A download progress event was received.\n   */\n  DownloadProgress,\n\n  /**\n   * The full response including the body was received.\n   */\n  Response,\n\n  /**\n   * A custom event from an interceptor or a backend.\n   */\n  User,\n}\n\n/**\n * Base interface for progress events.\n *\n * @publicApi\n */\nexport interface HttpProgressEvent {\n  /**\n   * Progress event type is either upload or download.\n   */\n  type: HttpEventType.DownloadProgress | HttpEventType.UploadProgress;\n\n  /**\n   * Number of bytes uploaded or downloaded.\n   */\n  loaded: number;\n\n  /**\n   * Total number of bytes to upload or download. Depending on the request or\n   * response, this may not be computable and thus may not be present.\n   */\n  total?: number;\n}\n\n/**\n * A download progress event.\n *\n * @publicApi\n */\nexport interface HttpDownloadProgressEvent extends HttpProgressEvent {\n  type: HttpEventType.DownloadProgress;\n\n  /**\n   * The partial response body as downloaded so far.\n   *\n   * Only present if the responseType was `text`.\n   */\n  partialText?: string;\n}\n\n/**\n * An upload progress event.\n *\n * Note: The `FetchBackend` doesn't support progress report on uploads.\n *\n * @publicApi\n */\nexport interface HttpUploadProgressEvent extends HttpProgressEvent {\n  type: HttpEventType.UploadProgress;\n}\n\n/**\n * An event indicating that the request was sent to the server. Useful\n * when a request may be retried multiple times, to distinguish between\n * retries on the final event stream.\n *\n * @publicApi\n */\nexport interface HttpSentEvent {\n  type: HttpEventType.Sent;\n}\n\n/**\n * A user-defined event.\n *\n * Grouping all custom events under this type ensures they will be handled\n * and forwarded by all implementations of interceptors.\n *\n * @publicApi\n */\nexport interface HttpUserEvent<T> {\n  type: HttpEventType.User;\n}\n\n/**\n * An error that represents a failed attempt to JSON.parse text coming back\n * from the server.\n *\n * It bundles the Error object with the actual response body that failed to parse.\n *\n *\n */\nexport interface HttpJsonParseError {\n  error: Error;\n  text: string;\n}\n\n/**\n * Union type for all possible events on the response stream.\n *\n * Typed according to the expected type of the response.\n *\n * @publicApi\n */\nexport type HttpEvent<T> =\n  | HttpSentEvent\n  | HttpHeaderResponse\n  | HttpResponse<T>\n  | HttpProgressEvent\n  | HttpUserEvent<T>;\n\n/**\n * Base class for both `HttpResponse` and `HttpHeaderResponse`.\n *\n * @publicApi\n */\nexport abstract class HttpResponseBase {\n  /**\n   * All response headers.\n   */\n  readonly headers: HttpHeaders;\n\n  /**\n   * Response status code.\n   */\n  readonly status: number;\n\n  /**\n   * Textual description of response status code, defaults to OK.\n   *\n   * Do not depend on this.\n   */\n  readonly statusText: string;\n\n  /**\n   * URL of the resource retrieved, or null if not available.\n   */\n  readonly url: string | null;\n\n  /**\n   * Whether the status code falls in the 2xx range.\n   */\n  readonly ok: boolean;\n\n  /**\n   * Type of the response, narrowed to either the full response or the header.\n   */\n  // TODO(issue/24571): remove '!'.\n  readonly type!: HttpEventType.Response | HttpEventType.ResponseHeader;\n\n  /**\n   * Super-constructor for all responses.\n   *\n   * The single parameter accepted is an initialization hash. Any properties\n   * of the response passed there will override the default values.\n   */\n  constructor(\n    init: {\n      headers?: HttpHeaders;\n      status?: number;\n      statusText?: string;\n      url?: string;\n    },\n    defaultStatus: number = 200,\n    defaultStatusText: string = 'OK',\n  ) {\n    // If the hash has values passed, use them to initialize the response.\n    // Otherwise use the default values.\n    this.headers = init.headers || new HttpHeaders();\n    this.status = init.status !== undefined ? init.status : defaultStatus;\n    this.statusText = init.statusText || defaultStatusText;\n    this.url = init.url || null;\n\n    // Cache the ok value to avoid defining a getter.\n    this.ok = this.status >= 200 && this.status < 300;\n  }\n}\n\n/**\n * A partial HTTP response which only includes the status and header data,\n * but no response body.\n *\n * `HttpHeaderResponse` is a `HttpEvent` available on the response\n * event stream, only when progress events are requested.\n *\n * @publicApi\n */\nexport class HttpHeaderResponse extends HttpResponseBase {\n  /**\n   * Create a new `HttpHeaderResponse` with the given parameters.\n   */\n  constructor(\n    init: {\n      headers?: HttpHeaders;\n      status?: number;\n      statusText?: string;\n      url?: string;\n    } = {},\n  ) {\n    super(init);\n  }\n\n  override readonly type: HttpEventType.ResponseHeader = HttpEventType.ResponseHeader;\n\n  /**\n   * Copy this `HttpHeaderResponse`, overriding its contents with the\n   * given parameter hash.\n   */\n  clone(\n    update: {headers?: HttpHeaders; status?: number; statusText?: string; url?: string} = {},\n  ): HttpHeaderResponse {\n    // Perform a straightforward initialization of the new HttpHeaderResponse,\n    // overriding the current parameters with new ones if given.\n    return new HttpHeaderResponse({\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined,\n    });\n  }\n}\n\n/**\n * A full HTTP response, including a typed response body (which may be `null`\n * if one was not returned).\n *\n * `HttpResponse` is a `HttpEvent` available on the response event\n * stream.\n *\n * @publicApi\n */\nexport class HttpResponse<T> extends HttpResponseBase {\n  /**\n   * The response body, or `null` if one was not returned.\n   */\n  readonly body: T | null;\n\n  /**\n   * Construct a new `HttpResponse`.\n   */\n  constructor(\n    init: {\n      body?: T | null;\n      headers?: HttpHeaders;\n      status?: number;\n      statusText?: string;\n      url?: string;\n    } = {},\n  ) {\n    super(init);\n    this.body = init.body !== undefined ? init.body : null;\n  }\n\n  override readonly type: HttpEventType.Response = HttpEventType.Response;\n\n  clone(): HttpResponse<T>;\n  clone(update: {\n    headers?: HttpHeaders;\n    status?: number;\n    statusText?: string;\n    url?: string;\n  }): HttpResponse<T>;\n  clone<V>(update: {\n    body?: V | null;\n    headers?: HttpHeaders;\n    status?: number;\n    statusText?: string;\n    url?: string;\n  }): HttpResponse<V>;\n  clone(\n    update: {\n      body?: any | null;\n      headers?: HttpHeaders;\n      status?: number;\n      statusText?: string;\n      url?: string;\n    } = {},\n  ): HttpResponse<any> {\n    return new HttpResponse<any>({\n      body: update.body !== undefined ? update.body : this.body,\n      headers: update.headers || this.headers,\n      status: update.status !== undefined ? update.status : this.status,\n      statusText: update.statusText || this.statusText,\n      url: update.url || this.url || undefined,\n    });\n  }\n}\n\n/**\n * A response that represents an error or failure, either from a\n * non-successful HTTP status, an error while executing the request,\n * or some other failure which occurred during the parsing of the response.\n *\n * Any error returned on the `Observable` response stream will be\n * wrapped in an `HttpErrorResponse` to provide additional context about\n * the state of the HTTP layer when the error occurred. The error property\n * will contain either a wrapped Error object or the error response returned\n * from the server.\n *\n * @publicApi\n */\nexport class HttpErrorResponse extends HttpResponseBase implements Error {\n  readonly name = 'HttpErrorResponse';\n  readonly message: string;\n  readonly error: any | null;\n\n  /**\n   * Errors are never okay, even when the status code is in the 2xx success range.\n   */\n  override readonly ok = false;\n\n  constructor(init: {\n    error?: any;\n    headers?: HttpHeaders;\n    status?: number;\n    statusText?: string;\n    url?: string;\n  }) {\n    // Initialize with a default status of 0 / Unknown Error.\n    super(init, 0, 'Unknown Error');\n\n    // If the response was successful, then this was a parse error. Otherwise, it was\n    // a protocol-level failure of some sort. Either the request failed in transit\n    // or the server returned an unsuccessful status code.\n    if (this.status >= 200 && this.status < 300) {\n      this.message = `Http failure during parsing for ${init.url || '(unknown url)'}`;\n    } else {\n      this.message = `Http failure response for ${init.url || '(unknown url)'}: ${init.status} ${\n        init.statusText\n      }`;\n    }\n    this.error = init.error || null;\n  }\n}\n\n/**\n * We use these constant to prevent pulling the whole HttpStatusCode enum\n * Those are the only ones referenced directly by the framework\n */\nexport const HTTP_STATUS_CODE_OK = 200;\nexport const HTTP_STATUS_CODE_NO_CONTENT = 204;\n\n/**\n * Http status codes.\n * As per https://www.iana.org/assignments/http-status-codes/http-status-codes.xhtml\n * @publicApi\n */\nexport enum HttpStatusCode {\n  Continue = 100,\n  SwitchingProtocols = 101,\n  Processing = 102,\n  EarlyHints = 103,\n\n  Ok = HTTP_STATUS_CODE_OK,\n  Created = 201,\n  Accepted = 202,\n  NonAuthoritativeInformation = 203,\n  NoContent = HTTP_STATUS_CODE_NO_CONTENT,\n  ResetContent = 205,\n  PartialContent = 206,\n  MultiStatus = 207,\n  AlreadyReported = 208,\n  ImUsed = 226,\n\n  MultipleChoices = 300,\n  MovedPermanently = 301,\n  Found = 302,\n  SeeOther = 303,\n  NotModified = 304,\n  UseProxy = 305,\n  Unused = 306,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n\n  BadRequest = 400,\n  Unauthorized = 401,\n  PaymentRequired = 402,\n  Forbidden = 403,\n  NotFound = 404,\n  MethodNotAllowed = 405,\n  NotAcceptable = 406,\n  ProxyAuthenticationRequired = 407,\n  RequestTimeout = 408,\n  Conflict = 409,\n  Gone = 410,\n  LengthRequired = 411,\n  PreconditionFailed = 412,\n  PayloadTooLarge = 413,\n  UriTooLong = 414,\n  UnsupportedMediaType = 415,\n  RangeNotSatisfiable = 416,\n  ExpectationFailed = 417,\n  ImATeapot = 418,\n  MisdirectedRequest = 421,\n  UnprocessableEntity = 422,\n  Locked = 423,\n  FailedDependency = 424,\n  TooEarly = 425,\n  UpgradeRequired = 426,\n  PreconditionRequired = 428,\n  TooManyRequests = 429,\n  RequestHeaderFieldsTooLarge = 431,\n  UnavailableForLegalReasons = 451,\n\n  InternalServerError = 500,\n  NotImplemented = 501,\n  BadGateway = 502,\n  ServiceUnavailable = 503,\n  GatewayTimeout = 504,\n  HttpVersionNotSupported = 505,\n  VariantAlsoNegotiates = 506,\n  InsufficientStorage = 507,\n  LoopDetected = 508,\n  NotExtended = 510,\n  NetworkAuthenticationRequired = 511,\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {Observable, of} from 'rxjs';\nimport {concatMap, filter, map} from 'rxjs/operators';\n\nimport {HttpHandler} from './backend';\nimport {HttpContext} from './context';\nimport {HttpHeaders} from './headers';\nimport {HttpParams, HttpParamsOptions} from './params';\nimport {HttpRequest} from './request';\nimport {HttpEvent, HttpResponse} from './response';\n\n/**\n * Constructs an instance of `HttpRequestOptions<T>` from a source `HttpMethodOptions` and\n * the given `body`. This function clones the object and adds the body.\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n *\n */\nfunction addBody<T>(\n  options: {\n    headers?: HttpHeaders | {[header: string]: string | string[]};\n    context?: HttpContext;\n    observe?: 'body' | 'events' | 'response';\n    params?:\n      | HttpParams\n      | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n    reportProgress?: boolean;\n    responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n    withCredentials?: boolean;\n    transferCache?: {includeHeaders?: string[]} | boolean;\n  },\n  body: T | null,\n): any {\n  return {\n    body,\n    headers: options.headers,\n    context: options.context,\n    observe: options.observe,\n    params: options.params,\n    reportProgress: options.reportProgress,\n    responseType: options.responseType,\n    withCredentials: options.withCredentials,\n    transferCache: options.transferCache,\n  };\n}\n\n/**\n * Performs HTTP requests.\n * This service is available as an injectable class, with methods to perform HTTP requests.\n * Each request method has multiple signatures, and the return type varies based on\n * the signature that is called (mainly the values of `observe` and `responseType`).\n *\n * Note that the `responseType` *options* value is a String that identifies the\n * single data type of the response.\n * A single overload version of the method handles each response type.\n * The value of `responseType` cannot be a union, as the combined signature could imply.\n\n * TODO(adev): review\n * @usageNotes\n *\n * ### HTTP Request Example\n *\n * ```\n *  // GET heroes whose name contains search term\n * searchHeroes(term: string): observable<Hero[]>{\n *\n *  const params = new HttpParams({fromString: 'name=term'});\n *    return this.httpClient.request('GET', this.heroesUrl, {responseType:'json', params});\n * }\n * ```\n *\n * Alternatively, the parameter string can be used without invoking HttpParams\n * by directly joining to the URL.\n * ```\n * this.httpClient.request('GET', this.heroesUrl + '?' + 'name=term', {responseType:'json'});\n * ```\n *\n *\n * ### JSONP Example\n * ```\n * requestJsonp(url, callback = 'callback') {\n *  return this.httpClient.jsonp(this.heroesURL, callback);\n * }\n * ```\n *\n * ### PATCH Example\n * ```\n * // PATCH one of the heroes' name\n * patchHero (id: number, heroName: string): Observable<{}> {\n * const url = `${this.heroesUrl}/${id}`;   // PATCH api/heroes/42\n *  return this.httpClient.patch(url, {name: heroName}, httpOptions)\n *    .pipe(catchError(this.handleError('patchHero')));\n * }\n * ```\n *\n * @see [HTTP Guide](guide/http)\n * @see [HTTP Request](api/common/http/HttpRequest)\n *\n * @publicApi\n */\n@Injectable()\nexport class HttpClient {\n  constructor(private handler: HttpHandler) {}\n\n  /**\n   * Sends an `HttpRequest` and returns a stream of `HttpEvent`s.\n   *\n   * @return An `Observable` of the response, with the response body as a stream of `HttpEvent`s.\n   */\n  request<R>(req: HttpRequest<any>): Observable<HttpEvent<R>>;\n\n  /**\n   * Constructs a request that interprets the body as an `ArrayBuffer` and returns the response in\n   * an `ArrayBuffer`.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   *\n   * @return An `Observable` of the response, with the response body as an `ArrayBuffer`.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<ArrayBuffer>;\n\n  /**\n   * Constructs a request that interprets the body as a blob and returns\n   * the response as a blob.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body of type `Blob`.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<Blob>;\n\n  /**\n   * Constructs a request that interprets the body as a text string and\n   * returns a string value.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body of type string.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<string>;\n\n  /**\n   * Constructs a request that interprets the body as an `ArrayBuffer` and returns the\n   * the full event stream.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body as an array of `HttpEvent`s for\n   * the request.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      observe: 'events';\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<ArrayBuffer>>;\n\n  /**\n   * Constructs a request that interprets the body as a `Blob` and returns\n   * the full event stream.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with the response body of type `Blob`.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<Blob>>;\n\n  /**\n   * Constructs a request which interprets the body as a text string and returns the full event\n   * stream.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with the response body of type string.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<string>>;\n\n  /**\n   * Constructs a request which interprets the body as a JavaScript object and returns the full\n   * event stream.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the  request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with the response body of type `Object`.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      reportProgress?: boolean;\n      observe: 'events';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<any>>;\n\n  /**\n   * Constructs a request which interprets the body as a JavaScript object and returns the full\n   * event stream.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with the response body of type `R`.\n   */\n  request<R>(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      reportProgress?: boolean;\n      observe: 'events';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<R>>;\n\n  /**\n   * Constructs a request which interprets the body as an `ArrayBuffer`\n   * and returns the full `HttpResponse`.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse`, with the response body as an `ArrayBuffer`.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<ArrayBuffer>>;\n\n  /**\n   * Constructs a request which interprets the body as a `Blob` and returns the full `HttpResponse`.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse`, with the response body of type `Blob`.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<Blob>>;\n\n  /**\n   * Constructs a request which interprets the body as a text stream and returns the full\n   * `HttpResponse`.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the HTTP response, with the response body of type string.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<string>>;\n\n  /**\n   * Constructs a request which interprets the body as a JavaScript object and returns the full\n   * `HttpResponse`.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the full `HttpResponse`,\n   * with the response body of type `Object`.\n   */\n  request(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      reportProgress?: boolean;\n      observe: 'response';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<Object>>;\n\n  /**\n   * Constructs a request which interprets the body as a JavaScript object and returns\n   * the full `HttpResponse` with the response body in the requested type.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return  An `Observable` of the full `HttpResponse`, with the response body of type `R`.\n   */\n  request<R>(\n    method: string,\n    url: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      reportProgress?: boolean;\n      observe: 'response';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<R>>;\n\n  /**\n   * Constructs a request which interprets the body as a JavaScript object and returns the full\n   * `HttpResponse` as a JavaScript object.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse`, with the response body of type `Object`.\n   */\n  request(\n    method: string,\n    url: string,\n    options?: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      responseType?: 'json';\n      reportProgress?: boolean;\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<Object>;\n\n  /**\n   * Constructs a request which interprets the body as a JavaScript object\n   * with the response body of the requested type.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse`, with the response body of type `R`.\n   */\n  request<R>(\n    method: string,\n    url: string,\n    options?: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      responseType?: 'json';\n      reportProgress?: boolean;\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<R>;\n\n  /**\n   * Constructs a request where response type and requested observable are not known statically.\n   *\n   * @param method  The HTTP method.\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the requested response, with body of type `any`.\n   */\n  request(\n    method: string,\n    url: string,\n    options?: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      observe?: 'body' | 'events' | 'response';\n      reportProgress?: boolean;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<any>;\n\n  /**\n   * Constructs an observable for a generic HTTP request that, when subscribed,\n   * fires the request through the chain of registered interceptors and on to the\n   * server.\n   *\n   * You can pass an `HttpRequest` directly as the only parameter. In this case,\n   * the call returns an observable of the raw `HttpEvent` stream.\n   *\n   * Alternatively you can pass an HTTP method as the first parameter,\n   * a URL string as the second, and an options hash containing the request body as the third.\n   * See `addBody()`. In this case, the specified `responseType` and `observe` options determine the\n   * type of returned observable.\n   *   * The `responseType` value determines how a successful response body is parsed.\n   *   * If `responseType` is the default `json`, you can pass a type interface for the resulting\n   * object as a type parameter to the call.\n   *\n   * The `observe` value determines the return type, according to what you are interested in\n   * observing.\n   *   * An `observe` value of events returns an observable of the raw `HttpEvent` stream, including\n   * progress events by default.\n   *   * An `observe` value of response returns an observable of `HttpResponse<T>`,\n   * where the `T` parameter depends on the `responseType` and any optionally provided type\n   * parameter.\n   *   * An `observe` value of body returns an observable of `<T>` with the same `T` body type.\n   *\n   */\n  request(\n    first: string | HttpRequest<any>,\n    url?: string,\n    options: {\n      body?: any;\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body' | 'events' | 'response';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    } = {},\n  ): Observable<any> {\n    let req: HttpRequest<any>;\n    // First, check whether the primary argument is an instance of `HttpRequest`.\n    if (first instanceof HttpRequest) {\n      // It is. The other arguments must be undefined (per the signatures) and can be\n      // ignored.\n      req = first;\n    } else {\n      // It's a string, so it represents a URL. Construct a request based on it,\n      // and incorporate the remaining arguments (assuming `GET` unless a method is\n      // provided.\n\n      // Figure out the headers.\n      let headers: HttpHeaders | undefined = undefined;\n      if (options.headers instanceof HttpHeaders) {\n        headers = options.headers;\n      } else {\n        headers = new HttpHeaders(options.headers);\n      }\n\n      // Sort out parameters.\n      let params: HttpParams | undefined = undefined;\n      if (!!options.params) {\n        if (options.params instanceof HttpParams) {\n          params = options.params;\n        } else {\n          params = new HttpParams({fromObject: options.params} as HttpParamsOptions);\n        }\n      }\n\n      // Construct the request.\n      req = new HttpRequest(first, url!, options.body !== undefined ? options.body : null, {\n        headers,\n        context: options.context,\n        params,\n        reportProgress: options.reportProgress,\n        // By default, JSON is assumed to be returned for all calls.\n        responseType: options.responseType || 'json',\n        withCredentials: options.withCredentials,\n        transferCache: options.transferCache,\n      });\n    }\n\n    // Start with an Observable.of() the initial request, and run the handler (which\n    // includes all interceptors) inside a concatMap(). This way, the handler runs\n    // inside an Observable chain, which causes interceptors to be re-run on every\n    // subscription (this also makes retries re-run the handler, including interceptors).\n    const events$: Observable<HttpEvent<any>> = of(req).pipe(\n      concatMap((req: HttpRequest<any>) => this.handler.handle(req)),\n    );\n\n    // If coming via the API signature which accepts a previously constructed HttpRequest,\n    // the only option is to get the event stream. Otherwise, return the event stream if\n    // that is what was requested.\n    if (first instanceof HttpRequest || options.observe === 'events') {\n      return events$;\n    }\n\n    // The requested stream contains either the full response or the body. In either\n    // case, the first step is to filter the event stream to extract a stream of\n    // responses(s).\n    const res$: Observable<HttpResponse<any>> = <Observable<HttpResponse<any>>>(\n      events$.pipe(filter((event: HttpEvent<any>) => event instanceof HttpResponse))\n    );\n\n    // Decide which stream to return.\n    switch (options.observe || 'body') {\n      case 'body':\n        // The requested stream is the body. Map the response stream to the response\n        // body. This could be done more simply, but a misbehaving interceptor might\n        // transform the response body into a different format and ignore the requested\n        // responseType. Guard against this by validating that the response is of the\n        // requested type.\n        switch (req.responseType) {\n          case 'arraybuffer':\n            return res$.pipe(\n              map((res: HttpResponse<any>) => {\n                // Validate that the body is an ArrayBuffer.\n                if (res.body !== null && !(res.body instanceof ArrayBuffer)) {\n                  throw new Error('Response is not an ArrayBuffer.');\n                }\n                return res.body;\n              }),\n            );\n          case 'blob':\n            return res$.pipe(\n              map((res: HttpResponse<any>) => {\n                // Validate that the body is a Blob.\n                if (res.body !== null && !(res.body instanceof Blob)) {\n                  throw new Error('Response is not a Blob.');\n                }\n                return res.body;\n              }),\n            );\n          case 'text':\n            return res$.pipe(\n              map((res: HttpResponse<any>) => {\n                // Validate that the body is a string.\n                if (res.body !== null && typeof res.body !== 'string') {\n                  throw new Error('Response is not a string.');\n                }\n                return res.body;\n              }),\n            );\n          case 'json':\n          default:\n            // No validation needed for JSON responses, as they can be of any type.\n            return res$.pipe(map((res: HttpResponse<any>) => res.body));\n        }\n      case 'response':\n        // The response stream was requested directly, so return it.\n        return res$;\n      default:\n        // Guard against new future observe types being added.\n        throw new Error(`Unreachable: unhandled observe type ${options.observe}}`);\n    }\n  }\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as an `ArrayBuffer`\n   *  and returns the response as an `ArrayBuffer`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return  An `Observable` of the response body as an `ArrayBuffer`.\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<ArrayBuffer>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as a `Blob` and returns\n   * the response as a `Blob`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response body as a `Blob`.\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<Blob>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as a text string and returns\n   * a string.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body of type string.\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<string>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as an `ArrayBuffer`\n   *  and returns the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with response body as an `ArrayBuffer`.\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<HttpEvent<ArrayBuffer>>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as a `Blob`\n   *  and returns the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request, with the response body as a\n   * `Blob`.\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<HttpEvent<Blob>>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as a text string\n   * and returns the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request, with the response\n   * body of type string.\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<HttpEvent<string>>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as JSON\n   * and returns the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request, with response body of\n   * type `Object`.\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<HttpEvent<Object>>;\n\n  /**\n   * Constructs a `DELETE`request that interprets the body as JSON\n   * and returns the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request, with a response\n   * body in the requested type.\n   */\n  delete<T>(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | (string | number | boolean)[]};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<HttpEvent<T>>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as an `ArrayBuffer` and returns\n   *  the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the full `HttpResponse`, with the response body as an `ArrayBuffer`.\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<HttpResponse<ArrayBuffer>>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as a `Blob` and returns the full\n   * `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse`, with the response body of type `Blob`.\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<HttpResponse<Blob>>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as a text stream and\n   *  returns the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the full `HttpResponse`, with the response body of type string.\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<HttpResponse<string>>;\n\n  /**\n   * Constructs a `DELETE` request the interprets the body as a JavaScript object and returns\n   * the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse`, with the response body of type `Object`.\n   *\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<HttpResponse<Object>>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as JSON\n   * and returns the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse`, with the response body of the requested type.\n   */\n  delete<T>(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<HttpResponse<T>>;\n\n  /**\n   * Constructs a `DELETE` request that interprets the body as JSON and\n   * returns the response body as an object parsed from JSON.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body of type `Object`.\n   */\n  delete(\n    url: string,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<Object>;\n\n  /**\n   * Constructs a DELETE request that interprets the body as JSON and returns\n   * the response in a given type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse`, with response body in the requested type.\n   */\n  delete<T>(\n    url: string,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      body?: any | null;\n    },\n  ): Observable<T>;\n\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `DELETE` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   */\n  delete(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body' | 'events' | 'response';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      body?: any | null;\n    } = {},\n  ): Observable<any> {\n    return this.request<any>('DELETE', url, options as any);\n  }\n\n  /**\n   * Constructs a `GET` request that interprets the body as an `ArrayBuffer` and returns the\n   * response in an `ArrayBuffer`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body as an `ArrayBuffer`.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<ArrayBuffer>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as a `Blob`\n   * and returns the response as a `Blob`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body as a `Blob`.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<Blob>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as a text string\n   * and returns the response as a string value.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body of type string.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<string>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as an `ArrayBuffer` and returns\n   *  the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request, with the response\n   * body as an `ArrayBuffer`.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<ArrayBuffer>>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as a `Blob` and\n   * returns the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body as a `Blob`.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<Blob>>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as a text string and returns\n   * the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body of type string.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<string>>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as JSON\n   * and returns the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body of type `Object`.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<Object>>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as JSON and returns the full\n   * event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with a response body in the requested type.\n   */\n  get<T>(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<T>>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as an `ArrayBuffer` and\n   * returns the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body as an `ArrayBuffer`.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<ArrayBuffer>>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as a `Blob` and\n   * returns the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body as a `Blob`.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<Blob>>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as a text stream and\n   * returns the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body of type string.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<string>>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as JSON and\n   * returns the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the full `HttpResponse`,\n   * with the response body of type `Object`.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<Object>>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as JSON and\n   * returns the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the full `HttpResponse` for the request,\n   * with a response body in the requested type.\n   */\n  get<T>(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<T>>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as JSON and\n   * returns the response body as an object parsed from JSON.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   *\n   * @return An `Observable` of the response body as a JavaScript object.\n   */\n  get(\n    url: string,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<Object>;\n\n  /**\n   * Constructs a `GET` request that interprets the body as JSON and returns\n   * the response body in a given type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse`, with a response body in the requested type.\n   */\n  get<T>(\n    url: string,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<T>;\n\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `GET` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  get(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body' | 'events' | 'response';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    } = {},\n  ): Observable<any> {\n    return this.request<any>('GET', url, options as any);\n  }\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as an `ArrayBuffer` and\n   * returns the response as an `ArrayBuffer`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body as an `ArrayBuffer`.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<ArrayBuffer>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as a `Blob` and returns\n   * the response as a `Blob`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return  An `Observable` of the response, with the response body as a `Blob`.\n   */\n\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<Blob>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as a text string and returns the response\n   * as a string value.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body of type string.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<string>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as an  `ArrayBuffer`\n   *  and returns the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with the response body as an `ArrayBuffer`.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<ArrayBuffer>>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as a `Blob` and\n   * returns the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with the response body as a `Blob`.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<Blob>>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as a text string\n   * and returns the full event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request, with the response body of type\n   * string.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<string>>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as JSON\n   * and returns the full HTTP event stream.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request, with a response body of\n   * type `Object`.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<Object>>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as JSON and\n   * returns the full event stream.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request,\n   * with a response body in the requested type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   */\n  head<T>(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<T>>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as an `ArrayBuffer`\n   *  and returns the full HTTP response.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body as an `ArrayBuffer`.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<ArrayBuffer>>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as a `Blob` and returns\n   * the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body as a blob.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<Blob>>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as text stream\n   * and returns the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body of type string.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<string>>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as JSON and\n   * returns the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body of type `Object`.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<Object>>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as JSON\n   * and returns the full `HttpResponse`.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with a response body of the requested type.\n   */\n  head<T>(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<T>>;\n\n  /**\n\n   * Constructs a `HEAD` request that interprets the body as JSON and\n   * returns the response body as an object parsed from JSON.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the response, with the response body as an object parsed from JSON.\n   */\n  head(\n    url: string,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<Object>;\n\n  /**\n   * Constructs a `HEAD` request that interprets the body as JSON and returns\n   * the response in a given type.\n   *\n   * @param url     The endpoint URL.\n   * @param options The HTTP options to send with the request.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with a response body of the given type.\n   */\n  head<T>(\n    url: string,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<T>;\n\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `HEAD` request to execute on the server. The `HEAD` method returns\n   * meta information about the resource without transferring the\n   * resource itself. See the individual overloads for\n   * details on the return type.\n   */\n  head(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body' | 'events' | 'response';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    } = {},\n  ): Observable<any> {\n    return this.request<any>('HEAD', url, options as any);\n  }\n\n  /**\n   * Constructs a `JSONP` request for the given URL and name of the callback parameter.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   * @return An `Observable` of the response object, with response body as an object.\n   */\n  jsonp(url: string, callbackParam: string): Observable<Object>;\n\n  /**\n   * Constructs a `JSONP` request for the given URL and name of the callback parameter.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   * You must install a suitable interceptor, such as one provided by `HttpClientJsonpModule`.\n   * If no such interceptor is reached,\n   * then the `JSONP` request can be rejected by the configured backend.\n   *\n   * @return An `Observable` of the response object, with response body in the requested type.\n   */\n  jsonp<T>(url: string, callbackParam: string): Observable<T>;\n\n  /**\n   * Constructs an `Observable` that, when subscribed, causes a request with the special method\n   * `JSONP` to be dispatched via the interceptor pipeline.\n   * The [JSONP pattern](https://en.wikipedia.org/wiki/JSONP) works around limitations of certain\n   * API endpoints that don't support newer,\n   * and preferable [CORS](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS) protocol.\n   * JSONP treats the endpoint API as a JavaScript file and tricks the browser to process the\n   * requests even if the API endpoint is not located on the same domain (origin) as the client-side\n   * application making the request.\n   * The endpoint API must support JSONP callback for JSONP requests to work.\n   * The resource API returns the JSON response wrapped in a callback function.\n   * You can pass the callback function name as one of the query parameters.\n   * Note that JSONP requests can only be used with `GET` requests.\n   *\n   * @param url The resource URL.\n   * @param callbackParam The callback function name.\n   *\n   */\n  jsonp<T>(url: string, callbackParam: string): Observable<T> {\n    return this.request<any>('JSONP', url, {\n      params: new HttpParams().append(callbackParam, 'JSONP_CALLBACK'),\n      observe: 'body',\n      responseType: 'json',\n    });\n  }\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as an\n   * `ArrayBuffer` and returns the response as an `ArrayBuffer`.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the response, with the response body as an `ArrayBuffer`.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n    },\n  ): Observable<ArrayBuffer>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as a `Blob` and returns\n   * the response as a `Blob`.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the response, with the response body as a `Blob`.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n    },\n  ): Observable<Blob>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as a text string and\n   * returns a string value.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the response, with the response body of type string.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n    },\n  ): Observable<string>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as an `ArrayBuffer`\n   *  and returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return  An `Observable` of all `HttpEvent`s for the request,\n   * with the response body as an `ArrayBuffer`.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<ArrayBuffer>>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as a `Blob` and\n   * returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with the response body as a `Blob`.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<Blob>>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as a text string\n   * and returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request,\n   * with the response body of type string.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<string>>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as JSON\n   * and returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request with the response\n   * body of type `Object`.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<Object>>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as JSON and\n   * returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request,\n   * with a response body in the requested type.\n   */\n  options<T>(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<T>>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as an `ArrayBuffer`\n   *  and returns the full HTTP response.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body as an `ArrayBuffer`.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<ArrayBuffer>>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as a `Blob`\n   *  and returns the full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body as a `Blob`.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<Blob>>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as text stream\n   * and returns the full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body of type string.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<string>>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as JSON\n   * and returns the full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body of type `Object`.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<Object>>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as JSON and\n   * returns the full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with a response body in the requested type.\n   */\n  options<T>(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<T>>;\n\n  /**\n\n   * Constructs an `OPTIONS` request that interprets the body as JSON and returns the\n   * response body as an object parsed from JSON.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the response, with the response body as an object parsed from JSON.\n   */\n  options(\n    url: string,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<Object>;\n\n  /**\n   * Constructs an `OPTIONS` request that interprets the body as JSON and returns the\n   * response in a given type.\n   *\n   * @param url The endpoint URL.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the `HttpResponse`, with a response body of the given type.\n   */\n  options<T>(\n    url: string,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<T>;\n\n  /**\n   * Constructs an `Observable` that, when subscribed, causes the configured\n   * `OPTIONS` request to execute on the server. This method allows the client\n   * to determine the supported HTTP methods and other capabilities of an endpoint,\n   * without implying a resource action. See the individual overloads for\n   * details on the return type.\n   */\n  options(\n    url: string,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body' | 'events' | 'response';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n    } = {},\n  ): Observable<any> {\n    return this.request<any>('OPTIONS', url, options as any);\n  }\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as an `ArrayBuffer` and returns\n   * the response as an `ArrayBuffer`.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the response, with the response body as an `ArrayBuffer`.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n    },\n  ): Observable<ArrayBuffer>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as a `Blob` and returns the response\n   * as a `Blob`.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the response, with the response body as a `Blob`.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n    },\n  ): Observable<Blob>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as a text string and\n   * returns the response as a string value.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the response, with a response body of type string.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n    },\n  ): Observable<string>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as an `ArrayBuffer` and\n   *  returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request,\n   * with the response body as an `ArrayBuffer`.\n   */\n\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<ArrayBuffer>>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as a `Blob`\n   *  and returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request, with the\n   * response body as `Blob`.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<Blob>>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as a text string and\n   * returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request, with a\n   * response body of type string.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<string>>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as JSON\n   * and returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request,\n   * with a response body of type `Object`.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<Object>>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as JSON\n   * and returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of all the `HttpEvent`s for the request,\n   * with a response body in the requested type.\n   */\n  patch<T>(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<T>>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as an `ArrayBuffer`\n   *  and returns the full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return  An `Observable` of the `HttpResponse` for the request,\n   * with the response body as an `ArrayBuffer`.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<ArrayBuffer>>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as a `Blob` and returns the full\n   * `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return  An `Observable` of the `HttpResponse` for the request,\n   * with the response body as a `Blob`.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<Blob>>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as a text stream and returns the\n   * full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return  An `Observable` of the `HttpResponse` for the request,\n   * with a response body of type string.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<string>>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as JSON\n   * and returns the full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with a response body in the requested type.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<Object>>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as JSON\n   * and returns the full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with a response body in the given type.\n   */\n  patch<T>(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<T>>;\n\n  /**\n\n   * Constructs a `PATCH` request that interprets the body as JSON and\n   * returns the response body as an object parsed from JSON.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the response, with the response body as an object parsed from JSON.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<Object>;\n\n  /**\n   * Constructs a `PATCH` request that interprets the body as JSON\n   * and returns the response in a given type.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to edit.\n   * @param options HTTP options.\n   *\n   * @return  An `Observable` of the `HttpResponse` for the request,\n   * with a response body in the given type.\n   */\n  patch<T>(\n    url: string,\n    body: any | null,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<T>;\n\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PATCH` request to execute on the server. See the individual overloads for\n   * details on the return type.\n   */\n  patch(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body' | 'events' | 'response';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n    } = {},\n  ): Observable<any> {\n    return this.request<any>('PATCH', url, addBody(options, body));\n  }\n\n  /**\n   * Constructs a `POST` request that interprets the body as an `ArrayBuffer` and returns\n   * an `ArrayBuffer`.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options.\n   *\n   * @return An `Observable` of the response, with the response body as an `ArrayBuffer`.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<ArrayBuffer>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as a `Blob` and returns the\n   * response as a `Blob`.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the response, with the response body as a `Blob`.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<Blob>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as a text string and\n   * returns the response as a string value.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the response, with a response body of type string.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<string>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as an `ArrayBuffer` and\n   * returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with the response body as an `ArrayBuffer`.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<ArrayBuffer>>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as a `Blob`\n   * and returns the response in an observable of the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request, with the response body as `Blob`.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<Blob>>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as a text string and returns the full\n   * event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return  An `Observable` of all `HttpEvent`s for the request,\n   * with a response body of type string.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<string>>;\n\n  /**\n   * Constructs a POST request that interprets the body as JSON and returns the full\n   * event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return  An `Observable` of all `HttpEvent`s for the request,\n   * with a response body of type `Object`.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<Object>>;\n\n  /**\n   * Constructs a POST request that interprets the body as JSON and returns the full\n   * event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with a response body in the requested type.\n   */\n  post<T>(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpEvent<T>>;\n\n  /**\n   * Constructs a POST request that interprets the body as an `ArrayBuffer`\n   *  and returns the full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return  An `Observable` of the `HttpResponse` for the request, with the response body as an\n   * `ArrayBuffer`.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<ArrayBuffer>>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as a `Blob` and returns the full\n   * `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body as a `Blob`.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<Blob>>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as a text stream and returns\n   * the full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return  An `Observable` of the `HttpResponse` for the request,\n   * with a response body of type string.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<string>>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as JSON\n   * and returns the full `HttpResponse`.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the `HttpResponse` for the request, with a response body of type\n   * `Object`.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<Object>>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as JSON and returns the\n   * full `HttpResponse`.\n   *\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the `HttpResponse` for the request, with a response body in the\n   * requested type.\n   */\n  post<T>(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<HttpResponse<T>>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as JSON\n   * and returns the response body as an object parsed from JSON.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the response, with the response body as an object parsed from JSON.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<Object>;\n\n  /**\n   * Constructs a `POST` request that interprets the body as JSON\n   * and returns an observable of the response.\n   *\n   * @param url The endpoint URL.\n   * @param body The content to replace with.\n   * @param options HTTP options\n   *\n   * @return  An `Observable` of the `HttpResponse` for the request, with a response body in the\n   * requested type.\n   */\n  post<T>(\n    url: string,\n    body: any | null,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    },\n  ): Observable<T>;\n\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `POST` request to execute on the server. The server responds with the location of\n   * the replaced resource. See the individual overloads for\n   * details on the return type.\n   */\n  post(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body' | 'events' | 'response';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n      transferCache?: {includeHeaders?: string[]} | boolean;\n    } = {},\n  ): Observable<any> {\n    return this.request<any>('POST', url, addBody(options, body));\n  }\n\n  /**\n   * Constructs a `PUT` request that interprets the body as an `ArrayBuffer` and returns the\n   * response as an `ArrayBuffer`.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the response, with the response body as an `ArrayBuffer`.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n    },\n  ): Observable<ArrayBuffer>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as a `Blob` and returns\n   * the response as a `Blob`.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the response, with the response body as a `Blob`.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n    },\n  ): Observable<Blob>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as a text string and\n   * returns the response as a string value.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the response, with a response body of type string.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n    },\n  ): Observable<string>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as an `ArrayBuffer` and\n   * returns the full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with the response body as an `ArrayBuffer`.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<ArrayBuffer>>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as a `Blob` and returns the full event\n   * stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with the response body as a `Blob`.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<Blob>>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as a text string and returns the full event\n   * stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request, with a response body\n   * of type string.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<string>>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as JSON and returns the full\n   * event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request, with a response body of\n   * type `Object`.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<Object>>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as JSON and returns the\n   * full event stream.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of all `HttpEvent`s for the request,\n   * with a response body in the requested type.\n   */\n  put<T>(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'events';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpEvent<T>>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as an\n   * `ArrayBuffer` and returns an observable of the full HTTP response.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the `HttpResponse` for the request, with the response body as an\n   * `ArrayBuffer`.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'arraybuffer';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<ArrayBuffer>>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as a `Blob` and returns the\n   * full HTTP response.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with the response body as a `Blob`.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'blob';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<Blob>>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as a text stream and returns the\n   * full HTTP response.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the `HttpResponse` for the request, with a response body of type\n   * string.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType: 'text';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<string>>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as JSON and returns the full\n   * HTTP response.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the `HttpResponse` for the request, with a response body\n   * of type 'Object`.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<Object>>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as an instance of the requested type and\n   * returns the full HTTP response.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the `HttpResponse` for the request,\n   * with a response body in the requested type.\n   */\n  put<T>(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      observe: 'response';\n      context?: HttpContext;\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<HttpResponse<T>>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as JSON\n   * and returns an observable of JavaScript object.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the response as a JavaScript object.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<Object>;\n\n  /**\n   * Constructs a `PUT` request that interprets the body as an instance of the requested type\n   * and returns an observable of the requested type.\n   *\n   * @param url The endpoint URL.\n   * @param body The resources to add/update.\n   * @param options HTTP options\n   *\n   * @return An `Observable` of the requested type.\n   */\n  put<T>(\n    url: string,\n    body: any | null,\n    options?: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'json';\n      withCredentials?: boolean;\n    },\n  ): Observable<T>;\n\n  /**\n   * Constructs an observable that, when subscribed, causes the configured\n   * `PUT` request to execute on the server. The `PUT` method replaces an existing resource\n   * with a new set of values.\n   * See the individual overloads for details on the return type.\n   */\n  put(\n    url: string,\n    body: any | null,\n    options: {\n      headers?: HttpHeaders | {[header: string]: string | string[]};\n      context?: HttpContext;\n      observe?: 'body' | 'events' | 'response';\n      params?:\n        | HttpParams\n        | {[param: string]: string | number | boolean | ReadonlyArray<string | number | boolean>};\n      reportProgress?: boolean;\n      responseType?: 'arraybuffer' | 'blob' | 'json' | 'text';\n      withCredentials?: boolean;\n    } = {},\n  ): Observable<any> {\n    return this.request<any>('PUT', url, addBody(options, body));\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {inject, Injectable, NgZone} from '@angular/core';\nimport {Observable, Observer} from 'rxjs';\n\nimport {HttpBackend} from './backend';\nimport {HttpHeaders} from './headers';\nimport {HttpRequest} from './request';\nimport {\n  HTTP_STATUS_CODE_OK,\n  HttpDownloadProgressEvent,\n  HttpErrorResponse,\n  HttpEvent,\n  HttpEventType,\n  HttpHeaderResponse,\n  HttpResponse,\n} from './response';\n\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n\nconst REQUEST_URL_HEADER = `X-Request-URL`;\n\n/**\n * Determine an appropriate URL for the response, by checking either\n * response url or the X-Request-URL header.\n */\nfunction getResponseUrl(response: Response): string | null {\n  if (response.url) {\n    return response.url;\n  }\n  // stored as lowercase in the map\n  const xRequestUrl = REQUEST_URL_HEADER.toLocaleLowerCase();\n  return response.headers.get(xRequestUrl);\n}\n\n/**\n * Uses `fetch` to send requests to a backend server.\n *\n * This `FetchBackend` requires the support of the\n * [Fetch API](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) which is available on all\n * supported browsers and on Node.js v18 or later.\n *\n * @see {@link HttpHandler}\n *\n * @publicApi\n */\n@Injectable()\nexport class FetchBackend implements HttpBackend {\n  // We use an arrow function to always reference the current global implementation of `fetch`.\n  // This is helpful for cases when the global `fetch` implementation is modified by external code,\n  // see https://github.com/angular/angular/issues/57527.\n  private readonly fetchImpl =\n    inject(FetchFactory, {optional: true})?.fetch ?? ((...args) => globalThis.fetch(...args));\n  private readonly ngZone = inject(NgZone);\n\n  handle(request: HttpRequest<any>): Observable<HttpEvent<any>> {\n    return new Observable((observer) => {\n      const aborter = new AbortController();\n      this.doRequest(request, aborter.signal, observer).then(noop, (error) =>\n        observer.error(new HttpErrorResponse({error})),\n      );\n      return () => aborter.abort();\n    });\n  }\n\n  private async doRequest(\n    request: HttpRequest<any>,\n    signal: AbortSignal,\n    observer: Observer<HttpEvent<any>>,\n  ): Promise<void> {\n    const init = this.createRequestInit(request);\n    let response;\n\n    try {\n      // Run fetch outside of Angular zone.\n      // This is due to Node.js fetch implementation (Undici) which uses a number of setTimeouts to check if\n      // the response should eventually timeout which causes extra CD cycles every 500ms\n      const fetchPromise = this.ngZone.runOutsideAngular(() =>\n        this.fetchImpl(request.urlWithParams, {signal, ...init}),\n      );\n\n      // Make sure Zone.js doesn't trigger false-positive unhandled promise\n      // error in case the Promise is rejected synchronously. See function\n      // description for additional information.\n      silenceSuperfluousUnhandledPromiseRejection(fetchPromise);\n\n      // Send the `Sent` event before awaiting the response.\n      observer.next({type: HttpEventType.Sent});\n\n      response = await fetchPromise;\n    } catch (error: any) {\n      observer.error(\n        new HttpErrorResponse({\n          error,\n          status: error.status ?? 0,\n          statusText: error.statusText,\n          url: request.urlWithParams,\n          headers: error.headers,\n        }),\n      );\n      return;\n    }\n\n    const headers = new HttpHeaders(response.headers);\n    const statusText = response.statusText;\n    const url = getResponseUrl(response) ?? request.urlWithParams;\n\n    let status = response.status;\n    let body: string | ArrayBuffer | Blob | object | null = null;\n\n    if (request.reportProgress) {\n      observer.next(new HttpHeaderResponse({headers, status, statusText, url}));\n    }\n\n    if (response.body) {\n      // Read Progress\n      const contentLength = response.headers.get('content-length');\n      const chunks: Uint8Array[] = [];\n      const reader = response.body.getReader();\n      let receivedLength = 0;\n\n      let decoder: TextDecoder;\n      let partialText: string | undefined;\n\n      // We have to check whether the Zone is defined in the global scope because this may be called\n      // when the zone is nooped.\n      const reqZone = typeof Zone !== 'undefined' && Zone.current;\n\n      // Perform response processing outside of Angular zone to\n      // ensure no excessive change detection runs are executed\n      // Here calling the async ReadableStreamDefaultReader.read() is responsible for triggering CD\n      await this.ngZone.runOutsideAngular(async () => {\n        while (true) {\n          const {done, value} = await reader.read();\n\n          if (done) {\n            break;\n          }\n\n          chunks.push(value);\n          receivedLength += value.length;\n\n          if (request.reportProgress) {\n            partialText =\n              request.responseType === 'text'\n                ? (partialText ?? '') +\n                  (decoder ??= new TextDecoder()).decode(value, {stream: true})\n                : undefined;\n\n            const reportProgress = () =>\n              observer.next({\n                type: HttpEventType.DownloadProgress,\n                total: contentLength ? +contentLength : undefined,\n                loaded: receivedLength,\n                partialText,\n              } as HttpDownloadProgressEvent);\n            reqZone ? reqZone.run(reportProgress) : reportProgress();\n          }\n        }\n      });\n\n      // Combine all chunks.\n      const chunksAll = this.concatChunks(chunks, receivedLength);\n      try {\n        const contentType = response.headers.get('Content-Type') ?? '';\n        body = this.parseBody(request, chunksAll, contentType);\n      } catch (error) {\n        // Body loading or parsing failed\n        observer.error(\n          new HttpErrorResponse({\n            error,\n            headers: new HttpHeaders(response.headers),\n            status: response.status,\n            statusText: response.statusText,\n            url: getResponseUrl(response) ?? request.urlWithParams,\n          }),\n        );\n        return;\n      }\n    }\n\n    // Same behavior as the XhrBackend\n    if (status === 0) {\n      status = body ? HTTP_STATUS_CODE_OK : 0;\n    }\n\n    // ok determines whether the response will be transmitted on the event or\n    // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n    // but a successful status code can still result in an error if the user\n    // asked for JSON data and the body cannot be parsed as such.\n    const ok = status >= 200 && status < 300;\n\n    if (ok) {\n      observer.next(\n        new HttpResponse({\n          body,\n          headers,\n          status,\n          statusText,\n          url,\n        }),\n      );\n\n      // The full body has been received and delivered, no further events\n      // are possible. This request is complete.\n      observer.complete();\n    } else {\n      observer.error(\n        new HttpErrorResponse({\n          error: body,\n          headers,\n          status,\n          statusText,\n          url,\n        }),\n      );\n    }\n  }\n\n  private parseBody(\n    request: HttpRequest<any>,\n    binContent: Uint8Array,\n    contentType: string,\n  ): string | ArrayBuffer | Blob | object | null {\n    switch (request.responseType) {\n      case 'json':\n        // stripping the XSSI when present\n        const text = new TextDecoder().decode(binContent).replace(XSSI_PREFIX, '');\n        return text === '' ? null : (JSON.parse(text) as object);\n      case 'text':\n        return new TextDecoder().decode(binContent);\n      case 'blob':\n        return new Blob([binContent], {type: contentType});\n      case 'arraybuffer':\n        return binContent.buffer;\n    }\n  }\n\n  private createRequestInit(req: HttpRequest<any>): RequestInit {\n    // We could share some of this logic with the XhrBackend\n\n    const headers: Record<string, string> = {};\n    const credentials: RequestCredentials | undefined = req.withCredentials ? 'include' : undefined;\n\n    // Setting all the requested headers.\n    req.headers.forEach((name, values) => (headers[name] = values.join(',')));\n\n    // Add an Accept header if one isn't present already.\n    if (!req.headers.has('Accept')) {\n      headers['Accept'] = 'application/json, text/plain, */*';\n    }\n\n    // Auto-detect the Content-Type header if one isn't present already.\n    if (!req.headers.has('Content-Type')) {\n      const detectedType = req.detectContentTypeHeader();\n      // Sometimes Content-Type detection fails.\n      if (detectedType !== null) {\n        headers['Content-Type'] = detectedType;\n      }\n    }\n\n    return {\n      body: req.serializeBody(),\n      method: req.method,\n      headers,\n      credentials,\n    };\n  }\n\n  private concatChunks(chunks: Uint8Array[], totalLength: number): Uint8Array {\n    const chunksAll = new Uint8Array(totalLength);\n    let position = 0;\n    for (const chunk of chunks) {\n      chunksAll.set(chunk, position);\n      position += chunk.length;\n    }\n\n    return chunksAll;\n  }\n}\n\n/**\n * Abstract class to provide a mocked implementation of `fetch()`\n */\nexport abstract class FetchFactory {\n  abstract fetch: typeof fetch;\n}\n\nfunction noop(): void {}\n\n/**\n * Zone.js treats a rejected promise that has not yet been awaited\n * as an unhandled error. This function adds a noop `.then` to make\n * sure that Zone.js doesn't throw an error if the Promise is rejected\n * synchronously.\n */\nfunction silenceSuperfluousUnhandledPromiseRejection(promise: Promise<unknown>) {\n  promise.then(noop, noop);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {isPlatformServer} from '@angular/common';\nimport {\n  EnvironmentInjector,\n  inject,\n  Injectable,\n  InjectionToken,\n  PLATFORM_ID,\n  runInInjectionContext,\n  ɵConsole as Console,\n  ɵformatRuntimeError as formatRuntimeError,\n  ɵPendingTasks as PendingTasks,\n} from '@angular/core';\nimport {Observable} from 'rxjs';\nimport {finalize} from 'rxjs/operators';\n\nimport {HttpBackend, HttpHandler} from './backend';\nimport {RuntimeErrorCode} from './errors';\nimport {FetchBackend} from './fetch';\nimport {HttpRequest} from './request';\nimport {HttpEvent} from './response';\n\n/**\n * Intercepts and handles an `HttpRequest` or `HttpResponse`.\n *\n * Most interceptors transform the outgoing request before passing it to the\n * next interceptor in the chain, by calling `next.handle(transformedReq)`.\n * An interceptor may transform the\n * response event stream as well, by applying additional RxJS operators on the stream\n * returned by `next.handle()`.\n *\n * More rarely, an interceptor may handle the request entirely,\n * and compose a new event stream instead of invoking `next.handle()`. This is an\n * acceptable behavior, but keep in mind that further interceptors will be skipped entirely.\n *\n * It is also rare but valid for an interceptor to return multiple responses on the\n * event stream for a single request.\n *\n * @publicApi\n *\n * @see [HTTP Guide](guide/http/interceptors)\n * @see {@link HttpInterceptorFn}\n *\n * @usageNotes\n *\n * To use the same instance of `HttpInterceptors` for the entire app, import the `HttpClientModule`\n * only in your `AppModule`, and add the interceptors to the root application injector.\n * If you import `HttpClientModule` multiple times across different modules (for example, in lazy\n * loading modules), each import creates a new copy of the `HttpClientModule`, which overwrites the\n * interceptors provided in the root module.\n */\nexport interface HttpInterceptor {\n  /**\n   * Identifies and handles a given HTTP request.\n   * @param req The outgoing request object to handle.\n   * @param next The next interceptor in the chain, or the backend\n   * if no interceptors remain in the chain.\n   * @returns An observable of the event stream.\n   */\n  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>>;\n}\n\n/**\n * Represents the next interceptor in an interceptor chain, or the real backend if there are no\n * further interceptors.\n *\n * Most interceptors will delegate to this function, and either modify the outgoing request or the\n * response when it arrives. Within the scope of the current request, however, this function may be\n * called any number of times, for any number of downstream requests. Such downstream requests need\n * not be to the same URL or even the same origin as the current request. It is also valid to not\n * call the downstream handler at all, and process the current request entirely within the\n * interceptor.\n *\n * This function should only be called within the scope of the request that's currently being\n * intercepted. Once that request is complete, this downstream handler function should not be\n * called.\n *\n * @publicApi\n *\n * @see [HTTP Guide](guide/http/interceptors)\n */\nexport type HttpHandlerFn = (req: HttpRequest<unknown>) => Observable<HttpEvent<unknown>>;\n\n/**\n * An interceptor for HTTP requests made via `HttpClient`.\n *\n * `HttpInterceptorFn`s are middleware functions which `HttpClient` calls when a request is made.\n * These functions have the opportunity to modify the outgoing request or any response that comes\n * back, as well as block, redirect, or otherwise change the request or response semantics.\n *\n * An `HttpHandlerFn` representing the next interceptor (or the backend which will make a real HTTP\n * request) is provided. Most interceptors will delegate to this function, but that is not required\n * (see `HttpHandlerFn` for more details).\n *\n * `HttpInterceptorFn`s are executed in an [injection context](guide/di/dependency-injection-context).\n * They have access to `inject()` via the `EnvironmentInjector` from which they were configured.\n *\n * @see [HTTP Guide](guide/http/interceptors)\n * @see {@link withInterceptors}\n *\n * @usageNotes\n * Here is a noop interceptor that passes the request through without modifying it:\n * ```typescript\n * export const noopInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next:\n * HttpHandlerFn) => {\n *   return next(modifiedReq);\n * };\n * ```\n *\n * If you want to alter a request, clone it first and modify the clone before passing it to the\n * `next()` handler function.\n *\n * Here is a basic interceptor that adds a bearer token to the headers\n * ```typescript\n * export const authenticationInterceptor: HttpInterceptorFn = (req: HttpRequest<unknown>, next:\n * HttpHandlerFn) => {\n *    const userToken = 'MY_TOKEN'; const modifiedReq = req.clone({\n *      headers: req.headers.set('Authorization', `Bearer ${userToken}`),\n *    });\n *\n *    return next(modifiedReq);\n * };\n * ```\n */\nexport type HttpInterceptorFn = (\n  req: HttpRequest<unknown>,\n  next: HttpHandlerFn,\n) => Observable<HttpEvent<unknown>>;\n\n/**\n * Function which invokes an HTTP interceptor chain.\n *\n * Each interceptor in the interceptor chain is turned into a `ChainedInterceptorFn` which closes\n * over the rest of the chain (represented by another `ChainedInterceptorFn`). The last such\n * function in the chain will instead delegate to the `finalHandlerFn`, which is passed down when\n * the chain is invoked.\n *\n * This pattern allows for a chain of many interceptors to be composed and wrapped in a single\n * `HttpInterceptorFn`, which is a useful abstraction for including different kinds of interceptors\n * (e.g. legacy class-based interceptors) in the same chain.\n */\ntype ChainedInterceptorFn<RequestT> = (\n  req: HttpRequest<RequestT>,\n  finalHandlerFn: HttpHandlerFn,\n) => Observable<HttpEvent<RequestT>>;\n\nfunction interceptorChainEndFn(\n  req: HttpRequest<any>,\n  finalHandlerFn: HttpHandlerFn,\n): Observable<HttpEvent<any>> {\n  return finalHandlerFn(req);\n}\n\n/**\n * Constructs a `ChainedInterceptorFn` which adapts a legacy `HttpInterceptor` to the\n * `ChainedInterceptorFn` interface.\n */\nfunction adaptLegacyInterceptorToChain(\n  chainTailFn: ChainedInterceptorFn<any>,\n  interceptor: HttpInterceptor,\n): ChainedInterceptorFn<any> {\n  return (initialRequest, finalHandlerFn) =>\n    interceptor.intercept(initialRequest, {\n      handle: (downstreamRequest) => chainTailFn(downstreamRequest, finalHandlerFn),\n    });\n}\n\n/**\n * Constructs a `ChainedInterceptorFn` which wraps and invokes a functional interceptor in the given\n * injector.\n */\nfunction chainedInterceptorFn(\n  chainTailFn: ChainedInterceptorFn<unknown>,\n  interceptorFn: HttpInterceptorFn,\n  injector: EnvironmentInjector,\n): ChainedInterceptorFn<unknown> {\n  return (initialRequest, finalHandlerFn) =>\n    runInInjectionContext(injector, () =>\n      interceptorFn(initialRequest, (downstreamRequest) =>\n        chainTailFn(downstreamRequest, finalHandlerFn),\n      ),\n    );\n}\n\n/**\n * A multi-provider token that represents the array of registered\n * `HttpInterceptor` objects.\n *\n * @publicApi\n */\nexport const HTTP_INTERCEPTORS = new InjectionToken<readonly HttpInterceptor[]>(\n  ngDevMode ? 'HTTP_INTERCEPTORS' : '',\n);\n\n/**\n * A multi-provided token of `HttpInterceptorFn`s.\n */\nexport const HTTP_INTERCEPTOR_FNS = new InjectionToken<readonly HttpInterceptorFn[]>(\n  ngDevMode ? 'HTTP_INTERCEPTOR_FNS' : '',\n);\n\n/**\n * A multi-provided token of `HttpInterceptorFn`s that are only set in root.\n */\nexport const HTTP_ROOT_INTERCEPTOR_FNS = new InjectionToken<readonly HttpInterceptorFn[]>(\n  ngDevMode ? 'HTTP_ROOT_INTERCEPTOR_FNS' : '',\n);\n\n// TODO(atscott): We need a larger discussion about stability and what should contribute to stability.\n// Should the whole interceptor chain contribute to stability or just the backend request #55075?\n// Should HttpClient contribute to stability automatically at all?\nexport const REQUESTS_CONTRIBUTE_TO_STABILITY = new InjectionToken<boolean>(\n  ngDevMode ? 'REQUESTS_CONTRIBUTE_TO_STABILITY' : '',\n  {providedIn: 'root', factory: () => true},\n);\n\n/**\n * Creates an `HttpInterceptorFn` which lazily initializes an interceptor chain from the legacy\n * class-based interceptors and runs the request through it.\n */\nexport function legacyInterceptorFnFactory(): HttpInterceptorFn {\n  let chain: ChainedInterceptorFn<any> | null = null;\n\n  return (req, handler) => {\n    if (chain === null) {\n      const interceptors = inject(HTTP_INTERCEPTORS, {optional: true}) ?? [];\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `interceptors` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      chain = interceptors.reduceRight(\n        adaptLegacyInterceptorToChain,\n        interceptorChainEndFn as ChainedInterceptorFn<any>,\n      );\n    }\n\n    const pendingTasks = inject(PendingTasks);\n    const contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n    if (contributeToStability) {\n      const taskId = pendingTasks.add();\n      return chain(req, handler).pipe(finalize(() => pendingTasks.remove(taskId)));\n    } else {\n      return chain(req, handler);\n    }\n  };\n}\n\nlet fetchBackendWarningDisplayed = false;\n\n/** Internal function to reset the flag in tests */\nexport function resetFetchBackendWarningFlag() {\n  fetchBackendWarningDisplayed = false;\n}\n\n@Injectable()\nexport class HttpInterceptorHandler extends HttpHandler {\n  private chain: ChainedInterceptorFn<unknown> | null = null;\n  private readonly pendingTasks = inject(PendingTasks);\n  private readonly contributeToStability = inject(REQUESTS_CONTRIBUTE_TO_STABILITY);\n\n  constructor(\n    private backend: HttpBackend,\n    private injector: EnvironmentInjector,\n  ) {\n    super();\n\n    // We strongly recommend using fetch backend for HTTP calls when SSR is used\n    // for an application. The logic below checks if that's the case and produces\n    // a warning otherwise.\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !fetchBackendWarningDisplayed) {\n      const isServer = isPlatformServer(injector.get(PLATFORM_ID));\n      if (isServer && !(this.backend instanceof FetchBackend)) {\n        fetchBackendWarningDisplayed = true;\n        injector\n          .get(Console)\n          .warn(\n            formatRuntimeError(\n              RuntimeErrorCode.NOT_USING_FETCH_BACKEND_IN_SSR,\n              'Angular detected that `HttpClient` is not configured ' +\n                \"to use `fetch` APIs. It's strongly recommended to \" +\n                'enable `fetch` for applications that use Server-Side Rendering ' +\n                'for better performance and compatibility. ' +\n                'To enable `fetch`, add the `withFetch()` to the `provideHttpClient()` ' +\n                'call at the root of the application.',\n            ),\n          );\n      }\n    }\n  }\n\n  override handle(initialRequest: HttpRequest<any>): Observable<HttpEvent<any>> {\n    if (this.chain === null) {\n      const dedupedInterceptorFns = Array.from(\n        new Set([\n          ...this.injector.get(HTTP_INTERCEPTOR_FNS),\n          ...this.injector.get(HTTP_ROOT_INTERCEPTOR_FNS, []),\n        ]),\n      );\n\n      // Note: interceptors are wrapped right-to-left so that final execution order is\n      // left-to-right. That is, if `dedupedInterceptorFns` is the array `[a, b, c]`, we want to\n      // produce a chain that is conceptually `c(b(a(end)))`, which we build from the inside\n      // out.\n      this.chain = dedupedInterceptorFns.reduceRight(\n        (nextSequencedFn, interceptorFn) =>\n          chainedInterceptorFn(nextSequencedFn, interceptorFn, this.injector),\n        interceptorChainEndFn as ChainedInterceptorFn<unknown>,\n      );\n    }\n\n    if (this.contributeToStability) {\n      const taskId = this.pendingTasks.add();\n      return this.chain(initialRequest, (downstreamRequest) =>\n        this.backend.handle(downstreamRequest),\n      ).pipe(finalize(() => this.pendingTasks.remove(taskId)));\n    } else {\n      return this.chain(initialRequest, (downstreamRequest) =>\n        this.backend.handle(downstreamRequest),\n      );\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {\n  EnvironmentInjector,\n  Inject,\n  inject,\n  Injectable,\n  runInInjectionContext,\n} from '@angular/core';\nimport {Observable, Observer} from 'rxjs';\n\nimport {HttpBackend, HttpHandler} from './backend';\nimport {HttpHandlerFn} from './interceptor';\nimport {HttpRequest} from './request';\nimport {\n  HTTP_STATUS_CODE_OK,\n  HttpErrorResponse,\n  HttpEvent,\n  HttpEventType,\n  HttpResponse,\n} from './response';\n\n// Every request made through JSONP needs a callback name that's unique across the\n// whole page. Each request is assigned an id and the callback name is constructed\n// from that. The next id to be assigned is tracked in a global variable here that\n// is shared among all applications on the page.\nlet nextRequestId: number = 0;\n\n/**\n * When a pending <script> is unsubscribed we'll move it to this document, so it won't be\n * executed.\n */\nlet foreignDocument: Document | undefined;\n\n// Error text given when a JSONP script is injected, but doesn't invoke the callback\n// passed in its URL.\nexport const JSONP_ERR_NO_CALLBACK = 'JSONP injected script did not invoke callback.';\n\n// Error text given when a request is passed to the JsonpClientBackend that doesn't\n// have a request method JSONP.\nexport const JSONP_ERR_WRONG_METHOD = 'JSONP requests must use JSONP request method.';\nexport const JSONP_ERR_WRONG_RESPONSE_TYPE = 'JSONP requests must use Json response type.';\n\n// Error text given when a request is passed to the JsonpClientBackend that has\n// headers set\nexport const JSONP_ERR_HEADERS_NOT_SUPPORTED = 'JSONP requests do not support headers.';\n\n/**\n * DI token/abstract type representing a map of JSONP callbacks.\n *\n * In the browser, this should always be the `window` object.\n *\n *\n */\nexport abstract class JsonpCallbackContext {\n  [key: string]: (data: any) => void;\n}\n\n/**\n * Factory function that determines where to store JSONP callbacks.\n *\n * Ordinarily JSONP callbacks are stored on the `window` object, but this may not exist\n * in test environments. In that case, callbacks are stored on an anonymous object instead.\n *\n *\n */\nexport function jsonpCallbackContext(): Object {\n  if (typeof window === 'object') {\n    return window;\n  }\n  return {};\n}\n\n/**\n * Processes an `HttpRequest` with the JSONP method,\n * by performing JSONP style requests.\n * @see {@link HttpHandler}\n * @see {@link HttpXhrBackend}\n *\n * @publicApi\n */\n@Injectable()\nexport class JsonpClientBackend implements HttpBackend {\n  /**\n   * A resolved promise that can be used to schedule microtasks in the event handlers.\n   */\n  private readonly resolvedPromise = Promise.resolve();\n\n  constructor(\n    private callbackMap: JsonpCallbackContext,\n    @Inject(DOCUMENT) private document: any,\n  ) {}\n\n  /**\n   * Get the name of the next callback method, by incrementing the global `nextRequestId`.\n   */\n  private nextCallback(): string {\n    return `ng_jsonp_callback_${nextRequestId++}`;\n  }\n\n  /**\n   * Processes a JSONP request and returns an event stream of the results.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   *\n   */\n  handle(req: HttpRequest<never>): Observable<HttpEvent<any>> {\n    // Firstly, check both the method and response type. If either doesn't match\n    // then the request was improperly routed here and cannot be handled.\n    if (req.method !== 'JSONP') {\n      throw new Error(JSONP_ERR_WRONG_METHOD);\n    } else if (req.responseType !== 'json') {\n      throw new Error(JSONP_ERR_WRONG_RESPONSE_TYPE);\n    }\n\n    // Check the request headers. JSONP doesn't support headers and\n    // cannot set any that were supplied.\n    if (req.headers.keys().length > 0) {\n      throw new Error(JSONP_ERR_HEADERS_NOT_SUPPORTED);\n    }\n\n    // Everything else happens inside the Observable boundary.\n    return new Observable<HttpEvent<any>>((observer: Observer<HttpEvent<any>>) => {\n      // The first step to make a request is to generate the callback name, and replace the\n      // callback placeholder in the URL with the name. Care has to be taken here to ensure\n      // a trailing &, if matched, gets inserted back into the URL in the correct place.\n      const callback = this.nextCallback();\n      const url = req.urlWithParams.replace(/=JSONP_CALLBACK(&|$)/, `=${callback}$1`);\n\n      // Construct the <script> tag and point it at the URL.\n      const node = this.document.createElement('script');\n      node.src = url;\n\n      // A JSONP request requires waiting for multiple callbacks. These variables\n      // are closed over and track state across those callbacks.\n\n      // The response object, if one has been received, or null otherwise.\n      let body: any | null = null;\n\n      // Whether the response callback has been called.\n      let finished: boolean = false;\n\n      // Set the response callback in this.callbackMap (which will be the window\n      // object in the browser. The script being loaded via the <script> tag will\n      // eventually call this callback.\n      this.callbackMap[callback] = (data?: any) => {\n        // Data has been received from the JSONP script. Firstly, delete this callback.\n        delete this.callbackMap[callback];\n\n        // Set state to indicate data was received.\n        body = data;\n        finished = true;\n      };\n\n      // cleanup() is a utility closure that removes the <script> from the page and\n      // the response callback from the window. This logic is used in both the\n      // success, error, and cancellation paths, so it's extracted out for convenience.\n      const cleanup = () => {\n        node.removeEventListener('load', onLoad);\n        node.removeEventListener('error', onError);\n\n        // Remove the <script> tag if it's still on the page.\n        node.remove();\n\n        // Remove the response callback from the callbackMap (window object in the\n        // browser).\n        delete this.callbackMap[callback];\n      };\n\n      // onLoad() is the success callback which runs after the response callback\n      // if the JSONP script loads successfully. The event itself is unimportant.\n      // If something went wrong, onLoad() may run without the response callback\n      // having been invoked.\n      const onLoad = (event: Event) => {\n        // We wrap it in an extra Promise, to ensure the microtask\n        // is scheduled after the loaded endpoint has executed any potential microtask itself,\n        // which is not guaranteed in Internet Explorer and EdgeHTML. See issue #39496\n        this.resolvedPromise.then(() => {\n          // Cleanup the page.\n          cleanup();\n\n          // Check whether the response callback has run.\n          if (!finished) {\n            // It hasn't, something went wrong with the request. Return an error via\n            // the Observable error path. All JSONP errors have status 0.\n            observer.error(\n              new HttpErrorResponse({\n                url,\n                status: 0,\n                statusText: 'JSONP Error',\n                error: new Error(JSONP_ERR_NO_CALLBACK),\n              }),\n            );\n            return;\n          }\n\n          // Success. body either contains the response body or null if none was\n          // returned.\n          observer.next(\n            new HttpResponse({\n              body,\n              status: HTTP_STATUS_CODE_OK,\n              statusText: 'OK',\n              url,\n            }),\n          );\n\n          // Complete the stream, the response is over.\n          observer.complete();\n        });\n      };\n\n      // onError() is the error callback, which runs if the script returned generates\n      // a Javascript error. It emits the error via the Observable error channel as\n      // a HttpErrorResponse.\n      const onError: any = (error: Error) => {\n        cleanup();\n\n        // Wrap the error in a HttpErrorResponse.\n        observer.error(\n          new HttpErrorResponse({\n            error,\n            status: 0,\n            statusText: 'JSONP Error',\n            url,\n          }),\n        );\n      };\n\n      // Subscribe to both the success (load) and error events on the <script> tag,\n      // and add it to the page.\n      node.addEventListener('load', onLoad);\n      node.addEventListener('error', onError);\n      this.document.body.appendChild(node);\n\n      // The request has now been successfully sent.\n      observer.next({type: HttpEventType.Sent});\n\n      // Cancellation handler.\n      return () => {\n        if (!finished) {\n          this.removeListeners(node);\n        }\n\n        // And finally, clean up the page.\n        cleanup();\n      };\n    });\n  }\n\n  private removeListeners(script: HTMLScriptElement): void {\n    // Issue #34818\n    // Changing <script>'s ownerDocument will prevent it from execution.\n    // https://html.spec.whatwg.org/multipage/scripting.html#execute-the-script-block\n    foreignDocument ??= (this.document.implementation as DOMImplementation).createHTMLDocument();\n\n    foreignDocument.adoptNode(script);\n  }\n}\n\n/**\n * Identifies requests with the method JSONP and shifts them to the `JsonpClientBackend`.\n */\nexport function jsonpInterceptorFn(\n  req: HttpRequest<unknown>,\n  next: HttpHandlerFn,\n): Observable<HttpEvent<unknown>> {\n  if (req.method === 'JSONP') {\n    return inject(JsonpClientBackend).handle(req as HttpRequest<never>);\n  }\n\n  // Fall through for normal HTTP requests.\n  return next(req);\n}\n\n/**\n * Identifies requests with the method JSONP and\n * shifts them to the `JsonpClientBackend`.\n *\n * @see {@link HttpInterceptor}\n *\n * @publicApi\n */\n@Injectable()\nexport class JsonpInterceptor {\n  constructor(private injector: EnvironmentInjector) {}\n\n  /**\n   * Identifies and handles a given JSONP request.\n   * @param initialRequest The outgoing request object to handle.\n   * @param next The next interceptor in the chain, or the backend\n   * if no interceptors remain in the chain.\n   * @returns An observable of the event stream.\n   */\n  intercept(initialRequest: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    return runInInjectionContext(this.injector, () =>\n      jsonpInterceptorFn(initialRequest, (downstreamRequest) => next.handle(downstreamRequest)),\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {XhrFactory} from '@angular/common';\nimport {Injectable, ɵRuntimeError as RuntimeError} from '@angular/core';\nimport {from, Observable, Observer, of} from 'rxjs';\nimport {switchMap} from 'rxjs/operators';\n\nimport {HttpBackend} from './backend';\nimport {RuntimeErrorCode} from './errors';\nimport {HttpHeaders} from './headers';\nimport {HttpRequest} from './request';\nimport {\n  HTTP_STATUS_CODE_NO_CONTENT,\n  HTTP_STATUS_CODE_OK,\n  HttpDownloadProgressEvent,\n  HttpErrorResponse,\n  HttpEvent,\n  HttpEventType,\n  HttpHeaderResponse,\n  HttpJsonParseError,\n  HttpResponse,\n  HttpUploadProgressEvent,\n} from './response';\n\nconst XSSI_PREFIX = /^\\)\\]\\}',?\\n/;\n\n/**\n * Determine an appropriate URL for the response, by checking either\n * XMLHttpRequest.responseURL or the X-Request-URL header.\n */\nfunction getResponseUrl(xhr: any): string | null {\n  if ('responseURL' in xhr && xhr.responseURL) {\n    return xhr.responseURL;\n  }\n  if (/^X-Request-URL:/m.test(xhr.getAllResponseHeaders())) {\n    return xhr.getResponseHeader('X-Request-URL');\n  }\n  return null;\n}\n\n/**\n * Uses `XMLHttpRequest` to send requests to a backend server.\n * @see {@link HttpHandler}\n * @see {@link JsonpClientBackend}\n *\n * @publicApi\n */\n@Injectable()\nexport class HttpXhrBackend implements HttpBackend {\n  constructor(private xhrFactory: XhrFactory) {}\n\n  /**\n   * Processes a request and returns a stream of response events.\n   * @param req The request object.\n   * @returns An observable of the response events.\n   */\n  handle(req: HttpRequest<any>): Observable<HttpEvent<any>> {\n    // Quick check to give a better error message when a user attempts to use\n    // HttpClient.jsonp() without installing the HttpClientJsonpModule\n    if (req.method === 'JSONP') {\n      throw new RuntimeError(\n        RuntimeErrorCode.MISSING_JSONP_MODULE,\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n          `Cannot make a JSONP request without JSONP support. To fix the problem, either add the \\`withJsonpSupport()\\` call (if \\`provideHttpClient()\\` is used) or import the \\`HttpClientJsonpModule\\` in the root NgModule.`,\n      );\n    }\n\n    // Check whether this factory has a special function to load an XHR implementation\n    // for various non-browser environments. We currently limit it to only `ServerXhr`\n    // class, which needs to load an XHR implementation.\n    const xhrFactory: XhrFactory & {ɵloadImpl?: () => Promise<void>} = this.xhrFactory;\n    const source: Observable<void | null> = xhrFactory.ɵloadImpl\n      ? from(xhrFactory.ɵloadImpl())\n      : of(null);\n\n    return source.pipe(\n      switchMap(() => {\n        // Everything happens on Observable subscription.\n        return new Observable((observer: Observer<HttpEvent<any>>) => {\n          // Start by setting up the XHR object with request method, URL, and withCredentials\n          // flag.\n          const xhr = xhrFactory.build();\n          xhr.open(req.method, req.urlWithParams);\n          if (req.withCredentials) {\n            xhr.withCredentials = true;\n          }\n\n          // Add all the requested headers.\n          req.headers.forEach((name, values) => xhr.setRequestHeader(name, values.join(',')));\n\n          // Add an Accept header if one isn't present already.\n          if (!req.headers.has('Accept')) {\n            xhr.setRequestHeader('Accept', 'application/json, text/plain, */*');\n          }\n\n          // Auto-detect the Content-Type header if one isn't present already.\n          if (!req.headers.has('Content-Type')) {\n            const detectedType = req.detectContentTypeHeader();\n            // Sometimes Content-Type detection fails.\n            if (detectedType !== null) {\n              xhr.setRequestHeader('Content-Type', detectedType);\n            }\n          }\n\n          // Set the responseType if one was requested.\n          if (req.responseType) {\n            const responseType = req.responseType.toLowerCase();\n\n            // JSON responses need to be processed as text. This is because if the server\n            // returns an XSSI-prefixed JSON response, the browser will fail to parse it,\n            // xhr.response will be null, and xhr.responseText cannot be accessed to\n            // retrieve the prefixed JSON data in order to strip the prefix. Thus, all JSON\n            // is parsed by first requesting text and then applying JSON.parse.\n            xhr.responseType = (responseType !== 'json' ? responseType : 'text') as any;\n          }\n\n          // Serialize the request body if one is present. If not, this will be set to null.\n          const reqBody = req.serializeBody();\n\n          // If progress events are enabled, response headers will be delivered\n          // in two events - the HttpHeaderResponse event and the full HttpResponse\n          // event. However, since response headers don't change in between these\n          // two events, it doesn't make sense to parse them twice. So headerResponse\n          // caches the data extracted from the response whenever it's first parsed,\n          // to ensure parsing isn't duplicated.\n          let headerResponse: HttpHeaderResponse | null = null;\n\n          // partialFromXhr extracts the HttpHeaderResponse from the current XMLHttpRequest\n          // state, and memoizes it into headerResponse.\n          const partialFromXhr = (): HttpHeaderResponse => {\n            if (headerResponse !== null) {\n              return headerResponse;\n            }\n\n            const statusText = xhr.statusText || 'OK';\n\n            // Parse headers from XMLHttpRequest - this step is lazy.\n            const headers = new HttpHeaders(xhr.getAllResponseHeaders());\n\n            // Read the response URL from the XMLHttpResponse instance and fall back on the\n            // request URL.\n            const url = getResponseUrl(xhr) || req.url;\n\n            // Construct the HttpHeaderResponse and memoize it.\n            headerResponse = new HttpHeaderResponse({headers, status: xhr.status, statusText, url});\n            return headerResponse;\n          };\n\n          // Next, a few closures are defined for the various events which XMLHttpRequest can\n          // emit. This allows them to be unregistered as event listeners later.\n\n          // First up is the load event, which represents a response being fully available.\n          const onLoad = () => {\n            // Read response state from the memoized partial data.\n            let {headers, status, statusText, url} = partialFromXhr();\n\n            // The body will be read out if present.\n            let body: any | null = null;\n\n            if (status !== HTTP_STATUS_CODE_NO_CONTENT) {\n              // Use XMLHttpRequest.response if set, responseText otherwise.\n              body = typeof xhr.response === 'undefined' ? xhr.responseText : xhr.response;\n            }\n\n            // Normalize another potential bug (this one comes from CORS).\n            if (status === 0) {\n              status = !!body ? HTTP_STATUS_CODE_OK : 0;\n            }\n\n            // ok determines whether the response will be transmitted on the event or\n            // error channel. Unsuccessful status codes (not 2xx) will always be errors,\n            // but a successful status code can still result in an error if the user\n            // asked for JSON data and the body cannot be parsed as such.\n            let ok = status >= 200 && status < 300;\n\n            // Check whether the body needs to be parsed as JSON (in many cases the browser\n            // will have done that already).\n            if (req.responseType === 'json' && typeof body === 'string') {\n              // Save the original body, before attempting XSSI prefix stripping.\n              const originalBody = body;\n              body = body.replace(XSSI_PREFIX, '');\n              try {\n                // Attempt the parse. If it fails, a parse error should be delivered to the\n                // user.\n                body = body !== '' ? JSON.parse(body) : null;\n              } catch (error) {\n                // Since the JSON.parse failed, it's reasonable to assume this might not have\n                // been a JSON response. Restore the original body (including any XSSI prefix)\n                // to deliver a better error response.\n                body = originalBody;\n\n                // If this was an error request to begin with, leave it as a string, it\n                // probably just isn't JSON. Otherwise, deliver the parsing error to the user.\n                if (ok) {\n                  // Even though the response status was 2xx, this is still an error.\n                  ok = false;\n                  // The parse error contains the text of the body that failed to parse.\n                  body = {error, text: body} as HttpJsonParseError;\n                }\n              }\n            }\n\n            if (ok) {\n              // A successful response is delivered on the event stream.\n              observer.next(\n                new HttpResponse({\n                  body,\n                  headers,\n                  status,\n                  statusText,\n                  url: url || undefined,\n                }),\n              );\n              // The full body has been received and delivered, no further events\n              // are possible. This request is complete.\n              observer.complete();\n            } else {\n              // An unsuccessful request is delivered on the error channel.\n              observer.error(\n                new HttpErrorResponse({\n                  // The error in this case is the response body (error from the server).\n                  error: body,\n                  headers,\n                  status,\n                  statusText,\n                  url: url || undefined,\n                }),\n              );\n            }\n          };\n\n          // The onError callback is called when something goes wrong at the network level.\n          // Connection timeout, DNS error, offline, etc. These are actual errors, and are\n          // transmitted on the error channel.\n          const onError = (error: ProgressEvent) => {\n            const {url} = partialFromXhr();\n            const res = new HttpErrorResponse({\n              error,\n              status: xhr.status || 0,\n              statusText: xhr.statusText || 'Unknown Error',\n              url: url || undefined,\n            });\n            observer.error(res);\n          };\n\n          // The sentHeaders flag tracks whether the HttpResponseHeaders event\n          // has been sent on the stream. This is necessary to track if progress\n          // is enabled since the event will be sent on only the first download\n          // progress event.\n          let sentHeaders = false;\n\n          // The download progress event handler, which is only registered if\n          // progress events are enabled.\n          const onDownProgress = (event: ProgressEvent) => {\n            // Send the HttpResponseHeaders event if it hasn't been sent already.\n            if (!sentHeaders) {\n              observer.next(partialFromXhr());\n              sentHeaders = true;\n            }\n\n            // Start building the download progress event to deliver on the response\n            // event stream.\n            let progressEvent: HttpDownloadProgressEvent = {\n              type: HttpEventType.DownloadProgress,\n              loaded: event.loaded,\n            };\n\n            // Set the total number of bytes in the event if it's available.\n            if (event.lengthComputable) {\n              progressEvent.total = event.total;\n            }\n\n            // If the request was for text content and a partial response is\n            // available on XMLHttpRequest, include it in the progress event\n            // to allow for streaming reads.\n            if (req.responseType === 'text' && !!xhr.responseText) {\n              progressEvent.partialText = xhr.responseText;\n            }\n\n            // Finally, fire the event.\n            observer.next(progressEvent);\n          };\n\n          // The upload progress event handler, which is only registered if\n          // progress events are enabled.\n          const onUpProgress = (event: ProgressEvent) => {\n            // Upload progress events are simpler. Begin building the progress\n            // event.\n            let progress: HttpUploadProgressEvent = {\n              type: HttpEventType.UploadProgress,\n              loaded: event.loaded,\n            };\n\n            // If the total number of bytes being uploaded is available, include\n            // it.\n            if (event.lengthComputable) {\n              progress.total = event.total;\n            }\n\n            // Send the event.\n            observer.next(progress);\n          };\n\n          // By default, register for load and error events.\n          xhr.addEventListener('load', onLoad);\n          xhr.addEventListener('error', onError);\n          xhr.addEventListener('timeout', onError);\n          xhr.addEventListener('abort', onError);\n\n          // Progress events are only enabled if requested.\n          if (req.reportProgress) {\n            // Download progress is always enabled if requested.\n            xhr.addEventListener('progress', onDownProgress);\n\n            // Upload progress depends on whether there is a body to upload.\n            if (reqBody !== null && xhr.upload) {\n              xhr.upload.addEventListener('progress', onUpProgress);\n            }\n          }\n\n          // Fire the request, and notify the event stream that it was fired.\n          xhr.send(reqBody!);\n          observer.next({type: HttpEventType.Sent});\n          // This is the return from the Observable function, which is the\n          // request cancellation handler.\n          return () => {\n            // On a cancellation, remove all registered event listeners.\n            xhr.removeEventListener('error', onError);\n            xhr.removeEventListener('abort', onError);\n            xhr.removeEventListener('load', onLoad);\n            xhr.removeEventListener('timeout', onError);\n\n            if (req.reportProgress) {\n              xhr.removeEventListener('progress', onDownProgress);\n              if (reqBody !== null && xhr.upload) {\n                xhr.upload.removeEventListener('progress', onUpProgress);\n              }\n            }\n\n            // Finally, abort the in-flight request.\n            if (xhr.readyState !== xhr.DONE) {\n              xhr.abort();\n            }\n          };\n        });\n      }),\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT, ɵparseCookieValue as parseCookieValue} from '@angular/common';\nimport {\n  EnvironmentInjector,\n  Inject,\n  inject,\n  Injectable,\n  InjectionToken,\n  PLATFORM_ID,\n  runInInjectionContext,\n} from '@angular/core';\nimport {Observable} from 'rxjs';\n\nimport {HttpHandler} from './backend';\nimport {HttpHandlerFn, HttpInterceptor} from './interceptor';\nimport {HttpRequest} from './request';\nimport {HttpEvent} from './response';\n\nexport const XSRF_ENABLED = new InjectionToken<boolean>(ngDevMode ? 'XSRF_ENABLED' : '');\n\nexport const XSRF_DEFAULT_COOKIE_NAME = 'XSRF-TOKEN';\nexport const XSRF_COOKIE_NAME = new InjectionToken<string>(ngDevMode ? 'XSRF_COOKIE_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_COOKIE_NAME,\n});\n\nexport const XSRF_DEFAULT_HEADER_NAME = 'X-XSRF-TOKEN';\nexport const XSRF_HEADER_NAME = new InjectionToken<string>(ngDevMode ? 'XSRF_HEADER_NAME' : '', {\n  providedIn: 'root',\n  factory: () => XSRF_DEFAULT_HEADER_NAME,\n});\n\n/**\n * Retrieves the current XSRF token to use with the next outgoing request.\n *\n * @publicApi\n */\nexport abstract class HttpXsrfTokenExtractor {\n  /**\n   * Get the XSRF token to use with an outgoing request.\n   *\n   * Will be called for every request, so the token may change between requests.\n   */\n  abstract getToken(): string | null;\n}\n\n/**\n * `HttpXsrfTokenExtractor` which retrieves the token from a cookie.\n */\n@Injectable()\nexport class HttpXsrfCookieExtractor implements HttpXsrfTokenExtractor {\n  private lastCookieString: string = '';\n  private lastToken: string | null = null;\n\n  /**\n   * @internal for testing\n   */\n  parseCount: number = 0;\n\n  constructor(\n    @Inject(DOCUMENT) private doc: any,\n    @Inject(PLATFORM_ID) private platform: string,\n    @Inject(XSRF_COOKIE_NAME) private cookieName: string,\n  ) {}\n\n  getToken(): string | null {\n    if (this.platform === 'server') {\n      return null;\n    }\n    const cookieString = this.doc.cookie || '';\n    if (cookieString !== this.lastCookieString) {\n      this.parseCount++;\n      this.lastToken = parseCookieValue(cookieString, this.cookieName);\n      this.lastCookieString = cookieString;\n    }\n    return this.lastToken;\n  }\n}\n\nexport function xsrfInterceptorFn(\n  req: HttpRequest<unknown>,\n  next: HttpHandlerFn,\n): Observable<HttpEvent<unknown>> {\n  const lcUrl = req.url.toLowerCase();\n  // Skip both non-mutating requests and absolute URLs.\n  // Non-mutating requests don't require a token, and absolute URLs require special handling\n  // anyway as the cookie set\n  // on our origin is not the same as the token expected by another origin.\n  if (\n    !inject(XSRF_ENABLED) ||\n    req.method === 'GET' ||\n    req.method === 'HEAD' ||\n    lcUrl.startsWith('http://') ||\n    lcUrl.startsWith('https://')\n  ) {\n    return next(req);\n  }\n\n  const token = inject(HttpXsrfTokenExtractor).getToken();\n  const headerName = inject(XSRF_HEADER_NAME);\n\n  // Be careful not to overwrite an existing header of the same name.\n  if (token != null && !req.headers.has(headerName)) {\n    req = req.clone({headers: req.headers.set(headerName, token)});\n  }\n  return next(req);\n}\n\n/**\n * `HttpInterceptor` which adds an XSRF token to eligible outgoing requests.\n */\n@Injectable()\nexport class HttpXsrfInterceptor implements HttpInterceptor {\n  constructor(private injector: EnvironmentInjector) {}\n\n  intercept(initialRequest: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {\n    return runInInjectionContext(this.injector, () =>\n      xsrfInterceptorFn(initialRequest, (downstreamRequest) => next.handle(downstreamRequest)),\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  EnvironmentProviders,\n  inject,\n  InjectionToken,\n  makeEnvironmentProviders,\n  Provider,\n} from '@angular/core';\n\nimport {HttpBackend, HttpHandler} from './backend';\nimport {HttpClient} from './client';\nimport {FetchBackend} from './fetch';\nimport {\n  HTTP_INTERCEPTOR_FNS,\n  HttpInterceptorFn,\n  HttpInterceptorHandler,\n  legacyInterceptorFnFactory,\n} from './interceptor';\nimport {\n  jsonpCallbackContext,\n  JsonpCallbackContext,\n  JsonpClientBackend,\n  jsonpInterceptorFn,\n} from './jsonp';\nimport {HttpXhrBackend} from './xhr';\nimport {\n  HttpXsrfCookieExtractor,\n  HttpXsrfTokenExtractor,\n  XSRF_COOKIE_NAME,\n  XSRF_ENABLED,\n  XSRF_HEADER_NAME,\n  xsrfInterceptorFn,\n} from './xsrf';\n\n/**\n * Identifies a particular kind of `HttpFeature`.\n *\n * @publicApi\n */\nexport enum HttpFeatureKind {\n  Interceptors,\n  LegacyInterceptors,\n  CustomXsrfConfiguration,\n  NoXsrfProtection,\n  JsonpSupport,\n  RequestsMadeViaParent,\n  Fetch,\n}\n\n/**\n * A feature for use when configuring `provideHttpClient`.\n *\n * @publicApi\n */\nexport interface HttpFeature<KindT extends HttpFeatureKind> {\n  ɵkind: KindT;\n  ɵproviders: Provider[];\n}\n\nfunction makeHttpFeature<KindT extends HttpFeatureKind>(\n  kind: KindT,\n  providers: Provider[],\n): HttpFeature<KindT> {\n  return {\n    ɵkind: kind,\n    ɵproviders: providers,\n  };\n}\n\n/**\n * Configures Angular's `HttpClient` service to be available for injection.\n *\n * By default, `HttpClient` will be configured for injection with its default options for XSRF\n * protection of outgoing requests. Additional configuration options can be provided by passing\n * feature functions to `provideHttpClient`. For example, HTTP interceptors can be added using the\n * `withInterceptors(...)` feature.\n *\n * <div class=\"alert is-helpful\">\n *\n * It's strongly recommended to enable\n * [`fetch`](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API) for applications that use\n * Server-Side Rendering for better performance and compatibility. To enable `fetch`, add\n * `withFetch()` feature to the `provideHttpClient()` call at the root of the application:\n *\n * ```\n * provideHttpClient(withFetch());\n * ```\n *\n * </div>\n *\n * @see {@link withInterceptors}\n * @see {@link withInterceptorsFromDi}\n * @see {@link withXsrfConfiguration}\n * @see {@link withNoXsrfProtection}\n * @see {@link withJsonpSupport}\n * @see {@link withRequestsMadeViaParent}\n * @see {@link withFetch}\n */\nexport function provideHttpClient(\n  ...features: HttpFeature<HttpFeatureKind>[]\n): EnvironmentProviders {\n  if (ngDevMode) {\n    const featureKinds = new Set(features.map((f) => f.ɵkind));\n    if (\n      featureKinds.has(HttpFeatureKind.NoXsrfProtection) &&\n      featureKinds.has(HttpFeatureKind.CustomXsrfConfiguration)\n    ) {\n      throw new Error(\n        ngDevMode\n          ? `Configuration error: found both withXsrfConfiguration() and withNoXsrfProtection() in the same call to provideHttpClient(), which is a contradiction.`\n          : '',\n      );\n    }\n  }\n\n  const providers: Provider[] = [\n    HttpClient,\n    HttpXhrBackend,\n    HttpInterceptorHandler,\n    {provide: HttpHandler, useExisting: HttpInterceptorHandler},\n    {\n      provide: HttpBackend,\n      useFactory: () => {\n        return inject(FetchBackend, {optional: true}) ?? inject(HttpXhrBackend);\n      },\n    },\n    {\n      provide: HTTP_INTERCEPTOR_FNS,\n      useValue: xsrfInterceptorFn,\n      multi: true,\n    },\n    {provide: XSRF_ENABLED, useValue: true},\n    {provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor},\n  ];\n\n  for (const feature of features) {\n    providers.push(...feature.ɵproviders);\n  }\n\n  return makeEnvironmentProviders(providers);\n}\n\n/**\n * Adds one or more functional-style HTTP interceptors to the configuration of the `HttpClient`\n * instance.\n *\n * @see {@link HttpInterceptorFn}\n * @see {@link provideHttpClient}\n * @publicApi\n */\nexport function withInterceptors(\n  interceptorFns: HttpInterceptorFn[],\n): HttpFeature<HttpFeatureKind.Interceptors> {\n  return makeHttpFeature(\n    HttpFeatureKind.Interceptors,\n    interceptorFns.map((interceptorFn) => {\n      return {\n        provide: HTTP_INTERCEPTOR_FNS,\n        useValue: interceptorFn,\n        multi: true,\n      };\n    }),\n  );\n}\n\nconst LEGACY_INTERCEPTOR_FN = new InjectionToken<HttpInterceptorFn>(\n  ngDevMode ? 'LEGACY_INTERCEPTOR_FN' : '',\n);\n\n/**\n * Includes class-based interceptors configured using a multi-provider in the current injector into\n * the configured `HttpClient` instance.\n *\n * Prefer `withInterceptors` and functional interceptors instead, as support for DI-provided\n * interceptors may be phased out in a later release.\n *\n * @see {@link HttpInterceptor}\n * @see {@link HTTP_INTERCEPTORS}\n * @see {@link provideHttpClient}\n */\nexport function withInterceptorsFromDi(): HttpFeature<HttpFeatureKind.LegacyInterceptors> {\n  // Note: the legacy interceptor function is provided here via an intermediate token\n  // (`LEGACY_INTERCEPTOR_FN`), using a pattern which guarantees that if these providers are\n  // included multiple times, all of the multi-provider entries will have the same instance of the\n  // interceptor function. That way, the `HttpINterceptorHandler` will dedup them and legacy\n  // interceptors will not run multiple times.\n  return makeHttpFeature(HttpFeatureKind.LegacyInterceptors, [\n    {\n      provide: LEGACY_INTERCEPTOR_FN,\n      useFactory: legacyInterceptorFnFactory,\n    },\n    {\n      provide: HTTP_INTERCEPTOR_FNS,\n      useExisting: LEGACY_INTERCEPTOR_FN,\n      multi: true,\n    },\n  ]);\n}\n\n/**\n * Customizes the XSRF protection for the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withNoXsrfProtection` feature.\n *\n * @see {@link provideHttpClient}\n */\nexport function withXsrfConfiguration({\n  cookieName,\n  headerName,\n}: {\n  cookieName?: string;\n  headerName?: string;\n}): HttpFeature<HttpFeatureKind.CustomXsrfConfiguration> {\n  const providers: Provider[] = [];\n  if (cookieName !== undefined) {\n    providers.push({provide: XSRF_COOKIE_NAME, useValue: cookieName});\n  }\n  if (headerName !== undefined) {\n    providers.push({provide: XSRF_HEADER_NAME, useValue: headerName});\n  }\n\n  return makeHttpFeature(HttpFeatureKind.CustomXsrfConfiguration, providers);\n}\n\n/**\n * Disables XSRF protection in the configuration of the current `HttpClient` instance.\n *\n * This feature is incompatible with the `withXsrfConfiguration` feature.\n *\n * @see {@link provideHttpClient}\n */\nexport function withNoXsrfProtection(): HttpFeature<HttpFeatureKind.NoXsrfProtection> {\n  return makeHttpFeature(HttpFeatureKind.NoXsrfProtection, [\n    {\n      provide: XSRF_ENABLED,\n      useValue: false,\n    },\n  ]);\n}\n\n/**\n * Add JSONP support to the configuration of the current `HttpClient` instance.\n *\n * @see {@link provideHttpClient}\n */\nexport function withJsonpSupport(): HttpFeature<HttpFeatureKind.JsonpSupport> {\n  return makeHttpFeature(HttpFeatureKind.JsonpSupport, [\n    JsonpClientBackend,\n    {provide: JsonpCallbackContext, useFactory: jsonpCallbackContext},\n    {provide: HTTP_INTERCEPTOR_FNS, useValue: jsonpInterceptorFn, multi: true},\n  ]);\n}\n\n/**\n * Configures the current `HttpClient` instance to make requests via the parent injector's\n * `HttpClient` instead of directly.\n *\n * By default, `provideHttpClient` configures `HttpClient` in its injector to be an independent\n * instance. For example, even if `HttpClient` is configured in the parent injector with\n * one or more interceptors, they will not intercept requests made via this instance.\n *\n * With this option enabled, once the request has passed through the current injector's\n * interceptors, it will be delegated to the parent injector's `HttpClient` chain instead of\n * dispatched directly, and interceptors in the parent configuration will be applied to the request.\n *\n * If there are several `HttpClient` instances in the injector hierarchy, it's possible for\n * `withRequestsMadeViaParent` to be used at multiple levels, which will cause the request to\n * \"bubble up\" until either reaching the root level or an `HttpClient` which was not configured with\n * this option.\n *\n * @see {@link provideHttpClient}\n * @developerPreview\n */\nexport function withRequestsMadeViaParent(): HttpFeature<HttpFeatureKind.RequestsMadeViaParent> {\n  return makeHttpFeature(HttpFeatureKind.RequestsMadeViaParent, [\n    {\n      provide: HttpBackend,\n      useFactory: () => {\n        const handlerFromParent = inject(HttpHandler, {skipSelf: true, optional: true});\n        if (ngDevMode && handlerFromParent === null) {\n          throw new Error(\n            'withRequestsMadeViaParent() can only be used when the parent injector also configures HttpClient',\n          );\n        }\n        return handlerFromParent;\n      },\n    },\n  ]);\n}\n\n/**\n * Configures the current `HttpClient` instance to make requests using the fetch API.\n *\n * Note: The Fetch API doesn't support progress report on uploads.\n *\n * @publicApi\n */\nexport function withFetch(): HttpFeature<HttpFeatureKind.Fetch> {\n  return makeHttpFeature(HttpFeatureKind.Fetch, [\n    FetchBackend,\n    {provide: HttpBackend, useExisting: FetchBackend},\n  ]);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ModuleWithProviders, NgModule} from '@angular/core';\n\nimport {HTTP_INTERCEPTORS} from './interceptor';\nimport {\n  provideHttpClient,\n  withInterceptorsFromDi,\n  withJsonpSupport,\n  withNoXsrfProtection,\n  withXsrfConfiguration,\n} from './provider';\nimport {\n  HttpXsrfCookieExtractor,\n  HttpXsrfInterceptor,\n  HttpXsrfTokenExtractor,\n  XSRF_DEFAULT_COOKIE_NAME,\n  XSRF_DEFAULT_HEADER_NAME,\n  XSRF_ENABLED,\n} from './xsrf';\n\n/**\n * Configures XSRF protection support for outgoing requests.\n *\n * For a server that supports a cookie-based XSRF protection system,\n * use directly to configure XSRF protection with the correct\n * cookie and header names.\n *\n * If no names are supplied, the default cookie name is `XSRF-TOKEN`\n * and the default header name is `X-XSRF-TOKEN`.\n *\n * @publicApi\n * @deprecated Use withXsrfConfiguration({cookieName: 'XSRF-TOKEN', headerName: 'X-XSRF-TOKEN'}) as\n *     providers instead or `withNoXsrfProtection` if you want to disabled XSRF protection.\n */\n@NgModule({\n  providers: [\n    HttpXsrfInterceptor,\n    {provide: HTTP_INTERCEPTORS, useExisting: HttpXsrfInterceptor, multi: true},\n    {provide: HttpXsrfTokenExtractor, useClass: HttpXsrfCookieExtractor},\n    withXsrfConfiguration({\n      cookieName: XSRF_DEFAULT_COOKIE_NAME,\n      headerName: XSRF_DEFAULT_HEADER_NAME,\n    }).ɵproviders,\n    {provide: XSRF_ENABLED, useValue: true},\n  ],\n})\nexport class HttpClientXsrfModule {\n  /**\n   * Disable the default XSRF protection.\n   */\n  static disable(): ModuleWithProviders<HttpClientXsrfModule> {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: [withNoXsrfProtection().ɵproviders],\n    };\n  }\n\n  /**\n   * Configure XSRF protection.\n   * @param options An object that can specify either or both\n   * cookie name or header name.\n   * - Cookie name default is `XSRF-TOKEN`.\n   * - Header name default is `X-XSRF-TOKEN`.\n   *\n   */\n  static withOptions(\n    options: {\n      cookieName?: string;\n      headerName?: string;\n    } = {},\n  ): ModuleWithProviders<HttpClientXsrfModule> {\n    return {\n      ngModule: HttpClientXsrfModule,\n      providers: withXsrfConfiguration(options).ɵproviders,\n    };\n  }\n}\n\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for XSRF. Automatically imported by `HttpClientModule`.\n *\n * You can add interceptors to the chain behind `HttpClient` by binding them to the\n * multiprovider for built-in DI token `HTTP_INTERCEPTORS`.\n *\n * @publicApi\n * @deprecated use `provideHttpClient(withInterceptorsFromDi())` as providers instead\n */\n@NgModule({\n  /**\n   * Configures the dependency injector where it is imported\n   * with supporting services for HTTP communications.\n   */\n  providers: [provideHttpClient(withInterceptorsFromDi())],\n})\nexport class HttpClientModule {}\n\n/**\n * Configures the dependency injector for `HttpClient`\n * with supporting services for JSONP.\n * Without this module, Jsonp requests reach the backend\n * with method JSONP, where they are rejected.\n *\n * @publicApi\n * @deprecated `withJsonpSupport()` as providers instead\n */\n@NgModule({\n  providers: [withJsonpSupport().ɵproviders],\n})\nexport class HttpClientJsonpModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  APP_BOOTSTRAP_LISTENER,\n  ApplicationRef,\n  inject,\n  InjectionToken,\n  makeStateKey,\n  PLATFORM_ID,\n  Provider,\n  StateKey,\n  TransferState,\n  ɵformatRuntimeError as formatRuntimeError,\n  ɵperformanceMarkFeature as performanceMarkFeature,\n  ɵtruncateMiddle as truncateMiddle,\n  ɵwhenStable as whenStable,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\nimport {isPlatformServer} from '@angular/common';\nimport {Observable, of} from 'rxjs';\nimport {tap} from 'rxjs/operators';\n\nimport {RuntimeErrorCode} from './errors';\nimport {HttpHeaders} from './headers';\nimport {HTTP_ROOT_INTERCEPTOR_FNS, HttpHandlerFn} from './interceptor';\nimport {HttpRequest} from './request';\nimport {HttpEvent, HttpResponse} from './response';\nimport {HttpParams} from './params';\n\n/**\n * Options to configure how TransferCache should be used to cache requests made via HttpClient.\n *\n * @param includeHeaders Specifies which headers should be included into cached responses. No\n *     headers are included by default.\n * @param filter A function that receives a request as an argument and returns a boolean to indicate\n *     whether a request should be included into the cache.\n * @param includePostRequests Enables caching for POST requests. By default, only GET and HEAD\n *     requests are cached. This option can be enabled if POST requests are used to retrieve data\n *     (for example using GraphQL).\n * @param includeRequestsWithAuthHeaders Enables caching of requests containing either `Authorization`\n *     or `Proxy-Authorization` headers. By default, these requests are excluded from caching.\n *\n * @publicApi\n */\nexport type HttpTransferCacheOptions = {\n  includeHeaders?: string[];\n  filter?: (req: HttpRequest<unknown>) => boolean;\n  includePostRequests?: boolean;\n  includeRequestsWithAuthHeaders?: boolean;\n};\n\n/**\n * If your application uses different HTTP origins to make API calls (via `HttpClient`) on the server and\n * on the client, the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token allows you to establish a mapping\n * between those origins, so that `HttpTransferCache` feature can recognize those requests as the same\n * ones and reuse the data cached on the server during hydration on the client.\n *\n * **Important note**: the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token should *only* be provided in\n * the *server* code of your application (typically in the `app.server.config.ts` script). Angular throws an\n * error if it detects that the token is defined while running on the client.\n *\n * @usageNotes\n *\n * When the same API endpoint is accessed via `http://internal-domain.com:8080` on the server and\n * via `https://external-domain.com` on the client, you can use the following configuration:\n * ```typescript\n * // in app.server.config.ts\n * {\n *     provide: HTTP_TRANSFER_CACHE_ORIGIN_MAP,\n *     useValue: {\n *         'http://internal-domain.com:8080': 'https://external-domain.com'\n *     }\n * }\n * ```\n *\n * @publicApi\n */\nexport const HTTP_TRANSFER_CACHE_ORIGIN_MAP = new InjectionToken<Record<string, string>>(\n  ngDevMode ? 'HTTP_TRANSFER_CACHE_ORIGIN_MAP' : '',\n);\n\n/**\n * Keys within cached response data structure.\n */\n\nexport const BODY = 'b';\nexport const HEADERS = 'h';\nexport const STATUS = 's';\nexport const STATUS_TEXT = 'st';\nexport const REQ_URL = 'u';\nexport const RESPONSE_TYPE = 'rt';\n\ninterface TransferHttpResponse {\n  /** body */\n  [BODY]: any;\n  /** headers */\n  [HEADERS]: Record<string, string[]>;\n  /** status */\n  [STATUS]?: number;\n  /** statusText */\n  [STATUS_TEXT]?: string;\n  /** url */\n  [REQ_URL]?: string;\n  /** responseType */\n  [RESPONSE_TYPE]?: HttpRequest<unknown>['responseType'];\n}\n\ninterface CacheOptions extends HttpTransferCacheOptions {\n  isCacheActive: boolean;\n}\n\nconst CACHE_OPTIONS = new InjectionToken<CacheOptions>(\n  ngDevMode ? 'HTTP_TRANSFER_STATE_CACHE_OPTIONS' : '',\n);\n\n/**\n * A list of allowed HTTP methods to cache.\n */\nconst ALLOWED_METHODS = ['GET', 'HEAD'];\n\nexport function transferCacheInterceptorFn(\n  req: HttpRequest<unknown>,\n  next: HttpHandlerFn,\n): Observable<HttpEvent<unknown>> {\n  const {isCacheActive, ...globalOptions} = inject(CACHE_OPTIONS);\n  const {transferCache: requestOptions, method: requestMethod} = req;\n\n  // In the following situations we do not want to cache the request\n  if (\n    !isCacheActive ||\n    requestOptions === false ||\n    // POST requests are allowed either globally or at request level\n    (requestMethod === 'POST' && !globalOptions.includePostRequests && !requestOptions) ||\n    (requestMethod !== 'POST' && !ALLOWED_METHODS.includes(requestMethod)) ||\n    // Do not cache request that require authorization when includeRequestsWithAuthHeaders is falsey\n    (!globalOptions.includeRequestsWithAuthHeaders && hasAuthHeaders(req)) ||\n    globalOptions.filter?.(req) === false\n  ) {\n    return next(req);\n  }\n\n  const transferState = inject(TransferState);\n\n  const originMap: Record<string, string> | null = inject(HTTP_TRANSFER_CACHE_ORIGIN_MAP, {\n    optional: true,\n  });\n  const isServer = isPlatformServer(inject(PLATFORM_ID));\n  if (originMap && !isServer) {\n    throw new RuntimeError(\n      RuntimeErrorCode.HTTP_ORIGIN_MAP_USED_IN_CLIENT,\n      ngDevMode &&\n        'Angular detected that the `HTTP_TRANSFER_CACHE_ORIGIN_MAP` token is configured and ' +\n          'present in the client side code. Please ensure that this token is only provided in the ' +\n          'server code of the application.',\n    );\n  }\n\n  const requestUrl = isServer && originMap ? mapRequestOriginUrl(req.url, originMap) : req.url;\n\n  const storeKey = makeCacheKey(req, requestUrl);\n  const response = transferState.get(storeKey, null);\n\n  let headersToInclude = globalOptions.includeHeaders;\n  if (typeof requestOptions === 'object' && requestOptions.includeHeaders) {\n    // Request-specific config takes precedence over the global config.\n    headersToInclude = requestOptions.includeHeaders;\n  }\n\n  if (response) {\n    const {\n      [BODY]: undecodedBody,\n      [RESPONSE_TYPE]: responseType,\n      [HEADERS]: httpHeaders,\n      [STATUS]: status,\n      [STATUS_TEXT]: statusText,\n      [REQ_URL]: url,\n    } = response;\n    // Request found in cache. Respond using it.\n    let body: ArrayBuffer | Blob | string | undefined = undecodedBody;\n\n    switch (responseType) {\n      case 'arraybuffer':\n        body = new TextEncoder().encode(undecodedBody).buffer;\n        break;\n      case 'blob':\n        body = new Blob([undecodedBody]);\n        break;\n    }\n\n    // We want to warn users accessing a header provided from the cache\n    // That HttpTransferCache alters the headers\n    // The warning will be logged a single time by HttpHeaders instance\n    let headers = new HttpHeaders(httpHeaders);\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Append extra logic in dev mode to produce a warning when a header\n      // that was not transferred to the client is accessed in the code via `get`\n      // and `has` calls.\n      headers = appendMissingHeadersDetection(req.url, headers, headersToInclude ?? []);\n    }\n\n    return of(\n      new HttpResponse({\n        body,\n        headers,\n        status,\n        statusText,\n        url,\n      }),\n    );\n  }\n\n  // Request not found in cache. Make the request and cache it if on the server.\n  return next(req).pipe(\n    tap((event: HttpEvent<unknown>) => {\n      if (event instanceof HttpResponse && isServer) {\n        transferState.set<TransferHttpResponse>(storeKey, {\n          [BODY]: event.body,\n          [HEADERS]: getFilteredHeaders(event.headers, headersToInclude),\n          [STATUS]: event.status,\n          [STATUS_TEXT]: event.statusText,\n          [REQ_URL]: requestUrl,\n          [RESPONSE_TYPE]: req.responseType,\n        });\n      }\n    }),\n  );\n}\n\n/** @returns true when the requests contains autorization related headers. */\nfunction hasAuthHeaders(req: HttpRequest<unknown>): boolean {\n  return req.headers.has('authorization') || req.headers.has('proxy-authorization');\n}\n\nfunction getFilteredHeaders(\n  headers: HttpHeaders,\n  includeHeaders: string[] | undefined,\n): Record<string, string[]> {\n  if (!includeHeaders) {\n    return {};\n  }\n\n  const headersMap: Record<string, string[]> = {};\n  for (const key of includeHeaders) {\n    const values = headers.getAll(key);\n    if (values !== null) {\n      headersMap[key] = values;\n    }\n  }\n\n  return headersMap;\n}\n\nfunction sortAndConcatParams(params: HttpParams | URLSearchParams): string {\n  return [...params.keys()]\n    .sort()\n    .map((k) => `${k}=${params.getAll(k)}`)\n    .join('&');\n}\n\nfunction makeCacheKey(\n  request: HttpRequest<any>,\n  mappedRequestUrl: string,\n): StateKey<TransferHttpResponse> {\n  // make the params encoded same as a url so it's easy to identify\n  const {params, method, responseType} = request;\n  const encodedParams = sortAndConcatParams(params);\n\n  let serializedBody = request.serializeBody();\n  if (serializedBody instanceof URLSearchParams) {\n    serializedBody = sortAndConcatParams(serializedBody);\n  } else if (typeof serializedBody !== 'string') {\n    serializedBody = '';\n  }\n\n  const key = [method, responseType, mappedRequestUrl, serializedBody, encodedParams].join('|');\n  const hash = generateHash(key);\n\n  return makeStateKey(hash);\n}\n\n/**\n * A method that returns a hash representation of a string using a variant of DJB2 hash\n * algorithm.\n *\n * This is the same hashing logic that is used to generate component ids.\n */\nfunction generateHash(value: string): string {\n  let hash = 0;\n\n  for (const char of value) {\n    hash = (Math.imul(31, hash) + char.charCodeAt(0)) << 0;\n  }\n\n  // Force positive number hash.\n  // ********** = equivalent of Integer.MAX_VALUE.\n  hash += ********** + 1;\n\n  return hash.toString();\n}\n\n/**\n * Returns the DI providers needed to enable HTTP transfer cache.\n *\n * By default, when using server rendering, requests are performed twice: once on the server and\n * other one on the browser.\n *\n * When these providers are added, requests performed on the server are cached and reused during the\n * bootstrapping of the application in the browser thus avoiding duplicate requests and reducing\n * load time.\n *\n */\nexport function withHttpTransferCache(cacheOptions: HttpTransferCacheOptions): Provider[] {\n  return [\n    {\n      provide: CACHE_OPTIONS,\n      useFactory: (): CacheOptions => {\n        performanceMarkFeature('NgHttpTransferCache');\n        return {isCacheActive: true, ...cacheOptions};\n      },\n    },\n    {\n      provide: HTTP_ROOT_INTERCEPTOR_FNS,\n      useValue: transferCacheInterceptorFn,\n      multi: true,\n      deps: [TransferState, CACHE_OPTIONS],\n    },\n    {\n      provide: APP_BOOTSTRAP_LISTENER,\n      multi: true,\n      useFactory: () => {\n        const appRef = inject(ApplicationRef);\n        const cacheState = inject(CACHE_OPTIONS);\n\n        return () => {\n          whenStable(appRef).then(() => {\n            cacheState.isCacheActive = false;\n          });\n        };\n      },\n    },\n  ];\n}\n\n/**\n * This function will add a proxy to an HttpHeader to intercept calls to get/has\n * and log a warning if the header entry requested has been removed\n */\nfunction appendMissingHeadersDetection(\n  url: string,\n  headers: HttpHeaders,\n  headersToInclude: string[],\n): HttpHeaders {\n  const warningProduced = new Set();\n  return new Proxy<HttpHeaders>(headers, {\n    get(target: HttpHeaders, prop: keyof HttpHeaders): unknown {\n      const value = Reflect.get(target, prop);\n      const methods: Set<keyof HttpHeaders> = new Set(['get', 'has', 'getAll']);\n\n      if (typeof value !== 'function' || !methods.has(prop)) {\n        return value;\n      }\n\n      return (headerName: string) => {\n        // We log when the key has been removed and a warning hasn't been produced for the header\n        const key = (prop + ':' + headerName).toLowerCase(); // e.g. `get:cache-control`\n        if (!headersToInclude.includes(headerName) && !warningProduced.has(key)) {\n          warningProduced.add(key);\n          const truncatedUrl = truncateMiddle(url);\n\n          // TODO: create Error guide for this warning\n          console.warn(\n            formatRuntimeError(\n              RuntimeErrorCode.HEADERS_ALTERED_BY_TRANSFER_CACHE,\n              `Angular detected that the \\`${headerName}\\` header is accessed, but the value of the header ` +\n                `was not transferred from the server to the client by the HttpTransferCache. ` +\n                `To include the value of the \\`${headerName}\\` header for the \\`${truncatedUrl}\\` request, ` +\n                `use the \\`includeHeaders\\` list. The \\`includeHeaders\\` can be defined either ` +\n                `on a request level by adding the \\`transferCache\\` parameter, or on an application ` +\n                `level by adding the \\`httpCacheTransfer.includeHeaders\\` argument to the ` +\n                `\\`provideClientHydration()\\` call. `,\n            ),\n          );\n        }\n\n        // invoking the original method\n        return (value as Function).apply(target, [headerName]);\n      };\n    },\n  });\n}\n\nfunction mapRequestOriginUrl(url: string, originMap: Record<string, string>): string {\n  const origin = new URL(url, 'resolve://').origin;\n  const mappedOrigin = originMap[origin];\n  if (!mappedOrigin) {\n    return url;\n  }\n\n  if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    verifyMappedOrigin(mappedOrigin);\n  }\n\n  return url.replace(origin, mappedOrigin);\n}\n\nfunction verifyMappedOrigin(url: string): void {\n  if (new URL(url, 'resolve://').pathname !== '/') {\n    throw new RuntimeError(\n      RuntimeErrorCode.HTTP_ORIGIN_MAP_CONTAINS_PATH,\n      'Angular detected a URL with a path segment in the value provided for the ' +\n        `\\`HTTP_TRANSFER_CACHE_ORIGIN_MAP\\` token: ${url}. The map should only contain origins ` +\n        'without any other segments.',\n    );\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["i1.<PERSON><PERSON>p<PERSON><PERSON><PERSON>", "XSSI_PREFIX", "getResponseUrl", "PendingTasks", "<PERSON><PERSON><PERSON>", "formatRuntimeError", "i1.HttpBackend", "RuntimeError", "parseCookieValue", "performanceMarkFeature", "whenStable", "truncateMiddle"], "mappings": ";;;;;;;;;;;;;AAaA;;;;;;;;;;;AAWG;MACmB,WAAW,CAAA;AAEhC,CAAA;AAED;;;;;;;;;AASG;MACmB,WAAW,CAAA;AAEhC;;AC3BD;;;;;;AAMG;MACU,WAAW,CAAA;;AAyBtB,IAAA,WAAA,CACE,OAAoF,EAAA;AAnBtF;;;AAGG;AACK,QAAA,IAAA,CAAA,eAAe,GAAwB,IAAI,GAAG,EAAE,CAAC;AAOzD;;AAEG;QACK,IAAU,CAAA,UAAA,GAAoB,IAAI,CAAC;QAOzC,IAAI,CAAC,OAAO,EAAE;AACZ,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAoB,CAAC;SAC5C;AAAM,aAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACtC,YAAA,IAAI,CAAC,QAAQ,GAAG,MAAK;AACnB,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAoB,CAAC;gBAC3C,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAI;oBACnC,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAChC,oBAAA,IAAI,KAAK,GAAG,CAAC,EAAE;wBACb,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;AAClC,wBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAC/B,wBAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAC3C,wBAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;wBACvC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACzB,4BAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;yBACpC;6BAAM;4BACL,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;yBAChC;qBACF;AACH,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC;SACH;aAAM,IAAI,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,YAAY,OAAO,EAAE;AACvE,YAAA,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAoB,CAAC;YAC3C,OAAO,CAAC,OAAO,CAAC,CAAC,MAAc,EAAE,IAAY,KAAI;AAC/C,gBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACtC,aAAC,CAAC,CAAC;SACJ;aAAM;AACL,YAAA,IAAI,CAAC,QAAQ,GAAG,MAAK;AACnB,gBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;oBACjD,kBAAkB,CAAC,OAAO,CAAC,CAAC;iBAC7B;AACD,gBAAA,IAAI,CAAC,OAAO,GAAG,IAAI,GAAG,EAAoB,CAAC;AAC3C,gBAAA,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,KAAI;AACjD,oBAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACtC,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC;SACH;KACF;AAED;;;;;;AAMG;AACH,IAAA,GAAG,CAAC,IAAY,EAAA;QACd,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;KAC7C;AAED;;;;;;AAMG;AACH,IAAA,GAAG,CAAC,IAAY,EAAA;QACd,IAAI,CAAC,IAAI,EAAE,CAAC;AAEZ,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AACpD,QAAA,OAAO,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KACvD;AAED;;;;AAIG;IACH,IAAI,GAAA;QACF,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;KAClD;AAED;;;;;;AAMG;AACH,IAAA,MAAM,CAAC,IAAY,EAAA;QACjB,IAAI,CAAC,IAAI,EAAE,CAAC;AAEZ,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,IAAI,CAAC;KACrD;AAED;;;;;;;;AAQG;IAEH,MAAM,CAAC,IAAY,EAAE,KAAwB,EAAA;AAC3C,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,EAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC,CAAC;KAC3C;AACD;;;;;;;;;AASG;IACH,GAAG,CAAC,IAAY,EAAE,KAAwB,EAAA;AACxC,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,EAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC,CAAC;KAC3C;AACD;;;;;;;AAOG;IACH,MAAM,CAAC,IAAY,EAAE,KAAyB,EAAA;AAC5C,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,EAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC,CAAC;KAC3C;IAEO,sBAAsB,CAAC,IAAY,EAAE,MAAc,EAAA;QACzD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACrC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;SACxC;KACF;IAEO,IAAI,GAAA;AACV,QAAA,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE;AACnB,YAAA,IAAI,IAAI,CAAC,QAAQ,YAAY,WAAW,EAAE;AACxC,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC9B;iBAAM;gBACL,IAAI,CAAC,QAAQ,EAAE,CAAC;aACjB;AACD,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,EAAE;AACrB,gBAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9D,gBAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;aACxB;SACF;KACF;AAEO,IAAA,QAAQ,CAAC,KAAkB,EAAA;QACjC,KAAK,CAAC,IAAI,EAAE,CAAC;AACb,QAAA,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;AAC/C,YAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,CAAC;AAC/C,YAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,CAAC;AACjE,SAAC,CAAC,CAAC;KACJ;AAEO,IAAA,KAAK,CAAC,MAAc,EAAA;AAC1B,QAAA,MAAM,KAAK,GAAG,IAAI,WAAW,EAAE,CAAC;QAChC,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,YAAY,WAAW,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AAChG,QAAA,KAAK,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5D,QAAA,OAAO,KAAK,CAAC;KACd;AAEO,IAAA,WAAW,CAAC,MAAc,EAAA;QAChC,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;AACtC,QAAA,QAAQ,MAAM,CAAC,EAAE;AACf,YAAA,KAAK,GAAG,CAAC;AACT,YAAA,KAAK,GAAG;AACN,gBAAA,IAAI,KAAK,GAAG,MAAM,CAAC,KAAM,CAAC;AAC1B,gBAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC7B,oBAAA,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;iBACjB;AACD,gBAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACtB,OAAO;iBACR;gBACD,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC9C,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,SAAS,KAAK,EAAE,CAAC;AAC3E,gBAAA,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;gBACpB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBAC5B,MAAM;AACR,YAAA,KAAK,GAAG;AACN,gBAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,KAA2B,CAAC;gBACpD,IAAI,CAAC,QAAQ,EAAE;AACb,oBAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACzB,oBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;iBAClC;qBAAM;oBACL,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBACrC,IAAI,CAAC,QAAQ,EAAE;wBACb,OAAO;qBACR;oBACD,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACtE,oBAAA,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AACzB,wBAAA,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACzB,wBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;qBAClC;yBAAM;wBACL,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;qBACjC;iBACF;gBACD,MAAM;SACT;KACF;IAEO,gBAAgB,CAAC,IAAY,EAAE,MAAW,EAAA;AAChD,QAAA,MAAM,YAAY,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC,KAAK,KACzE,KAAK,CAAC,QAAQ,EAAE,CACjB,CAAC;AACF,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAC/B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;AACpC,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;KACxC;AAED;;AAEG;AACH,IAAA,OAAO,CAAC,EAA4C,EAAA;QAClD,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAA,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAClD,EAAE,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAE,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,CAC3D,CAAC;KACH;AACF,CAAA;AAED;;;;AAIG;AACH,SAAS,kBAAkB,CACzB,OAA0C,EAAA;AAE1C,IAAA,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;QAClD,IAAI,EAAE,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACtF,YAAA,MAAM,IAAI,KAAK,CACb,CAAA,0BAAA,EAA6B,GAAG,CAAsB,oBAAA,CAAA;gBACpD,CAA+D,4DAAA,EAAA,KAAK,CAAK,GAAA,CAAA,CAC5E,CAAC;SACH;KACF;AACH;;ACxQA;;;;;;;;;AASG;MACU,oBAAoB,CAAA;AAC/B;;;;AAIG;AACH,IAAA,SAAS,CAAC,GAAW,EAAA;AACnB,QAAA,OAAO,gBAAgB,CAAC,GAAG,CAAC,CAAC;KAC9B;AAED;;;;AAIG;AACH,IAAA,WAAW,CAAC,KAAa,EAAA;AACvB,QAAA,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;KAChC;AAED;;;;AAIG;AACH,IAAA,SAAS,CAAC,GAAW,EAAA;AACnB,QAAA,OAAO,kBAAkB,CAAC,GAAG,CAAC,CAAC;KAChC;AAED;;;;AAIG;AACH,IAAA,WAAW,CAAC,KAAa,EAAA;AACvB,QAAA,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;KAClC;AACF,CAAA;AAED,SAAS,WAAW,CAAC,SAAiB,EAAE,KAAyB,EAAA;AAC/D,IAAA,MAAM,GAAG,GAAG,IAAI,GAAG,EAAoB,CAAC;AACxC,IAAA,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;;;;AAIxB,QAAA,MAAM,MAAM,GAAa,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACjE,QAAA,MAAM,CAAC,OAAO,CAAC,CAAC,KAAa,KAAI;YAC/B,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,GACd,KAAK,IAAI,CAAC,CAAC;kBACP,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC;AAC9B,kBAAE,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1F,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;AAChC,YAAA,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,YAAA,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACrB,SAAC,CAAC,CAAC;KACJ;AACD,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;AAEG;AACH,MAAM,uBAAuB,GAAG,iBAAiB,CAAC;AAClD,MAAM,8BAA8B,GAA0B;AAC5D,IAAA,IAAI,EAAE,GAAG;AACT,IAAA,IAAI,EAAE,GAAG;AACT,IAAA,IAAI,EAAE,GAAG;AACT,IAAA,IAAI,EAAE,GAAG;AACT,IAAA,IAAI,EAAE,GAAG;AACT,IAAA,IAAI,EAAE,GAAG;AACT,IAAA,IAAI,EAAE,GAAG;AACT,IAAA,IAAI,EAAE,GAAG;CACV,CAAC;AAEF,SAAS,gBAAgB,CAAC,CAAS,EAAA;IACjC,OAAO,kBAAkB,CAAC,CAAC,CAAC,CAAC,OAAO,CAClC,uBAAuB,EACvB,CAAC,CAAC,EAAE,CAAC,KAAK,8BAA8B,CAAC,CAAC,CAAC,IAAI,CAAC,CACjD,CAAC;AACJ,CAAC;AAED,SAAS,aAAa,CAAC,KAAgC,EAAA;IACrD,OAAO,CAAA,EAAG,KAAK,CAAA,CAAE,CAAC;AACpB,CAAC;AA6BD;;;;;;;AAOG;MACU,UAAU,CAAA;AAMrB,IAAA,WAAA,CAAY,UAA6B,EAAuB,EAAA;QAHxD,IAAO,CAAA,OAAA,GAAoB,IAAI,CAAC;QAChC,IAAS,CAAA,SAAA,GAAsB,IAAI,CAAC;QAG1C,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,oBAAoB,EAAE,CAAC;AAC7D,QAAA,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE;AACxB,YAAA,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE;AACxB,gBAAA,MAAM,IAAI,KAAK,CAAC,CAAA,8CAAA,CAAgD,CAAC,CAAC;aACnE;AACD,YAAA,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;SAC1D;AAAM,aAAA,IAAI,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE;AAC/B,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAoB,CAAC;AACvC,YAAA,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,KAAI;gBAC9C,MAAM,KAAK,GAAI,OAAO,CAAC,UAAkB,CAAC,GAAG,CAAC,CAAC;;gBAE/C,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;gBACxF,IAAI,CAAC,GAAI,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;AAC7B,aAAC,CAAC,CAAC;SACJ;aAAM;AACL,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;SACjB;KACF;AAED;;;;;AAKG;AACH,IAAA,GAAG,CAAC,KAAa,EAAA;QACf,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC,GAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KAC7B;AAED;;;;;AAKG;AACH,IAAA,GAAG,CAAC,KAAa,EAAA;QACf,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,MAAM,GAAG,GAAG,IAAI,CAAC,GAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjC,QAAA,OAAO,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;KAC9B;AAED;;;;;AAKG;AACH,IAAA,MAAM,CAAC,KAAa,EAAA;QAClB,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,IAAI,CAAC,GAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;KACrC;AAED;;;AAGG;IACH,IAAI,GAAA;QACF,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAI,CAAC,IAAI,EAAE,CAAC,CAAC;KACrC;AAED;;;;;AAKG;IACH,MAAM,CAAC,KAAa,EAAE,KAAgC,EAAA;AACpD,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,EAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC,CAAC;KAC5C;AAED;;;;AAIG;AACH,IAAA,SAAS,CAAC,MAET,EAAA;QACC,MAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACpC,YAAA,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5B,YAAA,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;AACxB,gBAAA,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACvB,oBAAA,OAAO,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC,CAAC;AAChD,iBAAC,CAAC,CAAC;aACJ;iBAAM;AACL,gBAAA,OAAO,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,KAAK,EAAE,KAAkC,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC,CAAC;aAC3E;AACH,SAAC,CAAC,CAAC;AACH,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC5B;AAED;;;;;AAKG;IACH,GAAG,CAAC,KAAa,EAAE,KAAgC,EAAA;AACjD,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,EAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC,CAAC;KAC5C;AAED;;;;;;AAMG;IACH,MAAM,CAAC,KAAa,EAAE,KAAiC,EAAA;AACrD,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,EAAC,KAAK,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,EAAC,CAAC,CAAC;KAC5C;AAED;;;AAGG;IACH,QAAQ,GAAA;QACN,IAAI,CAAC,IAAI,EAAE,CAAC;AACZ,QAAA,QACE,IAAI,CAAC,IAAI,EAAE;AACR,aAAA,GAAG,CAAC,CAAC,GAAG,KAAI;YACX,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;;;;AAIzC,YAAA,OAAO,IAAI,CAAC,GAAI,CAAC,GAAG,CAAC,GAAG,CAAE;AACvB,iBAAA,GAAG,CAAC,CAAC,KAAK,KAAK,IAAI,GAAG,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;iBAC5D,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,SAAC,CAAC;;;aAGD,MAAM,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,EAAE,CAAC;AAC/B,aAAA,IAAI,CAAC,GAAG,CAAC,EACZ;KACH;AAEO,IAAA,KAAK,CAAC,MAAyB,EAAA;AACrC,QAAA,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,EAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAsB,CAAC,CAAC;QAC3E,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;AACzC,QAAA,KAAK,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;AACpD,QAAA,OAAO,KAAK,CAAC;KACd;IAEO,IAAI,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,GAAG,KAAK,IAAI,EAAE;AACrB,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,GAAG,EAAoB,CAAC;SACxC;AACD,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;AACtB,YAAA,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,IAAI,CAAC,GAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,SAAU,CAAC,GAAI,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,CAAC,CAAC;YAC3F,IAAI,CAAC,OAAQ,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AAC/B,gBAAA,QAAQ,MAAM,CAAC,EAAE;AACf,oBAAA,KAAK,GAAG,CAAC;AACT,oBAAA,KAAK,GAAG;AACN,wBAAA,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,KAAK,GAAG,GAAG,IAAI,CAAC,GAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,KAAK,EAAE,CAAC;wBACjF,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,KAAM,CAAC,CAAC,CAAC;wBACxC,IAAI,CAAC,GAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;wBAClC,MAAM;AACR,oBAAA,KAAK,GAAG;AACN,wBAAA,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,EAAE;AAC9B,4BAAA,IAAI,IAAI,GAAG,IAAI,CAAC,GAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAC7C,4BAAA,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACtD,4BAAA,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;AACd,gCAAA,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;6BACrB;AACD,4BAAA,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gCACnB,IAAI,CAAC,GAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;6BACnC;iCAAM;gCACL,IAAI,CAAC,GAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;6BAChC;yBACF;6BAAM;4BACL,IAAI,CAAC,GAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;4BAC/B,MAAM;yBACP;iBACJ;AACH,aAAC,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;SACtC;KACF;AACF;;AC9UD;;;;AAIG;MACU,gBAAgB,CAAA;AAC3B,IAAA,WAAA,CAA4B,YAAqB,EAAA;QAArB,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAS;KAAI;AACtD,CAAA;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgCG;MACU,WAAW,CAAA;AAAxB,IAAA,WAAA,GAAA;AACmB,QAAA,IAAA,CAAA,GAAG,GAAG,IAAI,GAAG,EAAsC,CAAC;KA0DtE;AAxDC;;;;;;;AAOG;IACH,GAAG,CAAI,KAA0B,EAAE,KAAQ,EAAA;QACzC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC3B,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;;AAMG;AACH,IAAA,GAAG,CAAI,KAA0B,EAAA;QAC/B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AACxB,YAAA,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;SAC3C;QACD,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAM,CAAC;KACjC;AAED;;;;;;AAMG;AACH,IAAA,MAAM,CAAC,KAAgC,EAAA;AACrC,QAAA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACvB,QAAA,OAAO,IAAI,CAAC;KACb;AAED;;;;;;AAMG;AACH,IAAA,GAAG,CAAC,KAAgC,EAAA;QAClC,OAAO,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;KAC5B;AAED;;AAEG;IACH,IAAI,GAAA;AACF,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;KACxB;AACF;;AClFD;;AAEG;AACH,SAAS,aAAa,CAAC,MAAc,EAAA;IACnC,QAAQ,MAAM;AACZ,QAAA,KAAK,QAAQ,CAAC;AACd,QAAA,KAAK,KAAK,CAAC;AACX,QAAA,KAAK,MAAM,CAAC;AACZ,QAAA,KAAK,SAAS,CAAC;AACf,QAAA,KAAK,OAAO;AACV,YAAA,OAAO,KAAK,CAAC;AACf,QAAA;AACE,YAAA,OAAO,IAAI,CAAC;KACf;AACH,CAAC;AAED;;;;AAIG;AACH,SAAS,aAAa,CAAC,KAAU,EAAA;IAC/B,OAAO,OAAO,WAAW,KAAK,WAAW,IAAI,KAAK,YAAY,WAAW,CAAC;AAC5E,CAAC;AAED;;;;AAIG;AACH,SAAS,MAAM,CAAC,KAAU,EAAA;IACxB,OAAO,OAAO,IAAI,KAAK,WAAW,IAAI,KAAK,YAAY,IAAI,CAAC;AAC9D,CAAC;AAED;;;;AAIG;AACH,SAAS,UAAU,CAAC,KAAU,EAAA;IAC5B,OAAO,OAAO,QAAQ,KAAK,WAAW,IAAI,KAAK,YAAY,QAAQ,CAAC;AACtE,CAAC;AAED;;;;AAIG;AACH,SAAS,iBAAiB,CAAC,KAAU,EAAA;IACnC,OAAO,OAAO,eAAe,KAAK,WAAW,IAAI,KAAK,YAAY,eAAe,CAAC;AACpF,CAAC;AAED;;;;;;;;;AASG;MACU,WAAW,CAAA;AAkKtB,IAAA,WAAA,CACE,MAAc,EACL,GAAW,EACpB,KAWQ,EACR,MAQC,EAAA;QArBQ,IAAG,CAAA,GAAA,GAAH,GAAG,CAAQ;AAnKtB;;;;;;AAMG;QACM,IAAI,CAAA,IAAA,GAAa,IAAI,CAAC;AAa/B;;;;;;;AAOG;QACM,IAAc,CAAA,cAAA,GAAY,KAAK,CAAC;AAEzC;;AAEG;QACM,IAAe,CAAA,eAAA,GAAY,KAAK,CAAC;AAE1C;;;;;AAKG;QACM,IAAY,CAAA,YAAA,GAA6C,MAAM,CAAC;AAiJvE,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;;;AAGnC,QAAA,IAAI,OAAoC,CAAC;;;QAIzC,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE;;AAE1C,YAAA,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,SAAS,GAAI,KAAW,GAAG,IAAI,CAAC;YACtD,OAAO,GAAG,MAAM,CAAC;SAClB;aAAM;;YAEL,OAAO,GAAG,KAAwB,CAAC;SACpC;;QAGD,IAAI,OAAO,EAAE;;YAEX,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC;YAC/C,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC;;AAGjD,YAAA,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE;AAC1B,gBAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;aAC1C;;AAGD,YAAA,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;AACrB,gBAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;aAChC;AAED,YAAA,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE;AACrB,gBAAA,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;aAChC;AAED,YAAA,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE;AACpB,gBAAA,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;aAC9B;;AAGD,YAAA,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;SAC5C;;AAGD,QAAA,IAAI,CAAC,OAAO,KAAK,IAAI,WAAW,EAAE,CAAC;;AAGnC,QAAA,IAAI,CAAC,OAAO,KAAK,IAAI,WAAW,EAAE,CAAC;;AAGnC,QAAA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAChB,YAAA,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AAC/B,YAAA,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;SAC1B;aAAM;;YAEL,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;AACtC,YAAA,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;;AAEvB,gBAAA,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;aAC1B;iBAAM;;gBAEL,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;AAQ9B,gBAAA,MAAM,GAAG,GAAW,IAAI,KAAK,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;gBACzE,IAAI,CAAC,aAAa,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,CAAC;aACzC;SACF;KACF;AAED;;;AAGG;IACH,aAAa,GAAA;;AAEX,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;AACtB,YAAA,OAAO,IAAI,CAAC;SACb;;;AAGD,QAAA,IACE,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;AAC7B,YAAA,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC;AACxB,YAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;AACjB,YAAA,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACrB,YAAA,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAC5B;YACA,OAAO,IAAI,CAAC,IAAI,CAAC;SAClB;;AAED,QAAA,IAAI,IAAI,CAAC,IAAI,YAAY,UAAU,EAAE;AACnC,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;SAC7B;;AAED,QAAA,IACE,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;AAC7B,YAAA,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS;YAC9B,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EACxB;YACA,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAClC;;AAED,QAAA,OAAQ,IAAI,CAAC,IAAY,CAAC,QAAQ,EAAE,CAAC;KACtC;AAED;;;;;AAKG;IACH,uBAAuB,GAAA;;AAErB,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;AACtB,YAAA,OAAO,IAAI,CAAC;SACb;;AAED,QAAA,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC;SACb;;;AAGD,QAAA,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACrB,YAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC;SAC/B;;AAED,QAAA,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AAC5B,YAAA,OAAO,IAAI,CAAC;SACb;;;AAGD,QAAA,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;AACjC,YAAA,OAAO,YAAY,CAAC;SACrB;;AAED,QAAA,IAAI,IAAI,CAAC,IAAI,YAAY,UAAU,EAAE;AACnC,YAAA,OAAO,iDAAiD,CAAC;SAC1D;;AAED,QAAA,IACE,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;AAC7B,YAAA,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ;AAC7B,YAAA,OAAO,IAAI,CAAC,IAAI,KAAK,SAAS,EAC9B;AACA,YAAA,OAAO,kBAAkB,CAAC;SAC3B;;AAED,QAAA,OAAO,IAAI,CAAC;KACb;IA+BD,KAAK,CACH,SAaI,EAAE,EAAA;;;QAIN,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;QAC5C,MAAM,GAAG,GAAG,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC;QACnC,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC;;;QAI9D,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC;;;;;AAMjE,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,KAAK,SAAS,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;;;QAIjE,MAAM,eAAe,GAAG,MAAM,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC;QACvE,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC;;;QAIpE,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;QAC7C,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;;QAG1C,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC;;AAG/C,QAAA,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE;;AAEnC,YAAA,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,MAAM,CAC7C,CAAC,OAAO,EAAE,IAAI,KAAK,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,UAAW,CAAC,IAAI,CAAC,CAAC,EAC9D,OAAO,CACR,CAAC;SACH;;AAGD,QAAA,IAAI,MAAM,CAAC,SAAS,EAAE;;AAEpB,YAAA,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAC3C,CAAC,MAAM,EAAE,KAAK,KAAK,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,SAAU,CAAC,KAAK,CAAC,CAAC,EAC9D,MAAM,CACP,CAAC;SACH;;QAGD,OAAO,IAAI,WAAW,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;YACxC,MAAM;YACN,OAAO;YACP,OAAO;YACP,cAAc;YACd,YAAY;YACZ,eAAe;YACf,aAAa;AACd,SAAA,CAAC,CAAC;KACJ;AACF;;AC9gBD;;;;AAIG;IACS,cAgCX;AAhCD,CAAA,UAAY,aAAa,EAAA;AACvB;;AAEG;AACH,IAAA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AAEJ;;;;AAIG;AACH,IAAA,aAAA,CAAA,aAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc,CAAA;AAEd;;AAEG;AACH,IAAA,aAAA,CAAA,aAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,gBAAc,CAAA;AAEd;;AAEG;AACH,IAAA,aAAA,CAAA,aAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,kBAAgB,CAAA;AAEhB;;AAEG;AACH,IAAA,aAAA,CAAA,aAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ,CAAA;AAER;;AAEG;AACH,IAAA,aAAA,CAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI,CAAA;AACN,CAAC,EAhCW,aAAa,KAAb,aAAa,GAgCxB,EAAA,CAAA,CAAA,CAAA;AAsGD;;;;AAIG;MACmB,gBAAgB,CAAA;AAkCpC;;;;;AAKG;AACH,IAAA,WAAA,CACE,IAKC,EACD,aAAA,GAAwB,GAAG,EAC3B,oBAA4B,IAAI,EAAA;;;QAIhC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,WAAW,EAAE,CAAC;AACjD,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,aAAa,CAAC;QACtE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,iBAAiB,CAAC;QACvD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC;;AAG5B,QAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC;KACnD;AACF,CAAA;AAED;;;;;;;;AAQG;AACG,MAAO,kBAAmB,SAAQ,gBAAgB,CAAA;AACtD;;AAEG;AACH,IAAA,WAAA,CACE,OAKI,EAAE,EAAA;QAEN,KAAK,CAAC,IAAI,CAAC,CAAC;AAGI,QAAA,IAAA,CAAA,IAAI,GAAiC,aAAa,CAAC,cAAc,CAAC;KAFnF;AAID;;;AAGG;IACH,KAAK,CACH,SAAsF,EAAE,EAAA;;;QAIxF,OAAO,IAAI,kBAAkB,CAAC;AAC5B,YAAA,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO;AACvC,YAAA,MAAM,EAAE,MAAM,CAAC,MAAM,KAAK,SAAS,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;AACjE,YAAA,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU;YAChD,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,SAAS;AACzC,SAAA,CAAC,CAAC;KACJ;AACF,CAAA;AAED;;;;;;;;AAQG;AACG,MAAO,YAAgB,SAAQ,gBAAgB,CAAA;AAMnD;;AAEG;AACH,IAAA,WAAA,CACE,OAMI,EAAE,EAAA;QAEN,KAAK,CAAC,IAAI,CAAC,CAAC;AAII,QAAA,IAAA,CAAA,IAAI,GAA2B,aAAa,CAAC,QAAQ,CAAC;AAHtE,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,KAAK,SAAS,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;KACxD;IAkBD,KAAK,CACH,SAMI,EAAE,EAAA;QAEN,OAAO,IAAI,YAAY,CAAM;AAC3B,YAAA,IAAI,EAAE,MAAM,CAAC,IAAI,KAAK,SAAS,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;AACzD,YAAA,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO;AACvC,YAAA,MAAM,EAAE,MAAM,CAAC,MAAM,KAAK,SAAS,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM;AACjE,YAAA,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU;YAChD,GAAG,EAAE,MAAM,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,IAAI,SAAS;AACzC,SAAA,CAAC,CAAC;KACJ;AACF,CAAA;AAED;;;;;;;;;;;;AAYG;AACG,MAAO,iBAAkB,SAAQ,gBAAgB,CAAA;AAUrD,IAAA,WAAA,CAAY,IAMX,EAAA;;AAEC,QAAA,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC;QAjBzB,IAAI,CAAA,IAAA,GAAG,mBAAmB,CAAC;AAIpC;;AAEG;QACe,IAAE,CAAA,EAAA,GAAG,KAAK,CAAC;;;;AAe3B,QAAA,IAAI,IAAI,CAAC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;YAC3C,IAAI,CAAC,OAAO,GAAG,CAAmC,gCAAA,EAAA,IAAI,CAAC,GAAG,IAAI,eAAe,CAAA,CAAE,CAAC;SACjF;aAAM;AACL,YAAA,IAAI,CAAC,OAAO,GAAG,6BAA6B,IAAI,CAAC,GAAG,IAAI,eAAe,CAAK,EAAA,EAAA,IAAI,CAAC,MAAM,CAAA,CAAA,EACrF,IAAI,CAAC,UACP,EAAE,CAAC;SACJ;QACD,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;KACjC;AACF,CAAA;AAED;;;AAGG;AACI,MAAM,mBAAmB,GAAG,GAAG,CAAC;AAChC,MAAM,2BAA2B,GAAG,GAAG,CAAC;AAE/C;;;;AAIG;IACS,eAoEX;AApED,CAAA,UAAY,cAAc,EAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA,GAAA,UAAc,CAAA;AACd,IAAA,cAAA,CAAA,cAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,YAAA,CAAA,GAAA,GAAA,CAAA,GAAA,YAAgB,CAAA;AAChB,IAAA,cAAA,CAAA,cAAA,CAAA,YAAA,CAAA,GAAA,GAAA,CAAA,GAAA,YAAgB,CAAA;AAEhB,IAAA,cAAA,CAAA,cAAA,CAAA,IAAA,CAAA,GAAA,GAAA,CAAA,GAAA,IAAwB,CAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,SAAA,CAAA,GAAA,GAAA,CAAA,GAAA,SAAa,CAAA;AACb,IAAA,cAAA,CAAA,cAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA,GAAA,UAAc,CAAA;AACd,IAAA,cAAA,CAAA,cAAA,CAAA,6BAAA,CAAA,GAAA,GAAA,CAAA,GAAA,6BAAiC,CAAA;AACjC,IAAA,cAAA,CAAA,cAAA,CAAA,WAAA,CAAA,GAAA,GAAA,CAAA,GAAA,WAAuC,CAAA;AACvC,IAAA,cAAA,CAAA,cAAA,CAAA,cAAA,CAAA,GAAA,GAAA,CAAA,GAAA,cAAkB,CAAA;AAClB,IAAA,cAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,gBAAoB,CAAA;AACpB,IAAA,cAAA,CAAA,cAAA,CAAA,aAAA,CAAA,GAAA,GAAA,CAAA,GAAA,aAAiB,CAAA;AACjB,IAAA,cAAA,CAAA,cAAA,CAAA,iBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,iBAAqB,CAAA;AACrB,IAAA,cAAA,CAAA,cAAA,CAAA,QAAA,CAAA,GAAA,GAAA,CAAA,GAAA,QAAY,CAAA;AAEZ,IAAA,cAAA,CAAA,cAAA,CAAA,iBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,iBAAqB,CAAA;AACrB,IAAA,cAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAsB,CAAA;AACtB,IAAA,cAAA,CAAA,cAAA,CAAA,OAAA,CAAA,GAAA,GAAA,CAAA,GAAA,OAAW,CAAA;AACX,IAAA,cAAA,CAAA,cAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA,GAAA,UAAc,CAAA;AACd,IAAA,cAAA,CAAA,cAAA,CAAA,aAAA,CAAA,GAAA,GAAA,CAAA,GAAA,aAAiB,CAAA;AACjB,IAAA,cAAA,CAAA,cAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA,GAAA,UAAc,CAAA;AACd,IAAA,cAAA,CAAA,cAAA,CAAA,QAAA,CAAA,GAAA,GAAA,CAAA,GAAA,QAAY,CAAA;AACZ,IAAA,cAAA,CAAA,cAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;AACvB,IAAA,cAAA,CAAA,cAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;AAEvB,IAAA,cAAA,CAAA,cAAA,CAAA,YAAA,CAAA,GAAA,GAAA,CAAA,GAAA,YAAgB,CAAA;AAChB,IAAA,cAAA,CAAA,cAAA,CAAA,cAAA,CAAA,GAAA,GAAA,CAAA,GAAA,cAAkB,CAAA;AAClB,IAAA,cAAA,CAAA,cAAA,CAAA,iBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,iBAAqB,CAAA;AACrB,IAAA,cAAA,CAAA,cAAA,CAAA,WAAA,CAAA,GAAA,GAAA,CAAA,GAAA,WAAe,CAAA;AACf,IAAA,cAAA,CAAA,cAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA,GAAA,UAAc,CAAA;AACd,IAAA,cAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAsB,CAAA;AACtB,IAAA,cAAA,CAAA,cAAA,CAAA,eAAA,CAAA,GAAA,GAAA,CAAA,GAAA,eAAmB,CAAA;AACnB,IAAA,cAAA,CAAA,cAAA,CAAA,6BAAA,CAAA,GAAA,GAAA,CAAA,GAAA,6BAAiC,CAAA;AACjC,IAAA,cAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,gBAAoB,CAAA;AACpB,IAAA,cAAA,CAAA,cAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA,GAAA,UAAc,CAAA;AACd,IAAA,cAAA,CAAA,cAAA,CAAA,MAAA,CAAA,GAAA,GAAA,CAAA,GAAA,MAAU,CAAA;AACV,IAAA,cAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,gBAAoB,CAAA;AACpB,IAAA,cAAA,CAAA,cAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,iBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,iBAAqB,CAAA;AACrB,IAAA,cAAA,CAAA,cAAA,CAAA,YAAA,CAAA,GAAA,GAAA,CAAA,GAAA,YAAgB,CAAA;AAChB,IAAA,cAAA,CAAA,cAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC1B,IAAA,cAAA,CAAA,cAAA,CAAA,qBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,qBAAyB,CAAA;AACzB,IAAA,cAAA,CAAA,cAAA,CAAA,mBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,mBAAuB,CAAA;AACvB,IAAA,cAAA,CAAA,cAAA,CAAA,WAAA,CAAA,GAAA,GAAA,CAAA,GAAA,WAAe,CAAA;AACf,IAAA,cAAA,CAAA,cAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,qBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,qBAAyB,CAAA;AACzB,IAAA,cAAA,CAAA,cAAA,CAAA,QAAA,CAAA,GAAA,GAAA,CAAA,GAAA,QAAY,CAAA;AACZ,IAAA,cAAA,CAAA,cAAA,CAAA,kBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,kBAAsB,CAAA;AACtB,IAAA,cAAA,CAAA,cAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA,GAAA,UAAc,CAAA;AACd,IAAA,cAAA,CAAA,cAAA,CAAA,iBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,iBAAqB,CAAA;AACrB,IAAA,cAAA,CAAA,cAAA,CAAA,sBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,sBAA0B,CAAA;AAC1B,IAAA,cAAA,CAAA,cAAA,CAAA,iBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,iBAAqB,CAAA;AACrB,IAAA,cAAA,CAAA,cAAA,CAAA,6BAAA,CAAA,GAAA,GAAA,CAAA,GAAA,6BAAiC,CAAA;AACjC,IAAA,cAAA,CAAA,cAAA,CAAA,4BAAA,CAAA,GAAA,GAAA,CAAA,GAAA,4BAAgC,CAAA;AAEhC,IAAA,cAAA,CAAA,cAAA,CAAA,qBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,qBAAyB,CAAA;AACzB,IAAA,cAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,gBAAoB,CAAA;AACpB,IAAA,cAAA,CAAA,cAAA,CAAA,YAAA,CAAA,GAAA,GAAA,CAAA,GAAA,YAAgB,CAAA;AAChB,IAAA,cAAA,CAAA,cAAA,CAAA,oBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,oBAAwB,CAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,gBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,gBAAoB,CAAA;AACpB,IAAA,cAAA,CAAA,cAAA,CAAA,yBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,yBAA6B,CAAA;AAC7B,IAAA,cAAA,CAAA,cAAA,CAAA,uBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,uBAA2B,CAAA;AAC3B,IAAA,cAAA,CAAA,cAAA,CAAA,qBAAA,CAAA,GAAA,GAAA,CAAA,GAAA,qBAAyB,CAAA;AACzB,IAAA,cAAA,CAAA,cAAA,CAAA,cAAA,CAAA,GAAA,GAAA,CAAA,GAAA,cAAkB,CAAA;AAClB,IAAA,cAAA,CAAA,cAAA,CAAA,aAAA,CAAA,GAAA,GAAA,CAAA,GAAA,aAAiB,CAAA;AACjB,IAAA,cAAA,CAAA,cAAA,CAAA,+BAAA,CAAA,GAAA,GAAA,CAAA,GAAA,+BAAmC,CAAA;AACrC,CAAC,EApEW,cAAc,KAAd,cAAc,GAoEzB,EAAA,CAAA,CAAA;;AClbD;;;;;;;;;AASG;AACH,SAAS,OAAO,CACd,OAWC,EACD,IAAc,EAAA;IAEd,OAAO;QACL,IAAI;QACJ,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,OAAO,EAAE,OAAO,CAAC,OAAO;QACxB,MAAM,EAAE,OAAO,CAAC,MAAM;QACtB,cAAc,EAAE,OAAO,CAAC,cAAc;QACtC,YAAY,EAAE,OAAO,CAAC,YAAY;QAClC,eAAe,EAAE,OAAO,CAAC,eAAe;QACxC,aAAa,EAAE,OAAO,CAAC,aAAa;KACrC,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqDG;MAEU,UAAU,CAAA;AACrB,IAAA,WAAA,CAAoB,OAAoB,EAAA;QAApB,IAAO,CAAA,OAAA,GAAP,OAAO,CAAa;KAAI;AA6c5C;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACH,IAAA,OAAO,CACL,KAAgC,EAChC,GAAY,EACZ,UAYI,EAAE,EAAA;AAEN,QAAA,IAAI,GAAqB,CAAC;;AAE1B,QAAA,IAAI,KAAK,YAAY,WAAW,EAAE;;;YAGhC,GAAG,GAAG,KAAK,CAAC;SACb;aAAM;;;;;YAML,IAAI,OAAO,GAA4B,SAAS,CAAC;AACjD,YAAA,IAAI,OAAO,CAAC,OAAO,YAAY,WAAW,EAAE;AAC1C,gBAAA,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;aAC3B;iBAAM;gBACL,OAAO,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aAC5C;;YAGD,IAAI,MAAM,GAA2B,SAAS,CAAC;AAC/C,YAAA,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE;AACpB,gBAAA,IAAI,OAAO,CAAC,MAAM,YAAY,UAAU,EAAE;AACxC,oBAAA,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;iBACzB;qBAAM;AACL,oBAAA,MAAM,GAAG,IAAI,UAAU,CAAC,EAAC,UAAU,EAAE,OAAO,CAAC,MAAM,EAAsB,CAAC,CAAC;iBAC5E;aACF;;YAGD,GAAG,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE,GAAI,EAAE,OAAO,CAAC,IAAI,KAAK,SAAS,GAAG,OAAO,CAAC,IAAI,GAAG,IAAI,EAAE;gBACnF,OAAO;gBACP,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,MAAM;gBACN,cAAc,EAAE,OAAO,CAAC,cAAc;;AAEtC,gBAAA,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,MAAM;gBAC5C,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,aAAa,EAAE,OAAO,CAAC,aAAa;AACrC,aAAA,CAAC,CAAC;SACJ;;;;;QAMD,MAAM,OAAO,GAA+B,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,CACtD,SAAS,CAAC,CAAC,GAAqB,KAAK,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAC/D,CAAC;;;;QAKF,IAAI,KAAK,YAAY,WAAW,IAAI,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE;AAChE,YAAA,OAAO,OAAO,CAAC;SAChB;;;;QAKD,MAAM,IAAI,IACR,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAqB,KAAK,KAAK,YAAY,YAAY,CAAC,CAAC,CAC/E,CAAC;;AAGF,QAAA,QAAQ,OAAO,CAAC,OAAO,IAAI,MAAM;AAC/B,YAAA,KAAK,MAAM;;;;;;AAMT,gBAAA,QAAQ,GAAG,CAAC,YAAY;AACtB,oBAAA,KAAK,aAAa;wBAChB,OAAO,IAAI,CAAC,IAAI,CACd,GAAG,CAAC,CAAC,GAAsB,KAAI;;AAE7B,4BAAA,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,YAAY,WAAW,CAAC,EAAE;AAC3D,gCAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;6BACpD;4BACD,OAAO,GAAG,CAAC,IAAI,CAAC;yBACjB,CAAC,CACH,CAAC;AACJ,oBAAA,KAAK,MAAM;wBACT,OAAO,IAAI,CAAC,IAAI,CACd,GAAG,CAAC,CAAC,GAAsB,KAAI;;AAE7B,4BAAA,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE,GAAG,CAAC,IAAI,YAAY,IAAI,CAAC,EAAE;AACpD,gCAAA,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;6BAC5C;4BACD,OAAO,GAAG,CAAC,IAAI,CAAC;yBACjB,CAAC,CACH,CAAC;AACJ,oBAAA,KAAK,MAAM;wBACT,OAAO,IAAI,CAAC,IAAI,CACd,GAAG,CAAC,CAAC,GAAsB,KAAI;;AAE7B,4BAAA,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE;AACrD,gCAAA,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;6BAC9C;4BACD,OAAO,GAAG,CAAC,IAAI,CAAC;yBACjB,CAAC,CACH,CAAC;AACJ,oBAAA,KAAK,MAAM,CAAC;AACZ,oBAAA;;AAEE,wBAAA,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAsB,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;iBAC/D;AACH,YAAA,KAAK,UAAU;;AAEb,gBAAA,OAAO,IAAI,CAAC;AACd,YAAA;;gBAEE,MAAM,IAAI,KAAK,CAAC,CAAA,oCAAA,EAAuC,OAAO,CAAC,OAAO,CAAG,CAAA,CAAA,CAAC,CAAC;SAC9E;KACF;AA+XD;;;;;;;;AAQG;AACH,IAAA,MAAM,CACJ,GAAW,EACX,OAAA,GAWI,EAAE,EAAA;QAEN,OAAO,IAAI,CAAC,OAAO,CAAM,QAAQ,EAAE,GAAG,EAAE,OAAc,CAAC,CAAC;KACzD;AAgYD;;;;AAIG;AACH,IAAA,GAAG,CACD,GAAW,EACX,OAAA,GAWI,EAAE,EAAA;QAEN,OAAO,IAAI,CAAC,OAAO,CAAM,KAAK,EAAE,GAAG,EAAE,OAAc,CAAC,CAAC;KACtD;AAsYD;;;;;;AAMG;AACH,IAAA,IAAI,CACF,GAAW,EACX,OAAA,GAWI,EAAE,EAAA;QAEN,OAAO,IAAI,CAAC,OAAO,CAAM,MAAM,EAAE,GAAG,EAAE,OAAc,CAAC,CAAC;KACvD;AA0BD;;;;;;;;;;;;;;;;;AAiBG;IACH,KAAK,CAAI,GAAW,EAAE,aAAqB,EAAA;AACzC,QAAA,OAAO,IAAI,CAAC,OAAO,CAAM,OAAO,EAAE,GAAG,EAAE;YACrC,MAAM,EAAE,IAAI,UAAU,EAAE,CAAC,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC;AAChE,YAAA,OAAO,EAAE,MAAM;AACf,YAAA,YAAY,EAAE,MAAM;AACrB,SAAA,CAAC,CAAC;KACJ;AAqXD;;;;;;AAMG;AACH,IAAA,OAAO,CACL,GAAW,EACX,OAAA,GAUI,EAAE,EAAA;QAEN,OAAO,IAAI,CAAC,OAAO,CAAM,SAAS,EAAE,GAAG,EAAE,OAAc,CAAC,CAAC;KAC1D;AAqZD;;;;AAIG;AACH,IAAA,KAAK,CACH,GAAW,EACX,IAAgB,EAChB,UAUI,EAAE,EAAA;AAEN,QAAA,OAAO,IAAI,CAAC,OAAO,CAAM,OAAO,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;KAChE;AAkaD;;;;;AAKG;AACH,IAAA,IAAI,CACF,GAAW,EACX,IAAgB,EAChB,UAWI,EAAE,EAAA;AAEN,QAAA,OAAO,IAAI,CAAC,OAAO,CAAM,MAAM,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;KAC/D;AAkZD;;;;;AAKG;AACH,IAAA,GAAG,CACD,GAAW,EACX,IAAgB,EAChB,UAUI,EAAE,EAAA;AAEN,QAAA,OAAO,IAAI,CAAC,OAAO,CAAM,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;KAC9D;yHA9/GU,UAAU,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAA,WAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAV,UAAU,EAAA,CAAA,CAAA,EAAA;;sGAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBADtB,UAAU;;;ACvFX,MAAMC,aAAW,GAAG,cAAc,CAAC;AAEnC,MAAM,kBAAkB,GAAG,CAAA,aAAA,CAAe,CAAC;AAE3C;;;AAGG;AACH,SAASC,gBAAc,CAAC,QAAkB,EAAA;AACxC,IAAA,IAAI,QAAQ,CAAC,GAAG,EAAE;QAChB,OAAO,QAAQ,CAAC,GAAG,CAAC;KACrB;;AAED,IAAA,MAAM,WAAW,GAAG,kBAAkB,CAAC,iBAAiB,EAAE,CAAC;IAC3D,OAAO,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AAC3C,CAAC;AAED;;;;;;;;;;AAUG;MAEU,YAAY,CAAA;AADzB,IAAA,WAAA,GAAA;;;;AAKmB,QAAA,IAAA,CAAA,SAAS,GACxB,MAAM,CAAC,YAAY,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,KAAK,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;AAC3E,QAAA,IAAA,CAAA,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAkO1C,KAAA;AAhOC,IAAA,MAAM,CAAC,OAAyB,EAAA;AAC9B,QAAA,OAAO,IAAI,UAAU,CAAC,CAAC,QAAQ,KAAI;AACjC,YAAA,MAAM,OAAO,GAAG,IAAI,eAAe,EAAE,CAAC;AACtC,YAAA,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,KACjE,QAAQ,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC,EAAC,KAAK,EAAC,CAAC,CAAC,CAC/C,CAAC;AACF,YAAA,OAAO,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;AAC/B,SAAC,CAAC,CAAC;KACJ;AAEO,IAAA,MAAM,SAAS,CACrB,OAAyB,EACzB,MAAmB,EACnB,QAAkC,EAAA;QAElC,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC7C,QAAA,IAAI,QAAQ,CAAC;AAEb,QAAA,IAAI;;;;YAIF,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,MACjD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,EAAC,MAAM,EAAE,GAAG,IAAI,EAAC,CAAC,CACzD,CAAC;;;;YAKF,2CAA2C,CAAC,YAAY,CAAC,CAAC;;YAG1D,QAAQ,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,aAAa,CAAC,IAAI,EAAC,CAAC,CAAC;YAE1C,QAAQ,GAAG,MAAM,YAAY,CAAC;SAC/B;QAAC,OAAO,KAAU,EAAE;AACnB,YAAA,QAAQ,CAAC,KAAK,CACZ,IAAI,iBAAiB,CAAC;gBACpB,KAAK;AACL,gBAAA,MAAM,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC;gBACzB,UAAU,EAAE,KAAK,CAAC,UAAU;gBAC5B,GAAG,EAAE,OAAO,CAAC,aAAa;gBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;AACvB,aAAA,CAAC,CACH,CAAC;YACF,OAAO;SACR;QAED,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAClD,QAAA,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;QACvC,MAAM,GAAG,GAAGA,gBAAc,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,aAAa,CAAC;AAE9D,QAAA,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;QAC7B,IAAI,IAAI,GAAgD,IAAI,CAAC;AAE7D,QAAA,IAAI,OAAO,CAAC,cAAc,EAAE;AAC1B,YAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,EAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAC,CAAC,CAAC,CAAC;SAC3E;AAED,QAAA,IAAI,QAAQ,CAAC,IAAI,EAAE;;YAEjB,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAC7D,MAAM,MAAM,GAAiB,EAAE,CAAC;YAChC,MAAM,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACzC,IAAI,cAAc,GAAG,CAAC,CAAC;AAEvB,YAAA,IAAI,OAAoB,CAAC;AACzB,YAAA,IAAI,WAA+B,CAAC;;;YAIpC,MAAM,OAAO,GAAG,OAAO,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC;;;;YAK5D,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,YAAW;gBAC7C,OAAO,IAAI,EAAE;oBACX,MAAM,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;oBAE1C,IAAI,IAAI,EAAE;wBACR,MAAM;qBACP;AAED,oBAAA,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACnB,oBAAA,cAAc,IAAI,KAAK,CAAC,MAAM,CAAC;AAE/B,oBAAA,IAAI,OAAO,CAAC,cAAc,EAAE;wBAC1B,WAAW;4BACT,OAAO,CAAC,YAAY,KAAK,MAAM;AAC7B,kCAAE,CAAC,WAAW,IAAI,EAAE;AAClB,oCAAA,CAAC,OAAO,KAAK,IAAI,WAAW,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC;kCAC7D,SAAS,CAAC;wBAEhB,MAAM,cAAc,GAAG,MACrB,QAAQ,CAAC,IAAI,CAAC;4BACZ,IAAI,EAAE,aAAa,CAAC,gBAAgB;4BACpC,KAAK,EAAE,aAAa,GAAG,CAAC,aAAa,GAAG,SAAS;AACjD,4BAAA,MAAM,EAAE,cAAc;4BACtB,WAAW;AACiB,yBAAA,CAAC,CAAC;AAClC,wBAAA,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,cAAc,EAAE,CAAC;qBAC1D;iBACF;AACH,aAAC,CAAC,CAAC;;YAGH,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAC5D,YAAA,IAAI;AACF,gBAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;gBAC/D,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,CAAC,CAAC;aACxD;YAAC,OAAO,KAAK,EAAE;;AAEd,gBAAA,QAAQ,CAAC,KAAK,CACZ,IAAI,iBAAiB,CAAC;oBACpB,KAAK;AACL,oBAAA,OAAO,EAAE,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC;oBAC1C,MAAM,EAAE,QAAQ,CAAC,MAAM;oBACvB,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,GAAG,EAAEA,gBAAc,CAAC,QAAQ,CAAC,IAAI,OAAO,CAAC,aAAa;AACvD,iBAAA,CAAC,CACH,CAAC;gBACF,OAAO;aACR;SACF;;AAGD,QAAA,IAAI,MAAM,KAAK,CAAC,EAAE;YAChB,MAAM,GAAG,IAAI,GAAG,mBAAmB,GAAG,CAAC,CAAC;SACzC;;;;;QAMD,MAAM,EAAE,GAAG,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG,CAAC;QAEzC,IAAI,EAAE,EAAE;AACN,YAAA,QAAQ,CAAC,IAAI,CACX,IAAI,YAAY,CAAC;gBACf,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,GAAG;AACJ,aAAA,CAAC,CACH,CAAC;;;YAIF,QAAQ,CAAC,QAAQ,EAAE,CAAC;SACrB;aAAM;AACL,YAAA,QAAQ,CAAC,KAAK,CACZ,IAAI,iBAAiB,CAAC;AACpB,gBAAA,KAAK,EAAE,IAAI;gBACX,OAAO;gBACP,MAAM;gBACN,UAAU;gBACV,GAAG;AACJ,aAAA,CAAC,CACH,CAAC;SACH;KACF;AAEO,IAAA,SAAS,CACf,OAAyB,EACzB,UAAsB,EACtB,WAAmB,EAAA;AAEnB,QAAA,QAAQ,OAAO,CAAC,YAAY;AAC1B,YAAA,KAAK,MAAM;;AAET,gBAAA,MAAM,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,OAAO,CAACD,aAAW,EAAE,EAAE,CAAC,CAAC;AAC3E,gBAAA,OAAO,IAAI,KAAK,EAAE,GAAG,IAAI,GAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAY,CAAC;AAC3D,YAAA,KAAK,MAAM;gBACT,OAAO,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC9C,YAAA,KAAK,MAAM;AACT,gBAAA,OAAO,IAAI,IAAI,CAAC,CAAC,UAAU,CAAC,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,CAAC,CAAC;AACrD,YAAA,KAAK,aAAa;gBAChB,OAAO,UAAU,CAAC,MAAM,CAAC;SAC5B;KACF;AAEO,IAAA,iBAAiB,CAAC,GAAqB,EAAA;;QAG7C,MAAM,OAAO,GAA2B,EAAE,CAAC;AAC3C,QAAA,MAAM,WAAW,GAAmC,GAAG,CAAC,eAAe,GAAG,SAAS,GAAG,SAAS,CAAC;;QAGhG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,MAAM,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;QAG1E,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC9B,YAAA,OAAO,CAAC,QAAQ,CAAC,GAAG,mCAAmC,CAAC;SACzD;;QAGD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;AACpC,YAAA,MAAM,YAAY,GAAG,GAAG,CAAC,uBAAuB,EAAE,CAAC;;AAEnD,YAAA,IAAI,YAAY,KAAK,IAAI,EAAE;AACzB,gBAAA,OAAO,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC;aACxC;SACF;QAED,OAAO;AACL,YAAA,IAAI,EAAE,GAAG,CAAC,aAAa,EAAE;YACzB,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,OAAO;YACP,WAAW;SACZ,CAAC;KACH;IAEO,YAAY,CAAC,MAAoB,EAAE,WAAmB,EAAA;AAC5D,QAAA,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,CAAC;QAC9C,IAAI,QAAQ,GAAG,CAAC,CAAC;AACjB,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AAC1B,YAAA,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC/B,YAAA,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC;SAC1B;AAED,QAAA,OAAO,SAAS,CAAC;KAClB;yHAvOU,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAZ,YAAY,EAAA,CAAA,CAAA,EAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,UAAU;;AA2OX;;AAEG;MACmB,YAAY,CAAA;AAEjC,CAAA;AAED,SAAS,IAAI,MAAW;AAExB;;;;;AAKG;AACH,SAAS,2CAA2C,CAAC,OAAyB,EAAA;AAC5E,IAAA,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC3B;;ACvJA,SAAS,qBAAqB,CAC5B,GAAqB,EACrB,cAA6B,EAAA;AAE7B,IAAA,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC;AAC7B,CAAC;AAED;;;AAGG;AACH,SAAS,6BAA6B,CACpC,WAAsC,EACtC,WAA4B,EAAA;AAE5B,IAAA,OAAO,CAAC,cAAc,EAAE,cAAc,KACpC,WAAW,CAAC,SAAS,CAAC,cAAc,EAAE;QACpC,MAAM,EAAE,CAAC,iBAAiB,KAAK,WAAW,CAAC,iBAAiB,EAAE,cAAc,CAAC;AAC9E,KAAA,CAAC,CAAC;AACP,CAAC;AAED;;;AAGG;AACH,SAAS,oBAAoB,CAC3B,WAA0C,EAC1C,aAAgC,EAChC,QAA6B,EAAA;AAE7B,IAAA,OAAO,CAAC,cAAc,EAAE,cAAc,KACpC,qBAAqB,CAAC,QAAQ,EAAE,MAC9B,aAAa,CAAC,cAAc,EAAE,CAAC,iBAAiB,KAC9C,WAAW,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAC/C,CACF,CAAC;AACN,CAAC;AAED;;;;;AAKG;AACU,MAAA,iBAAiB,GAAG,IAAI,cAAc,CACjD,SAAS,GAAG,mBAAmB,GAAG,EAAE,EACpC;AAEF;;AAEG;AACI,MAAM,oBAAoB,GAAG,IAAI,cAAc,CACpD,SAAS,GAAG,sBAAsB,GAAG,EAAE,CACxC,CAAC;AAEF;;AAEG;AACU,MAAA,yBAAyB,GAAG,IAAI,cAAc,CACzD,SAAS,GAAG,2BAA2B,GAAG,EAAE,EAC5C;AAEF;AACA;AACA;AACO,MAAM,gCAAgC,GAAG,IAAI,cAAc,CAChE,SAAS,GAAG,kCAAkC,GAAG,EAAE,EACnD,EAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,IAAI,EAAC,EACzC;AAEF;;;AAGG;SACa,0BAA0B,GAAA;IACxC,IAAI,KAAK,GAAqC,IAAI,CAAC;AAEnD,IAAA,OAAO,CAAC,GAAG,EAAE,OAAO,KAAI;AACtB,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,YAAA,MAAM,YAAY,GAAG,MAAM,CAAC,iBAAiB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,IAAI,EAAE,CAAC;;;;;YAKvE,KAAK,GAAG,YAAY,CAAC,WAAW,CAC9B,6BAA6B,EAC7B,qBAAkD,CACnD,CAAC;SACH;AAED,QAAA,MAAM,YAAY,GAAG,MAAM,CAACE,aAAY,CAAC,CAAC;AAC1C,QAAA,MAAM,qBAAqB,GAAG,MAAM,CAAC,gCAAgC,CAAC,CAAC;QACvE,IAAI,qBAAqB,EAAE;AACzB,YAAA,MAAM,MAAM,GAAG,YAAY,CAAC,GAAG,EAAE,CAAC;YAClC,OAAO,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC9E;aAAM;AACL,YAAA,OAAO,KAAK,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;SAC5B;AACH,KAAC,CAAC;AACJ,CAAC;AAED,IAAI,4BAA4B,GAAG,KAAK,CAAC;AAEzC;SACgB,4BAA4B,GAAA;IAC1C,4BAA4B,GAAG,KAAK,CAAC;AACvC,CAAC;AAGK,MAAO,sBAAuB,SAAQ,WAAW,CAAA;IAKrD,WACU,CAAA,OAAoB,EACpB,QAA6B,EAAA;AAErC,QAAA,KAAK,EAAE,CAAC;QAHA,IAAO,CAAA,OAAA,GAAP,OAAO,CAAa;QACpB,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAqB;QAN/B,IAAK,CAAA,KAAA,GAAyC,IAAI,CAAC;AAC1C,QAAA,IAAA,CAAA,YAAY,GAAG,MAAM,CAACA,aAAY,CAAC,CAAC;AACpC,QAAA,IAAA,CAAA,qBAAqB,GAAG,MAAM,CAAC,gCAAgC,CAAC,CAAC;;;;AAWhF,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,CAAC,4BAA4B,EAAE;YACpF,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;YAC7D,IAAI,QAAQ,IAAI,EAAE,IAAI,CAAC,OAAO,YAAY,YAAY,CAAC,EAAE;gBACvD,4BAA4B,GAAG,IAAI,CAAC;gBACpC,QAAQ;qBACL,GAAG,CAACC,QAAO,CAAC;qBACZ,IAAI,CACHC,mBAAkB,CAAA,IAAA,wDAEhB,uDAAuD;oBACrD,oDAAoD;oBACpD,iEAAiE;oBACjE,4CAA4C;oBAC5C,wEAAwE;oBACxE,sCAAsC,CACzC,CACF,CAAC;aACL;SACF;KACF;AAEQ,IAAA,MAAM,CAAC,cAAgC,EAAA;AAC9C,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;YACvB,MAAM,qBAAqB,GAAG,KAAK,CAAC,IAAI,CACtC,IAAI,GAAG,CAAC;AACN,gBAAA,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,oBAAoB,CAAC;gBAC1C,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,yBAAyB,EAAE,EAAE,CAAC;AACpD,aAAA,CAAC,CACH,CAAC;;;;;YAMF,IAAI,CAAC,KAAK,GAAG,qBAAqB,CAAC,WAAW,CAC5C,CAAC,eAAe,EAAE,aAAa,KAC7B,oBAAoB,CAAC,eAAe,EAAE,aAAa,EAAE,IAAI,CAAC,QAAQ,CAAC,EACrE,qBAAsD,CACvD,CAAC;SACH;AAED,QAAA,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;AACvC,YAAA,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,iBAAiB,KAClD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CACvC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;SAC1D;aAAM;YACL,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,iBAAiB,KAClD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,CACvC,CAAC;SACH;KACF;yHAjEU,sBAAsB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,WAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAtB,sBAAsB,EAAA,CAAA,CAAA,EAAA;;sGAAtB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBADlC,UAAU;;;ACxOX;AACA;AACA;AACA;AACA,IAAI,aAAa,GAAW,CAAC,CAAC;AAE9B;;;AAGG;AACH,IAAI,eAAqC,CAAC;AAE1C;AACA;AACO,MAAM,qBAAqB,GAAG,gDAAgD,CAAC;AAEtF;AACA;AACO,MAAM,sBAAsB,GAAG,+CAA+C,CAAC;AAC/E,MAAM,6BAA6B,GAAG,6CAA6C,CAAC;AAE3F;AACA;AACO,MAAM,+BAA+B,GAAG,wCAAwC,CAAC;AAExF;;;;;;AAMG;MACmB,oBAAoB,CAAA;AAEzC,CAAA;AAED;;;;;;;AAOG;SACa,oBAAoB,GAAA;AAClC,IAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,QAAA,OAAO,MAAM,CAAC;KACf;AACD,IAAA,OAAO,EAAE,CAAC;AACZ,CAAC;AAED;;;;;;;AAOG;MAEU,kBAAkB,CAAA;IAM7B,WACU,CAAA,WAAiC,EACf,QAAa,EAAA;QAD/B,IAAW,CAAA,WAAA,GAAX,WAAW,CAAsB;QACf,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAK;AAPzC;;AAEG;AACc,QAAA,IAAA,CAAA,eAAe,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;KAKjD;AAEJ;;AAEG;IACK,YAAY,GAAA;AAClB,QAAA,OAAO,CAAqB,kBAAA,EAAA,aAAa,EAAE,CAAA,CAAE,CAAC;KAC/C;AAED;;;;;AAKG;AACH,IAAA,MAAM,CAAC,GAAuB,EAAA;;;AAG5B,QAAA,IAAI,GAAG,CAAC,MAAM,KAAK,OAAO,EAAE;AAC1B,YAAA,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;SACzC;AAAM,aAAA,IAAI,GAAG,CAAC,YAAY,KAAK,MAAM,EAAE;AACtC,YAAA,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;;;QAID,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,YAAA,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;SAClD;;AAGD,QAAA,OAAO,IAAI,UAAU,CAAiB,CAAC,QAAkC,KAAI;;;;AAI3E,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;AACrC,YAAA,MAAM,GAAG,GAAG,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAA,CAAA,EAAI,QAAQ,CAAA,EAAA,CAAI,CAAC,CAAC;;YAGhF,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AACnD,YAAA,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;;;;YAMf,IAAI,IAAI,GAAe,IAAI,CAAC;;YAG5B,IAAI,QAAQ,GAAY,KAAK,CAAC;;;;YAK9B,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAU,KAAI;;AAE1C,gBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;;gBAGlC,IAAI,GAAG,IAAI,CAAC;gBACZ,QAAQ,GAAG,IAAI,CAAC;AAClB,aAAC,CAAC;;;;YAKF,MAAM,OAAO,GAAG,MAAK;AACnB,gBAAA,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACzC,gBAAA,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;gBAG3C,IAAI,CAAC,MAAM,EAAE,CAAC;;;AAId,gBAAA,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACpC,aAAC,CAAC;;;;;AAMF,YAAA,MAAM,MAAM,GAAG,CAAC,KAAY,KAAI;;;;AAI9B,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAK;;AAE7B,oBAAA,OAAO,EAAE,CAAC;;oBAGV,IAAI,CAAC,QAAQ,EAAE;;;AAGb,wBAAA,QAAQ,CAAC,KAAK,CACZ,IAAI,iBAAiB,CAAC;4BACpB,GAAG;AACH,4BAAA,MAAM,EAAE,CAAC;AACT,4BAAA,UAAU,EAAE,aAAa;AACzB,4BAAA,KAAK,EAAE,IAAI,KAAK,CAAC,qBAAqB,CAAC;AACxC,yBAAA,CAAC,CACH,CAAC;wBACF,OAAO;qBACR;;;AAID,oBAAA,QAAQ,CAAC,IAAI,CACX,IAAI,YAAY,CAAC;wBACf,IAAI;AACJ,wBAAA,MAAM,EAAE,mBAAmB;AAC3B,wBAAA,UAAU,EAAE,IAAI;wBAChB,GAAG;AACJ,qBAAA,CAAC,CACH,CAAC;;oBAGF,QAAQ,CAAC,QAAQ,EAAE,CAAC;AACtB,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC;;;;AAKF,YAAA,MAAM,OAAO,GAAQ,CAAC,KAAY,KAAI;AACpC,gBAAA,OAAO,EAAE,CAAC;;AAGV,gBAAA,QAAQ,CAAC,KAAK,CACZ,IAAI,iBAAiB,CAAC;oBACpB,KAAK;AACL,oBAAA,MAAM,EAAE,CAAC;AACT,oBAAA,UAAU,EAAE,aAAa;oBACzB,GAAG;AACJ,iBAAA,CAAC,CACH,CAAC;AACJ,aAAC,CAAC;;;AAIF,YAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACtC,YAAA,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACxC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;;YAGrC,QAAQ,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,aAAa,CAAC,IAAI,EAAC,CAAC,CAAC;;AAG1C,YAAA,OAAO,MAAK;gBACV,IAAI,CAAC,QAAQ,EAAE;AACb,oBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;iBAC5B;;AAGD,gBAAA,OAAO,EAAE,CAAC;AACZ,aAAC,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AAEO,IAAA,eAAe,CAAC,MAAyB,EAAA;;;;QAI/C,eAAe,KAAM,IAAI,CAAC,QAAQ,CAAC,cAAoC,CAAC,kBAAkB,EAAE,CAAC;AAE7F,QAAA,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;KACnC;AA/KU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,mDAQnB,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HARP,kBAAkB,EAAA,CAAA,CAAA,EAAA;;sGAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B,UAAU;;0BASN,MAAM;2BAAC,QAAQ,CAAA;;AA0KpB;;AAEG;AACa,SAAA,kBAAkB,CAChC,GAAyB,EACzB,IAAmB,EAAA;AAEnB,IAAA,IAAI,GAAG,CAAC,MAAM,KAAK,OAAO,EAAE;QAC1B,OAAO,MAAM,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,GAAyB,CAAC,CAAC;KACrE;;AAGD,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,CAAC;AAED;;;;;;;AAOG;MAEU,gBAAgB,CAAA;AAC3B,IAAA,WAAA,CAAoB,QAA6B,EAAA;QAA7B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAqB;KAAI;AAErD;;;;;;AAMG;IACH,SAAS,CAAC,cAAgC,EAAE,IAAiB,EAAA;QAC3D,OAAO,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAC1C,kBAAkB,CAAC,cAAc,EAAE,CAAC,iBAAiB,KAAK,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAC1F,CAAC;KACH;yHAdU,gBAAgB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAhB,gBAAgB,EAAA,CAAA,CAAA,EAAA;;sGAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;;;ACpQX,MAAM,WAAW,GAAG,cAAc,CAAC;AAEnC;;;AAGG;AACH,SAAS,cAAc,CAAC,GAAQ,EAAA;IAC9B,IAAI,aAAa,IAAI,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE;QAC3C,OAAO,GAAG,CAAC,WAAW,CAAC;KACxB;IACD,IAAI,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC,EAAE;AACxD,QAAA,OAAO,GAAG,CAAC,iBAAiB,CAAC,eAAe,CAAC,CAAC;KAC/C;AACD,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;;;;;AAMG;MAEU,cAAc,CAAA;AACzB,IAAA,WAAA,CAAoB,UAAsB,EAAA;QAAtB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAY;KAAI;AAE9C;;;;AAIG;AACH,IAAA,MAAM,CAAC,GAAqB,EAAA;;;AAG1B,QAAA,IAAI,GAAG,CAAC,MAAM,KAAK,OAAO,EAAE;YAC1B,MAAM,IAAIC,aAAY,CAAA,CAAA,IAAA,8CAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,gBAAA,CAAA,oNAAA,CAAsN,CACzN,CAAC;SACH;;;;AAKD,QAAA,MAAM,UAAU,GAAmD,IAAI,CAAC,UAAU,CAAC;AACnF,QAAA,MAAM,MAAM,GAA4B,UAAU,CAAC,SAAS;AAC1D,cAAE,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;AAC9B,cAAE,EAAE,CAAC,IAAI,CAAC,CAAC;AAEb,QAAA,OAAO,MAAM,CAAC,IAAI,CAChB,SAAS,CAAC,MAAK;;AAEb,YAAA,OAAO,IAAI,UAAU,CAAC,CAAC,QAAkC,KAAI;;;AAG3D,gBAAA,MAAM,GAAG,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;gBAC/B,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;AACxC,gBAAA,IAAI,GAAG,CAAC,eAAe,EAAE;AACvB,oBAAA,GAAG,CAAC,eAAe,GAAG,IAAI,CAAC;iBAC5B;;gBAGD,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,MAAM,KAAK,GAAG,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;gBAGpF,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;AAC9B,oBAAA,GAAG,CAAC,gBAAgB,CAAC,QAAQ,EAAE,mCAAmC,CAAC,CAAC;iBACrE;;gBAGD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;AACpC,oBAAA,MAAM,YAAY,GAAG,GAAG,CAAC,uBAAuB,EAAE,CAAC;;AAEnD,oBAAA,IAAI,YAAY,KAAK,IAAI,EAAE;AACzB,wBAAA,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;qBACpD;iBACF;;AAGD,gBAAA,IAAI,GAAG,CAAC,YAAY,EAAE;oBACpB,MAAM,YAAY,GAAG,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC;;;;;;AAOpD,oBAAA,GAAG,CAAC,YAAY,IAAI,YAAY,KAAK,MAAM,GAAG,YAAY,GAAG,MAAM,CAAQ,CAAC;iBAC7E;;AAGD,gBAAA,MAAM,OAAO,GAAG,GAAG,CAAC,aAAa,EAAE,CAAC;;;;;;;gBAQpC,IAAI,cAAc,GAA8B,IAAI,CAAC;;;gBAIrD,MAAM,cAAc,GAAG,MAAyB;AAC9C,oBAAA,IAAI,cAAc,KAAK,IAAI,EAAE;AAC3B,wBAAA,OAAO,cAAc,CAAC;qBACvB;AAED,oBAAA,MAAM,UAAU,GAAG,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC;;oBAG1C,MAAM,OAAO,GAAG,IAAI,WAAW,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC,CAAC;;;oBAI7D,MAAM,GAAG,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC;;AAG3C,oBAAA,cAAc,GAAG,IAAI,kBAAkB,CAAC,EAAC,OAAO,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,GAAG,EAAC,CAAC,CAAC;AACxF,oBAAA,OAAO,cAAc,CAAC;AACxB,iBAAC,CAAC;;;;gBAMF,MAAM,MAAM,GAAG,MAAK;;AAElB,oBAAA,IAAI,EAAC,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,EAAC,GAAG,cAAc,EAAE,CAAC;;oBAG1D,IAAI,IAAI,GAAe,IAAI,CAAC;AAE5B,oBAAA,IAAI,MAAM,KAAK,2BAA2B,EAAE;;AAE1C,wBAAA,IAAI,GAAG,OAAO,GAAG,CAAC,QAAQ,KAAK,WAAW,GAAG,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,QAAQ,CAAC;qBAC9E;;AAGD,oBAAA,IAAI,MAAM,KAAK,CAAC,EAAE;AAChB,wBAAA,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,mBAAmB,GAAG,CAAC,CAAC;qBAC3C;;;;;oBAMD,IAAI,EAAE,GAAG,MAAM,IAAI,GAAG,IAAI,MAAM,GAAG,GAAG,CAAC;;;oBAIvC,IAAI,GAAG,CAAC,YAAY,KAAK,MAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;;wBAE3D,MAAM,YAAY,GAAG,IAAI,CAAC;wBAC1B,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACrC,wBAAA,IAAI;;;AAGF,4BAAA,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;yBAC9C;wBAAC,OAAO,KAAK,EAAE;;;;4BAId,IAAI,GAAG,YAAY,CAAC;;;4BAIpB,IAAI,EAAE,EAAE;;gCAEN,EAAE,GAAG,KAAK,CAAC;;gCAEX,IAAI,GAAG,EAAC,KAAK,EAAE,IAAI,EAAE,IAAI,EAAuB,CAAC;6BAClD;yBACF;qBACF;oBAED,IAAI,EAAE,EAAE;;AAEN,wBAAA,QAAQ,CAAC,IAAI,CACX,IAAI,YAAY,CAAC;4BACf,IAAI;4BACJ,OAAO;4BACP,MAAM;4BACN,UAAU;4BACV,GAAG,EAAE,GAAG,IAAI,SAAS;AACtB,yBAAA,CAAC,CACH,CAAC;;;wBAGF,QAAQ,CAAC,QAAQ,EAAE,CAAC;qBACrB;yBAAM;;AAEL,wBAAA,QAAQ,CAAC,KAAK,CACZ,IAAI,iBAAiB,CAAC;;AAEpB,4BAAA,KAAK,EAAE,IAAI;4BACX,OAAO;4BACP,MAAM;4BACN,UAAU;4BACV,GAAG,EAAE,GAAG,IAAI,SAAS;AACtB,yBAAA,CAAC,CACH,CAAC;qBACH;AACH,iBAAC,CAAC;;;;AAKF,gBAAA,MAAM,OAAO,GAAG,CAAC,KAAoB,KAAI;AACvC,oBAAA,MAAM,EAAC,GAAG,EAAC,GAAG,cAAc,EAAE,CAAC;AAC/B,oBAAA,MAAM,GAAG,GAAG,IAAI,iBAAiB,CAAC;wBAChC,KAAK;AACL,wBAAA,MAAM,EAAE,GAAG,CAAC,MAAM,IAAI,CAAC;AACvB,wBAAA,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,eAAe;wBAC7C,GAAG,EAAE,GAAG,IAAI,SAAS;AACtB,qBAAA,CAAC,CAAC;AACH,oBAAA,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACtB,iBAAC,CAAC;;;;;gBAMF,IAAI,WAAW,GAAG,KAAK,CAAC;;;AAIxB,gBAAA,MAAM,cAAc,GAAG,CAAC,KAAoB,KAAI;;oBAE9C,IAAI,CAAC,WAAW,EAAE;AAChB,wBAAA,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;wBAChC,WAAW,GAAG,IAAI,CAAC;qBACpB;;;AAID,oBAAA,IAAI,aAAa,GAA8B;wBAC7C,IAAI,EAAE,aAAa,CAAC,gBAAgB;wBACpC,MAAM,EAAE,KAAK,CAAC,MAAM;qBACrB,CAAC;;AAGF,oBAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAC1B,wBAAA,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;qBACnC;;;;AAKD,oBAAA,IAAI,GAAG,CAAC,YAAY,KAAK,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,YAAY,EAAE;AACrD,wBAAA,aAAa,CAAC,WAAW,GAAG,GAAG,CAAC,YAAY,CAAC;qBAC9C;;AAGD,oBAAA,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC/B,iBAAC,CAAC;;;AAIF,gBAAA,MAAM,YAAY,GAAG,CAAC,KAAoB,KAAI;;;AAG5C,oBAAA,IAAI,QAAQ,GAA4B;wBACtC,IAAI,EAAE,aAAa,CAAC,cAAc;wBAClC,MAAM,EAAE,KAAK,CAAC,MAAM;qBACrB,CAAC;;;AAIF,oBAAA,IAAI,KAAK,CAAC,gBAAgB,EAAE;AAC1B,wBAAA,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;qBAC9B;;AAGD,oBAAA,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1B,iBAAC,CAAC;;AAGF,gBAAA,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACrC,gBAAA,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACvC,gBAAA,GAAG,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AACzC,gBAAA,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;;AAGvC,gBAAA,IAAI,GAAG,CAAC,cAAc,EAAE;;AAEtB,oBAAA,GAAG,CAAC,gBAAgB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;;oBAGjD,IAAI,OAAO,KAAK,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE;wBAClC,GAAG,CAAC,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;qBACvD;iBACF;;AAGD,gBAAA,GAAG,CAAC,IAAI,CAAC,OAAQ,CAAC,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,EAAC,IAAI,EAAE,aAAa,CAAC,IAAI,EAAC,CAAC,CAAC;;;AAG1C,gBAAA,OAAO,MAAK;;AAEV,oBAAA,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC1C,oBAAA,GAAG,CAAC,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC1C,oBAAA,GAAG,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AACxC,oBAAA,GAAG,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;AAE5C,oBAAA,IAAI,GAAG,CAAC,cAAc,EAAE;AACtB,wBAAA,GAAG,CAAC,mBAAmB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;wBACpD,IAAI,OAAO,KAAK,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE;4BAClC,GAAG,CAAC,MAAM,CAAC,mBAAmB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;yBAC1D;qBACF;;oBAGD,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,IAAI,EAAE;wBAC/B,GAAG,CAAC,KAAK,EAAE,CAAC;qBACb;AACH,iBAAC,CAAC;AACJ,aAAC,CAAC,CAAC;SACJ,CAAC,CACH,CAAC;KACH;yHA3SU,cAAc,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,UAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAd,cAAc,EAAA,CAAA,CAAA,EAAA;;sGAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,UAAU;;;AC5BJ,MAAM,YAAY,GAAG,IAAI,cAAc,CAAU,SAAS,GAAG,cAAc,GAAG,EAAE,CAAC,CAAC;AAElF,MAAM,wBAAwB,GAAG,YAAY,CAAC;AAC9C,MAAM,gBAAgB,GAAG,IAAI,cAAc,CAAS,SAAS,GAAG,kBAAkB,GAAG,EAAE,EAAE;AAC9F,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,wBAAwB;AACxC,CAAA,CAAC,CAAC;AAEI,MAAM,wBAAwB,GAAG,cAAc,CAAC;AAChD,MAAM,gBAAgB,GAAG,IAAI,cAAc,CAAS,SAAS,GAAG,kBAAkB,GAAG,EAAE,EAAE;AAC9F,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,wBAAwB;AACxC,CAAA,CAAC,CAAC;AAEH;;;;AAIG;MACmB,sBAAsB,CAAA;AAO3C,CAAA;AAED;;AAEG;MAEU,uBAAuB,CAAA;AASlC,IAAA,WAAA,CAC4B,GAAQ,EACL,QAAgB,EACX,UAAkB,EAAA;QAF1B,IAAG,CAAA,GAAA,GAAH,GAAG,CAAK;QACL,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;QACX,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;QAX9C,IAAgB,CAAA,gBAAA,GAAW,EAAE,CAAC;QAC9B,IAAS,CAAA,SAAA,GAAkB,IAAI,CAAC;AAExC;;AAEG;QACH,IAAU,CAAA,UAAA,GAAW,CAAC,CAAC;KAMnB;IAEJ,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC;SACb;QACD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,EAAE,CAAC;AAC3C,QAAA,IAAI,YAAY,KAAK,IAAI,CAAC,gBAAgB,EAAE;YAC1C,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,CAAC,SAAS,GAAGC,iBAAgB,CAAC,YAAY,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;AACjE,YAAA,IAAI,CAAC,gBAAgB,GAAG,YAAY,CAAC;SACtC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB;AA1BU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,EAUxB,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,QAAQ,EACR,EAAA,EAAA,KAAA,EAAA,WAAW,aACX,gBAAgB,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAZf,uBAAuB,EAAA,CAAA,CAAA,EAAA;;sGAAvB,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBADnC,UAAU;;0BAWN,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;;0BAClB,MAAM;2BAAC,gBAAgB,CAAA;;AAiBZ,SAAA,iBAAiB,CAC/B,GAAyB,EACzB,IAAmB,EAAA;IAEnB,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;;;;;AAKpC,IAAA,IACE,CAAC,MAAM,CAAC,YAAY,CAAC;QACrB,GAAG,CAAC,MAAM,KAAK,KAAK;QACpB,GAAG,CAAC,MAAM,KAAK,MAAM;AACrB,QAAA,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC;AAC3B,QAAA,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,EAC5B;AACA,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;KAClB;IAED,MAAM,KAAK,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC,QAAQ,EAAE,CAAC;AACxD,IAAA,MAAM,UAAU,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAC;;AAG5C,IAAA,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;QACjD,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,EAAC,OAAO,EAAE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,KAAK,CAAC,EAAC,CAAC,CAAC;KAChE;AACD,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;AACnB,CAAC;AAED;;AAEG;MAEU,mBAAmB,CAAA;AAC9B,IAAA,WAAA,CAAoB,QAA6B,EAAA;QAA7B,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAqB;KAAI;IAErD,SAAS,CAAC,cAAgC,EAAE,IAAiB,EAAA;QAC3D,OAAO,qBAAqB,CAAC,IAAI,CAAC,QAAQ,EAAE,MAC1C,iBAAiB,CAAC,cAAc,EAAE,CAAC,iBAAiB,KAAK,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC,CACzF,CAAC;KACH;yHAPU,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,EAAA,CAAA,mBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAnB,mBAAmB,EAAA,CAAA,CAAA,EAAA;;sGAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B,UAAU;;;AC7EX;;;;AAIG;IACS,gBAQX;AARD,CAAA,UAAY,eAAe,EAAA;AACzB,IAAA,eAAA,CAAA,eAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY,CAAA;AACZ,IAAA,eAAA,CAAA,eAAA,CAAA,oBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,oBAAkB,CAAA;AAClB,IAAA,eAAA,CAAA,eAAA,CAAA,yBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,yBAAuB,CAAA;AACvB,IAAA,eAAA,CAAA,eAAA,CAAA,kBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,kBAAgB,CAAA;AAChB,IAAA,eAAA,CAAA,eAAA,CAAA,cAAA,CAAA,GAAA,CAAA,CAAA,GAAA,cAAY,CAAA;AACZ,IAAA,eAAA,CAAA,eAAA,CAAA,uBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,uBAAqB,CAAA;AACrB,IAAA,eAAA,CAAA,eAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK,CAAA;AACP,CAAC,EARW,eAAe,KAAf,eAAe,GAQ1B,EAAA,CAAA,CAAA,CAAA;AAYD,SAAS,eAAe,CACtB,IAAW,EACX,SAAqB,EAAA;IAErB,OAAO;AACL,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,UAAU,EAAE,SAAS;KACtB,CAAC;AACJ,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACa,SAAA,iBAAiB,CAC/B,GAAG,QAAwC,EAAA;IAE3C,IAAI,SAAS,EAAE;AACb,QAAA,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;AAC3D,QAAA,IACE,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,gBAAgB,CAAC;YAClD,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,uBAAuB,CAAC,EACzD;YACA,MAAM,IAAI,KAAK,CACb,SAAS;AACP,kBAAE,CAAuJ,qJAAA,CAAA;kBACvJ,EAAE,CACP,CAAC;SACH;KACF;AAED,IAAA,MAAM,SAAS,GAAe;QAC5B,UAAU;QACV,cAAc;QACd,sBAAsB;AACtB,QAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,sBAAsB,EAAC;AAC3D,QAAA;AACE,YAAA,OAAO,EAAE,WAAW;YACpB,UAAU,EAAE,MAAK;AACf,gBAAA,OAAO,MAAM,CAAC,YAAY,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,IAAI,MAAM,CAAC,cAAc,CAAC,CAAC;aACzE;AACF,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,oBAAoB;AAC7B,YAAA,QAAQ,EAAE,iBAAiB;AAC3B,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA;AACD,QAAA,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAC;AACvC,QAAA,EAAC,OAAO,EAAE,sBAAsB,EAAE,QAAQ,EAAE,uBAAuB,EAAC;KACrE,CAAC;AAEF,IAAA,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;QAC9B,SAAS,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;KACvC;AAED,IAAA,OAAO,wBAAwB,CAAC,SAAS,CAAC,CAAC;AAC7C,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,gBAAgB,CAC9B,cAAmC,EAAA;AAEnC,IAAA,OAAO,eAAe,CACpB,eAAe,CAAC,YAAY,EAC5B,cAAc,CAAC,GAAG,CAAC,CAAC,aAAa,KAAI;QACnC,OAAO;AACL,YAAA,OAAO,EAAE,oBAAoB;AAC7B,YAAA,QAAQ,EAAE,aAAa;AACvB,YAAA,KAAK,EAAE,IAAI;SACZ,CAAC;KACH,CAAC,CACH,CAAC;AACJ,CAAC;AAED,MAAM,qBAAqB,GAAG,IAAI,cAAc,CAC9C,SAAS,GAAG,uBAAuB,GAAG,EAAE,CACzC,CAAC;AAEF;;;;;;;;;;AAUG;SACa,sBAAsB,GAAA;;;;;;AAMpC,IAAA,OAAO,eAAe,CAAC,eAAe,CAAC,kBAAkB,EAAE;AACzD,QAAA;AACE,YAAA,OAAO,EAAE,qBAAqB;AAC9B,YAAA,UAAU,EAAE,0BAA0B;AACvC,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,oBAAoB;AAC7B,YAAA,WAAW,EAAE,qBAAqB;AAClC,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA;AACF,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;;AAMG;SACa,qBAAqB,CAAC,EACpC,UAAU,EACV,UAAU,GAIX,EAAA;IACC,MAAM,SAAS,GAAe,EAAE,CAAC;AACjC,IAAA,IAAI,UAAU,KAAK,SAAS,EAAE;AAC5B,QAAA,SAAS,CAAC,IAAI,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,UAAU,EAAC,CAAC,CAAC;KACnE;AACD,IAAA,IAAI,UAAU,KAAK,SAAS,EAAE;AAC5B,QAAA,SAAS,CAAC,IAAI,CAAC,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,UAAU,EAAC,CAAC,CAAC;KACnE;IAED,OAAO,eAAe,CAAC,eAAe,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC;AAC7E,CAAC;AAED;;;;;;AAMG;SACa,oBAAoB,GAAA;AAClC,IAAA,OAAO,eAAe,CAAC,eAAe,CAAC,gBAAgB,EAAE;AACvD,QAAA;AACE,YAAA,OAAO,EAAE,YAAY;AACrB,YAAA,QAAQ,EAAE,KAAK;AAChB,SAAA;AACF,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;AAIG;SACa,gBAAgB,GAAA;AAC9B,IAAA,OAAO,eAAe,CAAC,eAAe,CAAC,YAAY,EAAE;QACnD,kBAAkB;AAClB,QAAA,EAAC,OAAO,EAAE,oBAAoB,EAAE,UAAU,EAAE,oBAAoB,EAAC;QACjE,EAAC,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,kBAAkB,EAAE,KAAK,EAAE,IAAI,EAAC;AAC3E,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;;;;;;;AAmBG;SACa,yBAAyB,GAAA;AACvC,IAAA,OAAO,eAAe,CAAC,eAAe,CAAC,qBAAqB,EAAE;AAC5D,QAAA;AACE,YAAA,OAAO,EAAE,WAAW;YACpB,UAAU,EAAE,MAAK;AACf,gBAAA,MAAM,iBAAiB,GAAG,MAAM,CAAC,WAAW,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC;AAChF,gBAAA,IAAI,SAAS,IAAI,iBAAiB,KAAK,IAAI,EAAE;AAC3C,oBAAA,MAAM,IAAI,KAAK,CACb,kGAAkG,CACnG,CAAC;iBACH;AACD,gBAAA,OAAO,iBAAiB,CAAC;aAC1B;AACF,SAAA;AACF,KAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;;AAMG;SACa,SAAS,GAAA;AACvB,IAAA,OAAO,eAAe,CAAC,eAAe,CAAC,KAAK,EAAE;QAC5C,YAAY;AACZ,QAAA,EAAC,OAAO,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAC;AAClD,KAAA,CAAC,CAAC;AACL;;AC1RA;;;;;;;;;;;;;AAaG;MAaU,oBAAoB,CAAA;AAC/B;;AAEG;AACH,IAAA,OAAO,OAAO,GAAA;QACZ,OAAO;AACL,YAAA,QAAQ,EAAE,oBAAoB;AAC9B,YAAA,SAAS,EAAE,CAAC,oBAAoB,EAAE,CAAC,UAAU,CAAC;SAC/C,CAAC;KACH;AAED;;;;;;;AAOG;AACH,IAAA,OAAO,WAAW,CAChB,OAAA,GAGI,EAAE,EAAA;QAEN,OAAO;AACL,YAAA,QAAQ,EAAE,oBAAoB;AAC9B,YAAA,SAAS,EAAE,qBAAqB,CAAC,OAAO,CAAC,CAAC,UAAU;SACrD,CAAC;KACH;yHA7BU,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;0HAApB,oBAAoB,EAAA,CAAA,CAAA,EAAA;AAApB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EAXpB,SAAA,EAAA;YACT,mBAAmB;YACnB,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAC;AAC3E,YAAA,EAAC,OAAO,EAAE,sBAAsB,EAAE,QAAQ,EAAE,uBAAuB,EAAC;AACpE,YAAA,qBAAqB,CAAC;AACpB,gBAAA,UAAU,EAAE,wBAAwB;AACpC,gBAAA,UAAU,EAAE,wBAAwB;AACrC,aAAA,CAAC,CAAC,UAAU;AACb,YAAA,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAC;AACxC,SAAA,EAAA,CAAA,CAAA,EAAA;;sGAEU,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAZhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE;wBACT,mBAAmB;wBACnB,EAAC,OAAO,EAAE,iBAAiB,EAAE,WAAW,EAAE,mBAAmB,EAAE,KAAK,EAAE,IAAI,EAAC;AAC3E,wBAAA,EAAC,OAAO,EAAE,sBAAsB,EAAE,QAAQ,EAAE,uBAAuB,EAAC;AACpE,wBAAA,qBAAqB,CAAC;AACpB,4BAAA,UAAU,EAAE,wBAAwB;AACpC,4BAAA,UAAU,EAAE,wBAAwB;AACrC,yBAAA,CAAC,CAAC,UAAU;AACb,wBAAA,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,EAAC;AACxC,qBAAA;AACF,iBAAA,CAAA;;AAiCD;;;;;;;;;AASG;MAQU,gBAAgB,CAAA;yHAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;0HAAhB,gBAAgB,EAAA,CAAA,CAAA,EAAA;AAAhB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,aAFhB,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,CAAC,CAAC,EAAA,CAAA,CAAA,EAAA;;sGAE7C,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAP5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR;;;AAGG;AACH,oBAAA,SAAS,EAAE,CAAC,iBAAiB,CAAC,sBAAsB,EAAE,CAAC,CAAC;AACzD,iBAAA,CAAA;;AAGD;;;;;;;;AAQG;MAIU,qBAAqB,CAAA;yHAArB,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;0HAArB,qBAAqB,EAAA,CAAA,CAAA,EAAA;AAArB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,qBAAqB,aAFrB,CAAC,gBAAgB,EAAE,CAAC,UAAU,CAAC,EAAA,CAAA,CAAA,EAAA;;sGAE/B,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAHjC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC,UAAU,CAAC;AAC3C,iBAAA,CAAA;;;AC1DD;;;;;;;;;;;;;;;;;;;;;;;;;AAyBG;AACU,MAAA,8BAA8B,GAAG,IAAI,cAAc,CAC9D,SAAS,GAAG,gCAAgC,GAAG,EAAE,EACjD;AAEF;;AAEG;AAEI,MAAM,IAAI,GAAG,GAAG,CAAC;AACjB,MAAM,OAAO,GAAG,GAAG,CAAC;AACpB,MAAM,MAAM,GAAG,GAAG,CAAC;AACnB,MAAM,WAAW,GAAG,IAAI,CAAC;AACzB,MAAM,OAAO,GAAG,GAAG,CAAC;AACpB,MAAM,aAAa,GAAG,IAAI,CAAC;AAqBlC,MAAM,aAAa,GAAG,IAAI,cAAc,CACtC,SAAS,GAAG,mCAAmC,GAAG,EAAE,CACrD,CAAC;AAEF;;AAEG;AACH,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAExB,SAAA,0BAA0B,CACxC,GAAyB,EACzB,IAAmB,EAAA;IAEnB,MAAM,EAAC,aAAa,EAAE,GAAG,aAAa,EAAC,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;IAChE,MAAM,EAAC,aAAa,EAAE,cAAc,EAAE,MAAM,EAAE,aAAa,EAAC,GAAG,GAAG,CAAC;;AAGnE,IAAA,IACE,CAAC,aAAa;AACd,QAAA,cAAc,KAAK,KAAK;;SAEvB,aAAa,KAAK,MAAM,IAAI,CAAC,aAAa,CAAC,mBAAmB,IAAI,CAAC,cAAc,CAAC;SAClF,aAAa,KAAK,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;;SAErE,CAAC,aAAa,CAAC,8BAA8B,IAAI,cAAc,CAAC,GAAG,CAAC,CAAC;QACtE,aAAa,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,KAAK,EACrC;AACA,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;KAClB;AAED,IAAA,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAE5C,IAAA,MAAM,SAAS,GAAkC,MAAM,CAAC,8BAA8B,EAAE;AACtF,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC,CAAC;IACH,MAAM,QAAQ,GAAG,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC;AACvD,IAAA,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;QAC1B,MAAM,IAAID,aAAY,CAAA,IAAA,wDAEpB,SAAS;YACP,qFAAqF;gBACnF,yFAAyF;AACzF,gBAAA,iCAAiC,CACtC,CAAC;KACH;IAED,MAAM,UAAU,GAAG,QAAQ,IAAI,SAAS,GAAG,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC;IAE7F,MAAM,QAAQ,GAAG,YAAY,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;IAC/C,MAAM,QAAQ,GAAG,aAAa,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAEnD,IAAA,IAAI,gBAAgB,GAAG,aAAa,CAAC,cAAc,CAAC;IACpD,IAAI,OAAO,cAAc,KAAK,QAAQ,IAAI,cAAc,CAAC,cAAc,EAAE;;AAEvE,QAAA,gBAAgB,GAAG,cAAc,CAAC,cAAc,CAAC;KAClD;IAED,IAAI,QAAQ,EAAE;AACZ,QAAA,MAAM,EACJ,CAAC,IAAI,GAAG,aAAa,EACrB,CAAC,aAAa,GAAG,YAAY,EAC7B,CAAC,OAAO,GAAG,WAAW,EACtB,CAAC,MAAM,GAAG,MAAM,EAChB,CAAC,WAAW,GAAG,UAAU,EACzB,CAAC,OAAO,GAAG,GAAG,GACf,GAAG,QAAQ,CAAC;;QAEb,IAAI,IAAI,GAA4C,aAAa,CAAC;QAElE,QAAQ,YAAY;AAClB,YAAA,KAAK,aAAa;gBAChB,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC;gBACtD,MAAM;AACR,YAAA,KAAK,MAAM;gBACT,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC;gBACjC,MAAM;SACT;;;;AAKD,QAAA,IAAI,OAAO,GAAG,IAAI,WAAW,CAAC,WAAW,CAAC,CAAC;AAC3C,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;;;;AAIjD,YAAA,OAAO,GAAG,6BAA6B,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,gBAAgB,IAAI,EAAE,CAAC,CAAC;SACnF;AAED,QAAA,OAAO,EAAE,CACP,IAAI,YAAY,CAAC;YACf,IAAI;YACJ,OAAO;YACP,MAAM;YACN,UAAU;YACV,GAAG;AACJ,SAAA,CAAC,CACH,CAAC;KACH;;AAGD,IAAA,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CACnB,GAAG,CAAC,CAAC,KAAyB,KAAI;AAChC,QAAA,IAAI,KAAK,YAAY,YAAY,IAAI,QAAQ,EAAE;AAC7C,YAAA,aAAa,CAAC,GAAG,CAAuB,QAAQ,EAAE;AAChD,gBAAA,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;gBAClB,CAAC,OAAO,GAAG,kBAAkB,CAAC,KAAK,CAAC,OAAO,EAAE,gBAAgB,CAAC;AAC9D,gBAAA,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;AACtB,gBAAA,CAAC,WAAW,GAAG,KAAK,CAAC,UAAU;gBAC/B,CAAC,OAAO,GAAG,UAAU;AACrB,gBAAA,CAAC,aAAa,GAAG,GAAG,CAAC,YAAY;AAClC,aAAA,CAAC,CAAC;SACJ;KACF,CAAC,CACH,CAAC;AACJ,CAAC;AAED;AACA,SAAS,cAAc,CAAC,GAAyB,EAAA;AAC/C,IAAA,OAAO,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AACpF,CAAC;AAED,SAAS,kBAAkB,CACzB,OAAoB,EACpB,cAAoC,EAAA;IAEpC,IAAI,CAAC,cAAc,EAAE;AACnB,QAAA,OAAO,EAAE,CAAC;KACX;IAED,MAAM,UAAU,GAA6B,EAAE,CAAC;AAChD,IAAA,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE;QAChC,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACnC,QAAA,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,YAAA,UAAU,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC;SAC1B;KACF;AAED,IAAA,OAAO,UAAU,CAAC;AACpB,CAAC;AAED,SAAS,mBAAmB,CAAC,MAAoC,EAAA;AAC/D,IAAA,OAAO,CAAC,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AACtB,SAAA,IAAI,EAAE;AACN,SAAA,GAAG,CAAC,CAAC,CAAC,KAAK,CAAG,EAAA,CAAC,CAAI,CAAA,EAAA,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;SACtC,IAAI,CAAC,GAAG,CAAC,CAAC;AACf,CAAC;AAED,SAAS,YAAY,CACnB,OAAyB,EACzB,gBAAwB,EAAA;;IAGxB,MAAM,EAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAC,GAAG,OAAO,CAAC;AAC/C,IAAA,MAAM,aAAa,GAAG,mBAAmB,CAAC,MAAM,CAAC,CAAC;AAElD,IAAA,IAAI,cAAc,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;AAC7C,IAAA,IAAI,cAAc,YAAY,eAAe,EAAE;AAC7C,QAAA,cAAc,GAAG,mBAAmB,CAAC,cAAc,CAAC,CAAC;KACtD;AAAM,SAAA,IAAI,OAAO,cAAc,KAAK,QAAQ,EAAE;QAC7C,cAAc,GAAG,EAAE,CAAC;KACrB;AAED,IAAA,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,YAAY,EAAE,gBAAgB,EAAE,cAAc,EAAE,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9F,IAAA,MAAM,IAAI,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;AAE/B,IAAA,OAAO,YAAY,CAAC,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED;;;;;AAKG;AACH,SAAS,YAAY,CAAC,KAAa,EAAA;IACjC,IAAI,IAAI,GAAG,CAAC,CAAC;AAEb,IAAA,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;QACxB,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;KACxD;;;AAID,IAAA,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;AAEvB,IAAA,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;AACzB,CAAC;AAED;;;;;;;;;;AAUG;AACG,SAAU,qBAAqB,CAAC,YAAsC,EAAA;IAC1E,OAAO;AACL,QAAA;AACE,YAAA,OAAO,EAAE,aAAa;YACtB,UAAU,EAAE,MAAmB;gBAC7BE,uBAAsB,CAAC,qBAAqB,CAAC,CAAC;gBAC9C,OAAO,EAAC,aAAa,EAAE,IAAI,EAAE,GAAG,YAAY,EAAC,CAAC;aAC/C;AACF,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,yBAAyB;AAClC,YAAA,QAAQ,EAAE,0BAA0B;AACpC,YAAA,KAAK,EAAE,IAAI;AACX,YAAA,IAAI,EAAE,CAAC,aAAa,EAAE,aAAa,CAAC;AACrC,SAAA;AACD,QAAA;AACE,YAAA,OAAO,EAAE,sBAAsB;AAC/B,YAAA,KAAK,EAAE,IAAI;YACX,UAAU,EAAE,MAAK;AACf,gBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,cAAc,CAAC,CAAC;AACtC,gBAAA,MAAM,UAAU,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAEzC,gBAAA,OAAO,MAAK;AACV,oBAAAC,WAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAK;AAC3B,wBAAA,UAAU,CAAC,aAAa,GAAG,KAAK,CAAC;AACnC,qBAAC,CAAC,CAAC;AACL,iBAAC,CAAC;aACH;AACF,SAAA;KACF,CAAC;AACJ,CAAC;AAED;;;AAGG;AACH,SAAS,6BAA6B,CACpC,GAAW,EACX,OAAoB,EACpB,gBAA0B,EAAA;AAE1B,IAAA,MAAM,eAAe,GAAG,IAAI,GAAG,EAAE,CAAC;AAClC,IAAA,OAAO,IAAI,KAAK,CAAc,OAAO,EAAE;QACrC,GAAG,CAAC,MAAmB,EAAE,IAAuB,EAAA;YAC9C,MAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACxC,YAAA,MAAM,OAAO,GAA2B,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;AAE1E,YAAA,IAAI,OAAO,KAAK,KAAK,UAAU,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AACrD,gBAAA,OAAO,KAAK,CAAC;aACd;YAED,OAAO,CAAC,UAAkB,KAAI;;AAE5B,gBAAA,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,GAAG,GAAG,UAAU,EAAE,WAAW,EAAE,CAAC;AACpD,gBAAA,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACvE,oBAAA,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACzB,oBAAA,MAAM,YAAY,GAAGC,eAAc,CAAC,GAAG,CAAC,CAAC;;AAGzC,oBAAA,OAAO,CAAC,IAAI,CACVN,mBAAkB,CAEhB,IAAA,2DAAA,CAAA,4BAAA,EAA+B,UAAU,CAAqD,mDAAA,CAAA;wBAC5F,CAA8E,4EAAA,CAAA;wBAC9E,CAAiC,8BAAA,EAAA,UAAU,CAAuB,oBAAA,EAAA,YAAY,CAAc,YAAA,CAAA;wBAC5F,CAAgF,8EAAA,CAAA;wBAChF,CAAqF,mFAAA,CAAA;wBACrF,CAA2E,yEAAA,CAAA;wBAC3E,CAAqC,mCAAA,CAAA,CACxC,CACF,CAAC;iBACH;;gBAGD,OAAQ,KAAkB,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;AACzD,aAAC,CAAC;SACH;AACF,KAAA,CAAC,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAW,EAAE,SAAiC,EAAA;IACzE,MAAM,MAAM,GAAG,IAAI,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,MAAM,CAAC;AACjD,IAAA,MAAM,YAAY,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC;IACvC,IAAI,CAAC,YAAY,EAAE;AACjB,QAAA,OAAO,GAAG,CAAC;KACZ;AAED,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;QACjD,kBAAkB,CAAC,YAAY,CAAC,CAAC;KAClC;IAED,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;AAC3C,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW,EAAA;AACrC,IAAA,IAAI,IAAI,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,QAAQ,KAAK,GAAG,EAAE;QAC/C,MAAM,IAAIE,aAAY,CAAA,IAAA,uDAEpB,2EAA2E;AACzE,YAAA,CAAA,0CAAA,EAA6C,GAAG,CAAwC,sCAAA,CAAA;AACxF,YAAA,6BAA6B,CAChC,CAAC;KACH;AACH;;AC5ZA;;ACRA;;AAEG;;;;"}