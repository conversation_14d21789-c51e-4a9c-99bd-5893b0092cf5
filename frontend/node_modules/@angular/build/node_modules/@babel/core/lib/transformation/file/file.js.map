{"version": 3, "names": ["helpers", "data", "require", "_traverse", "_codeFrame", "_t", "_semver", "babel7", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "cloneNode", "interpreterDirective", "errorVisitor", "enter", "path", "state", "loc", "node", "stop", "File", "constructor", "options", "code", "ast", "inputMap", "_map", "Map", "opts", "declarations", "scope", "metadata", "hub", "file", "getCode", "getScope", "addHelper", "bind", "buildError", "buildCodeFrameError", "NodePath", "parentPath", "parent", "container", "key", "setContext", "shebang", "interpreter", "value", "replaceWith", "remove", "val", "Error", "availableHelper", "name", "versionRange", "minVersion", "err", "semver", "valid", "intersects", "declar", "generator", "res", "uid", "generateUidIdentifier", "dependencies", "dep", "getDependencies", "nodes", "globals", "keys", "getAllBindings", "for<PERSON>ach", "hasBinding", "rename", "_compact", "added", "unshiftContainer", "isVariableDeclaration", "registerDeclaration", "msg", "_Error", "SyntaxError", "traverse", "txt", "highlightCode", "codeFrameColumns", "start", "line", "column", "end", "undefined", "exports", "prototype", "addImport", "addTemplateObject", "getModuleName"], "sources": ["../../../src/transformation/file/file.ts"], "sourcesContent": ["import * as helpers from \"@babel/helpers\";\nimport { NodePath } from \"@babel/traverse\";\nimport type { HubInterface, Visitor, Scope } from \"@babel/traverse\";\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport traverse from \"@babel/traverse\";\nimport { cloneNode, interpreterDirective } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport semver from \"semver\";\n\nimport type { NormalizedFile } from \"../normalize-file.ts\";\n\n// @ts-expect-error This file is `any`\nimport * as babel7 from \"./babel-7-helpers.cjs\";\n\nconst errorVisitor: Visitor<{ loc: t.SourceLocation | null }> = {\n  enter(path, state) {\n    const loc = path.node.loc;\n    if (loc) {\n      state.loc = loc;\n      path.stop();\n    }\n  },\n};\n\nexport default class File {\n  _map: Map<unknown, unknown> = new Map();\n  opts: { [key: string]: any };\n  declarations: { [key: string]: t.Identifier } = {};\n  path: NodePath<t.Program>;\n  ast: t.File;\n  scope: Scope;\n  metadata: { [key: string]: any } = {};\n  code: string = \"\";\n  inputMap: any;\n\n  hub: HubInterface & { file: File } = {\n    // keep it for the usage in babel-core, ex: path.hub.file.opts.filename\n    file: this,\n    getCode: () => this.code,\n    getScope: () => this.scope,\n    addHelper: this.addHelper.bind(this),\n    buildError: this.buildCodeFrameError.bind(this),\n  };\n\n  constructor(options: any, { code, ast, inputMap }: NormalizedFile) {\n    this.opts = options;\n    this.code = code;\n    this.ast = ast;\n    this.inputMap = inputMap;\n\n    this.path = NodePath.get({\n      hub: this.hub,\n      parentPath: null,\n      parent: this.ast,\n      container: this.ast,\n      key: \"program\",\n    }).setContext() as NodePath<t.Program>;\n    this.scope = this.path.scope;\n  }\n\n  /**\n   * Provide backward-compatible access to the interpreter directive handling\n   * in Babel 6.x. If you are writing a plugin for Babel 7.x, it would be\n   * best to use 'program.interpreter' directly.\n   */\n  get shebang(): string {\n    const { interpreter } = this.path.node;\n    return interpreter ? interpreter.value : \"\";\n  }\n  set shebang(value: string) {\n    if (value) {\n      this.path.get(\"interpreter\").replaceWith(interpreterDirective(value));\n    } else {\n      this.path.get(\"interpreter\").remove();\n    }\n  }\n\n  set(key: unknown, val: unknown) {\n    if (!process.env.BABEL_8_BREAKING) {\n      if (key === \"helpersNamespace\") {\n        throw new Error(\n          \"Babel 7.0.0-beta.56 has dropped support for the 'helpersNamespace' utility.\" +\n            \"If you are using @babel/plugin-external-helpers you will need to use a newer \" +\n            \"version than the one you currently have installed. \" +\n            \"If you have your own implementation, you'll want to explore using 'helperGenerator' \" +\n            \"alongside 'file.availableHelper()'.\",\n        );\n      }\n    }\n\n    this._map.set(key, val);\n  }\n\n  get(key: unknown): any {\n    return this._map.get(key);\n  }\n\n  has(key: unknown): boolean {\n    return this._map.has(key);\n  }\n\n  /**\n   * Check if a given helper is available in @babel/core's helper list.\n   *\n   * This _also_ allows you to pass a Babel version specifically. If the\n   * helper exists, but was not available for the full given range, it will be\n   * considered unavailable.\n   */\n  availableHelper(name: string, versionRange?: string | null): boolean {\n    let minVersion;\n    try {\n      minVersion = helpers.minVersion(name);\n    } catch (err) {\n      if (err.code !== \"BABEL_HELPER_UNKNOWN\") throw err;\n\n      return false;\n    }\n\n    if (typeof versionRange !== \"string\") return true;\n\n    // semver.intersects() has some surprising behavior with comparing ranges\n    // with pre-release versions. We add '^' to ensure that we are always\n    // comparing ranges with ranges, which sidesteps this logic.\n    // For example:\n    //\n    //   semver.intersects(`<7.0.1`, \"7.0.0-beta.0\") // false - surprising\n    //   semver.intersects(`<7.0.1`, \"^7.0.0-beta.0\") // true - expected\n    //\n    // This is because the first falls back to\n    //\n    //   semver.satisfies(\"7.0.0-beta.0\", `<7.0.1`) // false - surprising\n    //\n    // and this fails because a prerelease version can only satisfy a range\n    // if it is a prerelease within the same major/minor/patch range.\n    //\n    // Note: If this is found to have issues, please also revisit the logic in\n    // transform-runtime's definitions.js file.\n    if (semver.valid(versionRange)) versionRange = `^${versionRange}`;\n\n    if (process.env.BABEL_8_BREAKING) {\n      return (\n        !semver.intersects(`<${minVersion}`, versionRange) &&\n        !semver.intersects(`>=9.0.0`, versionRange)\n      );\n    } else {\n      return (\n        !semver.intersects(`<${minVersion}`, versionRange) &&\n        !semver.intersects(`>=8.0.0`, versionRange)\n      );\n    }\n  }\n\n  addHelper(name: string): t.Identifier {\n    const declar = this.declarations[name];\n    if (declar) return cloneNode(declar);\n\n    const generator = this.get(\"helperGenerator\");\n    if (generator) {\n      const res = generator(name);\n      if (res) return res;\n    }\n\n    // make sure that the helper exists\n    helpers.minVersion(name);\n\n    const uid = (this.declarations[name] =\n      this.scope.generateUidIdentifier(name));\n\n    const dependencies: { [key: string]: t.Identifier } = {};\n    for (const dep of helpers.getDependencies(name)) {\n      dependencies[dep] = this.addHelper(dep);\n    }\n\n    const { nodes, globals } = helpers.get(\n      name,\n      dep => dependencies[dep],\n      uid.name,\n      Object.keys(this.scope.getAllBindings()),\n    );\n\n    globals.forEach(name => {\n      if (this.path.scope.hasBinding(name, true /* noGlobals */)) {\n        this.path.scope.rename(name);\n      }\n    });\n\n    nodes.forEach(node => {\n      // @ts-expect-error Fixme: document _compact node property\n      node._compact = true;\n    });\n\n    const added = this.path.unshiftContainer(\"body\", nodes);\n    // TODO: NodePath#unshiftContainer should automatically register new\n    // bindings.\n    for (const path of added) {\n      if (path.isVariableDeclaration()) this.scope.registerDeclaration(path);\n    }\n\n    return uid;\n  }\n\n  buildCodeFrameError(\n    node: t.Node | undefined | null,\n    msg: string,\n    _Error: typeof Error = SyntaxError,\n  ): Error {\n    let loc = node?.loc;\n\n    if (!loc && node) {\n      const state: { loc?: t.SourceLocation | null } = {\n        loc: null,\n      };\n      traverse(node, errorVisitor, this.scope, state);\n      loc = state.loc;\n\n      let txt =\n        \"This is an error on an internal node. Probably an internal error.\";\n      if (loc) txt += \" Location has been estimated.\";\n\n      msg += ` (${txt})`;\n    }\n\n    if (loc) {\n      const { highlightCode = true } = this.opts;\n\n      msg +=\n        \"\\n\" +\n        codeFrameColumns(\n          this.code,\n          {\n            start: {\n              line: loc.start.line,\n              column: loc.start.column + 1,\n            },\n            end:\n              loc.end && loc.start.line === loc.end.line\n                ? {\n                    line: loc.end.line,\n                    column: loc.end.column + 1,\n                  }\n                : undefined,\n          },\n          { highlightCode },\n        );\n    }\n\n    return new _Error(msg);\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-expect-error Babel 7\n  File.prototype.addImport = function addImport() {\n    throw new Error(\n      \"This API has been removed. If you're looking for this \" +\n        \"functionality in Babel 7, you should import the \" +\n        \"'@babel/helper-module-imports' module and use the functions exposed \" +\n        \" from that module, such as 'addNamed' or 'addDefault'.\",\n    );\n  };\n  // @ts-expect-error Babel 7\n  File.prototype.addTemplateObject = function addTemplateObject() {\n    throw new Error(\n      \"This function has been moved into the template literal transform itself.\",\n    );\n  };\n\n  if (!USE_ESM || IS_STANDALONE) {\n    // @ts-expect-error Babel 7\n    File.prototype.getModuleName = function getModuleName() {\n      return babel7.getModuleName()(this.opts, this.opts);\n    };\n  }\n}\n"], "mappings": ";;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,UAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,SAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAG,WAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,GAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,EAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAK,QAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,OAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAKA,IAAAM,MAAA,GAAAC,uBAAA,CAAAN,OAAA;AAAgD,SAAAO,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA;EAPvCW,SAAS;EAAEC;AAAoB,IAAAzB,EAAA;AASxC,MAAM0B,YAAuD,GAAG;EAC9DC,KAAKA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACjB,MAAMC,GAAG,GAAGF,IAAI,CAACG,IAAI,CAACD,GAAG;IACzB,IAAIA,GAAG,EAAE;MACPD,KAAK,CAACC,GAAG,GAAGA,GAAG;MACfF,IAAI,CAACI,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAEc,MAAMC,IAAI,CAAC;EAoBxBC,WAAWA,CAACC,OAAY,EAAE;IAAEC,IAAI;IAAEC,GAAG;IAAEC;EAAyB,CAAC,EAAE;IAAA,KAnBnEC,IAAI,GAA0B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACvCC,IAAI;IAAA,KACJC,YAAY,GAAoC,CAAC,CAAC;IAAA,KAClDd,IAAI;IAAA,KACJS,GAAG;IAAA,KACHM,KAAK;IAAA,KACLC,QAAQ,GAA2B,CAAC,CAAC;IAAA,KACrCR,IAAI,GAAW,EAAE;IAAA,KACjBE,QAAQ;IAAA,KAERO,GAAG,GAAkC;MAEnCC,IAAI,EAAE,IAAI;MACVC,OAAO,EAAEA,CAAA,KAAM,IAAI,CAACX,IAAI;MACxBY,QAAQ,EAAEA,CAAA,KAAM,IAAI,CAACL,KAAK;MAC1BM,SAAS,EAAE,IAAI,CAACA,SAAS,CAACC,IAAI,CAAC,IAAI,CAAC;MACpCC,UAAU,EAAE,IAAI,CAACC,mBAAmB,CAACF,IAAI,CAAC,IAAI;IAChD,CAAC;IAGC,IAAI,CAACT,IAAI,GAAGN,OAAO;IACnB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IAExB,IAAI,CAACV,IAAI,GAAGyB,oBAAQ,CAACzC,GAAG,CAAC;MACvBiC,GAAG,EAAE,IAAI,CAACA,GAAG;MACbS,UAAU,EAAE,IAAI;MAChBC,MAAM,EAAE,IAAI,CAAClB,GAAG;MAChBmB,SAAS,EAAE,IAAI,CAACnB,GAAG;MACnBoB,GAAG,EAAE;IACP,CAAC,CAAC,CAACC,UAAU,CAAC,CAAwB;IACtC,IAAI,CAACf,KAAK,GAAG,IAAI,CAACf,IAAI,CAACe,KAAK;EAC9B;EAOA,IAAIgB,OAAOA,CAAA,EAAW;IACpB,MAAM;MAAEC;IAAY,CAAC,GAAG,IAAI,CAAChC,IAAI,CAACG,IAAI;IACtC,OAAO6B,WAAW,GAAGA,WAAW,CAACC,KAAK,GAAG,EAAE;EAC7C;EACA,IAAIF,OAAOA,CAACE,KAAa,EAAE;IACzB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACjC,IAAI,CAAChB,GAAG,CAAC,aAAa,CAAC,CAACkD,WAAW,CAACrC,oBAAoB,CAACoC,KAAK,CAAC,CAAC;IACvE,CAAC,MAAM;MACL,IAAI,CAACjC,IAAI,CAAChB,GAAG,CAAC,aAAa,CAAC,CAACmD,MAAM,CAAC,CAAC;IACvC;EACF;EAEAxC,GAAGA,CAACkC,GAAY,EAAEO,GAAY,EAAE;IACK;MACjC,IAAIP,GAAG,KAAK,kBAAkB,EAAE;QAC9B,MAAM,IAAIQ,KAAK,CACb,6EAA6E,GAC3E,+EAA+E,GAC/E,qDAAqD,GACrD,sFAAsF,GACtF,qCACJ,CAAC;MACH;IACF;IAEA,IAAI,CAAC1B,IAAI,CAAChB,GAAG,CAACkC,GAAG,EAAEO,GAAG,CAAC;EACzB;EAEApD,GAAGA,CAAC6C,GAAY,EAAO;IACrB,OAAO,IAAI,CAAClB,IAAI,CAAC3B,GAAG,CAAC6C,GAAG,CAAC;EAC3B;EAEA9C,GAAGA,CAAC8C,GAAY,EAAW;IACzB,OAAO,IAAI,CAAClB,IAAI,CAAC5B,GAAG,CAAC8C,GAAG,CAAC;EAC3B;EASAS,eAAeA,CAACC,IAAY,EAAEC,YAA4B,EAAW;IACnE,IAAIC,UAAU;IACd,IAAI;MACFA,UAAU,GAAG1E,OAAO,CAAD,CAAC,CAAC0E,UAAU,CAACF,IAAI,CAAC;IACvC,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZ,IAAIA,GAAG,CAAClC,IAAI,KAAK,sBAAsB,EAAE,MAAMkC,GAAG;MAElD,OAAO,KAAK;IACd;IAEA,IAAI,OAAOF,YAAY,KAAK,QAAQ,EAAE,OAAO,IAAI;IAmBjD,IAAIG,QAAKA,CAAC,CAACC,KAAK,CAACJ,YAAY,CAAC,EAAEA,YAAY,GAAG,IAAIA,YAAY,EAAE;IAO1D;MACL,OACE,CAACG,QAAKA,CAAC,CAACE,UAAU,CAAC,IAAIJ,UAAU,EAAE,EAAED,YAAY,CAAC,IAClD,CAACG,QAAKA,CAAC,CAACE,UAAU,CAAC,SAAS,EAAEL,YAAY,CAAC;IAE/C;EACF;EAEAnB,SAASA,CAACkB,IAAY,EAAgB;IACpC,MAAMO,MAAM,GAAG,IAAI,CAAChC,YAAY,CAACyB,IAAI,CAAC;IACtC,IAAIO,MAAM,EAAE,OAAOlD,SAAS,CAACkD,MAAM,CAAC;IAEpC,MAAMC,SAAS,GAAG,IAAI,CAAC/D,GAAG,CAAC,iBAAiB,CAAC;IAC7C,IAAI+D,SAAS,EAAE;MACb,MAAMC,GAAG,GAAGD,SAAS,CAACR,IAAI,CAAC;MAC3B,IAAIS,GAAG,EAAE,OAAOA,GAAG;IACrB;IAGAjF,OAAO,CAAD,CAAC,CAAC0E,UAAU,CAACF,IAAI,CAAC;IAExB,MAAMU,GAAG,GAAI,IAAI,CAACnC,YAAY,CAACyB,IAAI,CAAC,GAClC,IAAI,CAACxB,KAAK,CAACmC,qBAAqB,CAACX,IAAI,CAAE;IAEzC,MAAMY,YAA6C,GAAG,CAAC,CAAC;IACxD,KAAK,MAAMC,GAAG,IAAIrF,OAAO,CAAD,CAAC,CAACsF,eAAe,CAACd,IAAI,CAAC,EAAE;MAC/CY,YAAY,CAACC,GAAG,CAAC,GAAG,IAAI,CAAC/B,SAAS,CAAC+B,GAAG,CAAC;IACzC;IAEA,MAAM;MAAEE,KAAK;MAAEC;IAAQ,CAAC,GAAGxF,OAAO,CAAD,CAAC,CAACiB,GAAG,CACpCuD,IAAI,EACJa,GAAG,IAAID,YAAY,CAACC,GAAG,CAAC,EACxBH,GAAG,CAACV,IAAI,EACRnD,MAAM,CAACoE,IAAI,CAAC,IAAI,CAACzC,KAAK,CAAC0C,cAAc,CAAC,CAAC,CACzC,CAAC;IAEDF,OAAO,CAACG,OAAO,CAACnB,IAAI,IAAI;MACtB,IAAI,IAAI,CAACvC,IAAI,CAACe,KAAK,CAAC4C,UAAU,CAACpB,IAAI,EAAE,IAAoB,CAAC,EAAE;QAC1D,IAAI,CAACvC,IAAI,CAACe,KAAK,CAAC6C,MAAM,CAACrB,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC;IAEFe,KAAK,CAACI,OAAO,CAACvD,IAAI,IAAI;MAEpBA,IAAI,CAAC0D,QAAQ,GAAG,IAAI;IACtB,CAAC,CAAC;IAEF,MAAMC,KAAK,GAAG,IAAI,CAAC9D,IAAI,CAAC+D,gBAAgB,CAAC,MAAM,EAAET,KAAK,CAAC;IAGvD,KAAK,MAAMtD,IAAI,IAAI8D,KAAK,EAAE;MACxB,IAAI9D,IAAI,CAACgE,qBAAqB,CAAC,CAAC,EAAE,IAAI,CAACjD,KAAK,CAACkD,mBAAmB,CAACjE,IAAI,CAAC;IACxE;IAEA,OAAOiD,GAAG;EACZ;EAEAzB,mBAAmBA,CACjBrB,IAA+B,EAC/B+D,GAAW,EACXC,MAAoB,GAAGC,WAAW,EAC3B;IACP,IAAIlE,GAAG,GAAGC,IAAI,oBAAJA,IAAI,CAAED,GAAG;IAEnB,IAAI,CAACA,GAAG,IAAIC,IAAI,EAAE;MAChB,MAAMF,KAAwC,GAAG;QAC/CC,GAAG,EAAE;MACP,CAAC;MACD,IAAAmE,mBAAQ,EAAClE,IAAI,EAAEL,YAAY,EAAE,IAAI,CAACiB,KAAK,EAAEd,KAAK,CAAC;MAC/CC,GAAG,GAAGD,KAAK,CAACC,GAAG;MAEf,IAAIoE,GAAG,GACL,mEAAmE;MACrE,IAAIpE,GAAG,EAAEoE,GAAG,IAAI,+BAA+B;MAE/CJ,GAAG,IAAI,KAAKI,GAAG,GAAG;IACpB;IAEA,IAAIpE,GAAG,EAAE;MACP,MAAM;QAAEqE,aAAa,GAAG;MAAK,CAAC,GAAG,IAAI,CAAC1D,IAAI;MAE1CqD,GAAG,IACD,IAAI,GACJ,IAAAM,6BAAgB,EACd,IAAI,CAAChE,IAAI,EACT;QACEiE,KAAK,EAAE;UACLC,IAAI,EAAExE,GAAG,CAACuE,KAAK,CAACC,IAAI;UACpBC,MAAM,EAAEzE,GAAG,CAACuE,KAAK,CAACE,MAAM,GAAG;QAC7B,CAAC;QACDC,GAAG,EACD1E,GAAG,CAAC0E,GAAG,IAAI1E,GAAG,CAACuE,KAAK,CAACC,IAAI,KAAKxE,GAAG,CAAC0E,GAAG,CAACF,IAAI,GACtC;UACEA,IAAI,EAAExE,GAAG,CAAC0E,GAAG,CAACF,IAAI;UAClBC,MAAM,EAAEzE,GAAG,CAAC0E,GAAG,CAACD,MAAM,GAAG;QAC3B,CAAC,GACDE;MACR,CAAC,EACD;QAAEN;MAAc,CAClB,CAAC;IACL;IAEA,OAAO,IAAIJ,MAAM,CAACD,GAAG,CAAC;EACxB;AACF;AAACY,OAAA,CAAAhG,OAAA,GAAAuB,IAAA;AAEkC;EAEjCA,IAAI,CAAC0E,SAAS,CAACC,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IAC9C,MAAM,IAAI3C,KAAK,CACb,wDAAwD,GACtD,kDAAkD,GAClD,sEAAsE,GACtE,wDACJ,CAAC;EACH,CAAC;EAEDhC,IAAI,CAAC0E,SAAS,CAACE,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IAC9D,MAAM,IAAI5C,KAAK,CACb,0EACF,CAAC;EACH,CAAC;EAE8B;IAE7BhC,IAAI,CAAC0E,SAAS,CAACG,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;MACtD,OAAO5G,MAAM,CAAC4G,aAAa,CAAC,CAAC,CAAC,IAAI,CAACrE,IAAI,EAAE,IAAI,CAACA,IAAI,CAAC;IACrD,CAAC;EACH;AACF;AAAC", "ignoreList": []}