{"version": 3, "names": ["_gensync", "data", "require", "_index", "_index2", "_rewriteStackTrace", "transformRunner", "gens<PERSON>", "transform", "code", "opts", "config", "loadConfig", "run", "exports", "optsOrCallback", "maybe<PERSON><PERSON><PERSON>", "callback", "undefined", "beginHiddenCallStack", "sync", "errback", "transformSync", "args", "transformAsync", "async"], "sources": ["../src/transform.ts"], "sourcesContent": ["import gensync, { type <PERSON><PERSON> } from \"gensync\";\n\nimport loadConfig from \"./config/index.ts\";\nimport type { InputOptions, ResolvedConfig } from \"./config/index.ts\";\nimport { run } from \"./transformation/index.ts\";\n\nimport type { FileResult, FileResultCallback } from \"./transformation/index.ts\";\nimport { beginHiddenCallStack } from \"./errors/rewrite-stack-trace.ts\";\n\nexport type { FileResult } from \"./transformation/index.ts\";\n\ntype Transform = {\n  (code: string, callback: FileResultCallback): void;\n  (\n    code: string,\n    opts: InputOptions | undefined | null,\n    callback: FileResultCallback,\n  ): void;\n  (code: string, opts?: InputOptions | null): FileResult | null;\n};\n\nconst transformRunner = gensync(function* transform(\n  code: string,\n  opts?: InputOptions,\n): Handler<FileResult | null> {\n  const config: ResolvedConfig | null = yield* loadConfig(opts);\n  if (config === null) return null;\n\n  return yield* run(config, code);\n});\n\nexport const transform: Transform = function transform(\n  code,\n  optsOrCallback?: InputOptions | null | undefined | FileResultCallback,\n  maybeCallback?: FileResultCallback,\n) {\n  let opts: InputOptions | undefined | null;\n  let callback: FileResultCallback | undefined;\n  if (typeof optsOrCallback === \"function\") {\n    callback = optsOrCallback;\n    opts = undefined;\n  } else {\n    opts = optsOrCallback;\n    callback = maybeCallback;\n  }\n\n  if (callback === undefined) {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'transform' function expects a callback. If you need to call it synchronously, please use 'transformSync'.\",\n      );\n    } else {\n      // console.warn(\n      //   \"Starting from Babel 8.0.0, the 'transform' function will expect a callback. If you need to call it synchronously, please use 'transformSync'.\",\n      // );\n      return beginHiddenCallStack(transformRunner.sync)(code, opts);\n    }\n  }\n\n  beginHiddenCallStack(transformRunner.errback)(code, opts, callback);\n};\n\nexport function transformSync(\n  ...args: Parameters<typeof transformRunner.sync>\n) {\n  return beginHiddenCallStack(transformRunner.sync)(...args);\n}\nexport function transformAsync(\n  ...args: Parameters<typeof transformRunner.async>\n) {\n  return beginHiddenCallStack(transformRunner.async)(...args);\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,IAAAE,MAAA,GAAAD,OAAA;AAEA,IAAAE,OAAA,GAAAF,OAAA;AAGA,IAAAG,kBAAA,GAAAH,OAAA;AAcA,MAAMI,eAAe,GAAGC,SAAMA,CAAC,CAAC,UAAUC,SAASA,CACjDC,IAAY,EACZC,IAAmB,EACS;EAC5B,MAAMC,MAA6B,GAAG,OAAO,IAAAC,cAAU,EAACF,IAAI,CAAC;EAC7D,IAAIC,MAAM,KAAK,IAAI,EAAE,OAAO,IAAI;EAEhC,OAAO,OAAO,IAAAE,WAAG,EAACF,MAAM,EAAEF,IAAI,CAAC;AACjC,CAAC,CAAC;AAEK,MAAMD,SAAoB,GAAAM,OAAA,CAAAN,SAAA,GAAG,SAASA,SAASA,CACpDC,IAAI,EACJM,cAAqE,EACrEC,aAAkC,EAClC;EACA,IAAIN,IAAqC;EACzC,IAAIO,QAAwC;EAC5C,IAAI,OAAOF,cAAc,KAAK,UAAU,EAAE;IACxCE,QAAQ,GAAGF,cAAc;IACzBL,IAAI,GAAGQ,SAAS;EAClB,CAAC,MAAM;IACLR,IAAI,GAAGK,cAAc;IACrBE,QAAQ,GAAGD,aAAa;EAC1B;EAEA,IAAIC,QAAQ,KAAKC,SAAS,EAAE;IAKnB;MAIL,OAAO,IAAAC,uCAAoB,EAACb,eAAe,CAACc,IAAI,CAAC,CAACX,IAAI,EAAEC,IAAI,CAAC;IAC/D;EACF;EAEA,IAAAS,uCAAoB,EAACb,eAAe,CAACe,OAAO,CAAC,CAACZ,IAAI,EAAEC,IAAI,EAAEO,QAAQ,CAAC;AACrE,CAAC;AAEM,SAASK,aAAaA,CAC3B,GAAGC,IAA6C,EAChD;EACA,OAAO,IAAAJ,uCAAoB,EAACb,eAAe,CAACc,IAAI,CAAC,CAAC,GAAGG,IAAI,CAAC;AAC5D;AACO,SAASC,cAAcA,CAC5B,GAAGD,IAA8C,EACjD;EACA,OAAO,IAAAJ,uCAAoB,EAACb,eAAe,CAACmB,KAAK,CAAC,CAAC,GAAGF,IAAI,CAAC;AAC7D;AAAC", "ignoreList": []}