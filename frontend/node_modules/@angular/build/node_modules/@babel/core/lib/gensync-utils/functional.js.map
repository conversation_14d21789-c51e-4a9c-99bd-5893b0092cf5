{"version": 3, "names": ["_async", "require", "once", "fn", "result", "resultP", "promiseReferenced", "waitFor", "isAsync", "ok", "value", "error", "resolve", "reject", "Promise", "res", "rej"], "sources": ["../../src/gensync-utils/functional.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from \"gensync\";\n\nimport { isAsync, waitFor } from \"./async.ts\";\n\nexport function once<R>(fn: () => Handler<R>): () => Handler<R> {\n  let result: { ok: true; value: R } | { ok: false; value: unknown };\n  let resultP: Promise<R>;\n  let promiseReferenced = false;\n  return function* () {\n    if (!result) {\n      if (resultP) {\n        promiseReferenced = true;\n        return yield* waitFor(resultP);\n      }\n\n      if (!(yield* isAsync())) {\n        try {\n          result = { ok: true, value: yield* fn() };\n        } catch (error) {\n          result = { ok: false, value: error };\n        }\n      } else {\n        let resolve: (result: R) => void, reject: (error: unknown) => void;\n        resultP = new Promise((res, rej) => {\n          resolve = res;\n          reject = rej;\n        });\n\n        try {\n          result = { ok: true, value: yield* fn() };\n          // Avoid keeping the promise around\n          // now that we have the result.\n          resultP = null;\n          // We only resolve/reject the promise if it has been actually\n          // referenced. If there are no listeners we can forget about it.\n          // In the reject case, this avoid uncatchable unhandledRejection\n          // events.\n          if (promiseReferenced) resolve(result.value);\n        } catch (error) {\n          result = { ok: false, value: error };\n          resultP = null;\n          if (promiseReferenced) reject(error);\n        }\n      }\n    }\n\n    if (result.ok) return result.value;\n    else throw result.value;\n  };\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,MAAA,GAAAC,OAAA;AAEO,SAASC,IAAIA,CAAIC,EAAoB,EAAoB;EAC9D,IAAIC,MAA8D;EAClE,IAAIC,OAAmB;EACvB,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,OAAO,aAAa;IAClB,IAAI,CAACF,MAAM,EAAE;MACX,IAAIC,OAAO,EAAE;QACXC,iBAAiB,GAAG,IAAI;QACxB,OAAO,OAAO,IAAAC,cAAO,EAACF,OAAO,CAAC;MAChC;MAEA,IAAI,EAAE,OAAO,IAAAG,cAAO,EAAC,CAAC,CAAC,EAAE;QACvB,IAAI;UACFJ,MAAM,GAAG;YAAEK,EAAE,EAAE,IAAI;YAAEC,KAAK,EAAE,OAAOP,EAAE,CAAC;UAAE,CAAC;QAC3C,CAAC,CAAC,OAAOQ,KAAK,EAAE;UACdP,MAAM,GAAG;YAAEK,EAAE,EAAE,KAAK;YAAEC,KAAK,EAAEC;UAAM,CAAC;QACtC;MACF,CAAC,MAAM;QACL,IAAIC,OAA4B,EAAEC,MAAgC;QAClER,OAAO,GAAG,IAAIS,OAAO,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;UAClCJ,OAAO,GAAGG,GAAG;UACbF,MAAM,GAAGG,GAAG;QACd,CAAC,CAAC;QAEF,IAAI;UACFZ,MAAM,GAAG;YAAEK,EAAE,EAAE,IAAI;YAAEC,KAAK,EAAE,OAAOP,EAAE,CAAC;UAAE,CAAC;UAGzCE,OAAO,GAAG,IAAI;UAKd,IAAIC,iBAAiB,EAAEM,OAAO,CAACR,MAAM,CAACM,KAAK,CAAC;QAC9C,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdP,MAAM,GAAG;YAAEK,EAAE,EAAE,KAAK;YAAEC,KAAK,EAAEC;UAAM,CAAC;UACpCN,OAAO,GAAG,IAAI;UACd,IAAIC,iBAAiB,EAAEO,MAAM,CAACF,KAAK,CAAC;QACtC;MACF;IACF;IAEA,IAAIP,MAAM,CAACK,EAAE,EAAE,OAAOL,MAAM,CAACM,KAAK,CAAC,KAC9B,MAAMN,MAAM,CAACM,KAAK;EACzB,CAAC;AACH;AAAC", "ignoreList": []}