{"name": "@angular/cli", "version": "18.2.20", "description": "CLI tool for Angular", "main": "lib/cli/index.js", "bin": {"ng": "./bin/ng.js"}, "keywords": ["Angular CLI", "Angular DevKit", "angular", "angular-cli", "devkit", "sdk"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli", "dependencies": {"@angular-devkit/architect": "0.1802.20", "@angular-devkit/core": "18.2.20", "@angular-devkit/schematics": "18.2.20", "@inquirer/prompts": "5.3.8", "@listr2/prompt-adapter-inquirer": "2.0.15", "@schematics/angular": "18.2.20", "@yarnpkg/lockfile": "1.1.0", "ini": "4.1.3", "jsonc-parser": "3.3.1", "listr2": "8.2.4", "npm-package-arg": "11.0.3", "npm-pick-manifest": "9.1.0", "pacote": "18.0.6", "resolve": "1.22.8", "semver": "7.6.3", "symbol-observable": "4.0.0", "yargs": "17.7.2"}, "ng-update": {"migrations": "@schematics/angular/migrations/migration-collection.json", "packageGroup": {"@angular/cli": "18.2.20", "@angular/build": "18.2.20", "@angular/ssr": "18.2.20", "@angular-devkit/architect": "0.1802.20", "@angular-devkit/build-angular": "18.2.20", "@angular-devkit/build-webpack": "0.1802.20", "@angular-devkit/core": "18.2.20", "@angular-devkit/schematics": "18.2.20"}}, "packageManager": "yarn@4.4.0", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "dependenciesMeta": {"esbuild": {"built": true}, "puppeteer": {"built": true}}}