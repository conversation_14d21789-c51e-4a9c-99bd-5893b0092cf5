/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/**
 * @module
 * @description
 * Entry point for all animation APIs of the animation package.
 */
export { AnimationBuilder, AnimationFactory } from './animation_builder';
export { animate, animateChild, animation, AnimationMetadataType, AUTO_STYLE, group, keyframes, query, sequence, stagger, state, style, transition, trigger, useAnimation, } from './animation_metadata';
export { NoopAnimationPlayer } from './players/animation_player';
export * from './private_export';
//# sourceMappingURL=data:application/json;base64,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