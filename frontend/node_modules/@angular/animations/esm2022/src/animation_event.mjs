/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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