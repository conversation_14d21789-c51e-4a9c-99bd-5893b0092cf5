/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export { createEngine as ɵcreateEngine } from './create_engine';
export { Animation as ɵAnimation } from './dsl/animation';
export { AnimationStyleNormalizer as ɵAnimationStyleNormalizer, NoopAnimationStyleNormalizer as ɵNoopAnimationStyleNormalizer, } from './dsl/style_normalization/animation_style_normalizer';
export { WebAnimationsStyleNormalizer as ɵWebAnimationsStyleNormalizer } from './dsl/style_normalization/web_animations_style_normalizer';
export { AnimationEngine as ɵAnimationEngine } from './render/animation_engine_next';
export { AnimationRendererFactory as ɵAnimationRendererFactory } from './render/animation_renderer';
export { AnimationRenderer as ɵAnimationRenderer, BaseAnimationRenderer as ɵBaseAnimationRenderer, } from './render/renderer';
export { containsElement as ɵcontainsElement, getParentElement as ɵgetParentElement, invokeQuery as ɵinvokeQuery, validateStyleProperty as ɵvalidateStyleProperty, validateWebAnimatableStyleProperty as ɵvalidateWebAnimatableStyleProperty, } from './render/shared';
export { WebAnimationsDriver as ɵWebAnimationsDriver } from './render/web_animations/web_animations_driver';
export { WebAnimationsPlayer as ɵWebAnimationsPlayer } from './render/web_animations/web_animations_player';
export { allowPreviousPlayerStylesMerge as ɵallowPreviousPlayerStylesMerge, camelCaseToDashCase as ɵcamelCaseToDashCase, normalizeKeyframes as ɵnormalizeKeyframes, } from './util';
//# sourceMappingURL=data:application/json;base64,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