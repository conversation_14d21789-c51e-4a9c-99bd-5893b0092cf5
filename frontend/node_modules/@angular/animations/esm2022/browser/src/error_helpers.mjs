/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { ɵRuntimeError as RuntimeError } from '@angular/core';
const LINE_START = '\n - ';
export function invalidTimingValue(exp) {
    return new RuntimeError(3000 /* RuntimeErrorCode.INVALID_TIMING_VALUE */, ngDevMode && `The provided timing value "${exp}" is invalid.`);
}
export function negativeStepValue() {
    return new RuntimeError(3100 /* RuntimeErrorCode.NEGATIVE_STEP_VALUE */, ngDevMode && 'Duration values below 0 are not allowed for this animation step.');
}
export function negativeDelayValue() {
    return new RuntimeError(3101 /* RuntimeErrorCode.NEGATIVE_DELAY_VALUE */, ngDevMode && 'Delay values below 0 are not allowed for this animation step.');
}
export function invalidStyleParams(varName) {
    return new RuntimeError(3001 /* RuntimeErrorCode.INVALID_STYLE_PARAMS */, ngDevMode &&
        `Unable to resolve the local animation param ${varName} in the given list of values`);
}
export function invalidParamValue(varName) {
    return new RuntimeError(3003 /* RuntimeErrorCode.INVALID_PARAM_VALUE */, ngDevMode && `Please provide a value for the animation param ${varName}`);
}
export function invalidNodeType(nodeType) {
    return new RuntimeError(3004 /* RuntimeErrorCode.INVALID_NODE_TYPE */, ngDevMode && `Unable to resolve animation metadata node #${nodeType}`);
}
export function invalidCssUnitValue(userProvidedProperty, value) {
    return new RuntimeError(3005 /* RuntimeErrorCode.INVALID_CSS_UNIT_VALUE */, ngDevMode && `Please provide a CSS unit value for ${userProvidedProperty}:${value}`);
}
export function invalidTrigger() {
    return new RuntimeError(3006 /* RuntimeErrorCode.INVALID_TRIGGER */, ngDevMode &&
        "animation triggers cannot be prefixed with an `@` sign (e.g. trigger('@foo', [...]))");
}
export function invalidDefinition() {
    return new RuntimeError(3007 /* RuntimeErrorCode.INVALID_DEFINITION */, ngDevMode && 'only state() and transition() definitions can sit inside of a trigger()');
}
export function invalidState(metadataName, missingSubs) {
    return new RuntimeError(3008 /* RuntimeErrorCode.INVALID_STATE */, ngDevMode &&
        `state("${metadataName}", ...) must define default values for all the following style substitutions: ${missingSubs.join(', ')}`);
}
export function invalidStyleValue(value) {
    return new RuntimeError(3002 /* RuntimeErrorCode.INVALID_STYLE_VALUE */, ngDevMode && `The provided style string value ${value} is not allowed.`);
}
export function invalidProperty(prop) {
    return new RuntimeError(3009 /* RuntimeErrorCode.INVALID_PROPERTY */, ngDevMode &&
        `The provided animation property "${prop}" is not a supported CSS property for animations`);
}
export function invalidParallelAnimation(prop, firstStart, firstEnd, secondStart, secondEnd) {
    return new RuntimeError(3010 /* RuntimeErrorCode.INVALID_PARALLEL_ANIMATION */, ngDevMode &&
        `The CSS property "${prop}" that exists between the times of "${firstStart}ms" and "${firstEnd}ms" is also being animated in a parallel animation between the times of "${secondStart}ms" and "${secondEnd}ms"`);
}
export function invalidKeyframes() {
    return new RuntimeError(3011 /* RuntimeErrorCode.INVALID_KEYFRAMES */, ngDevMode && `keyframes() must be placed inside of a call to animate()`);
}
export function invalidOffset() {
    return new RuntimeError(3012 /* RuntimeErrorCode.INVALID_OFFSET */, ngDevMode && `Please ensure that all keyframe offsets are between 0 and 1`);
}
export function keyframeOffsetsOutOfOrder() {
    return new RuntimeError(3200 /* RuntimeErrorCode.KEYFRAME_OFFSETS_OUT_OF_ORDER */, ngDevMode && `Please ensure that all keyframe offsets are in order`);
}
export function keyframesMissingOffsets() {
    return new RuntimeError(3202 /* RuntimeErrorCode.KEYFRAMES_MISSING_OFFSETS */, ngDevMode && `Not all style() steps within the declared keyframes() contain offsets`);
}
export function invalidStagger() {
    return new RuntimeError(3013 /* RuntimeErrorCode.INVALID_STAGGER */, ngDevMode && `stagger() can only be used inside of query()`);
}
export function invalidQuery(selector) {
    return new RuntimeError(3014 /* RuntimeErrorCode.INVALID_QUERY */, ngDevMode &&
        `\`query("${selector}")\` returned zero elements. (Use \`query("${selector}", { optional: true })\` if you wish to allow this.)`);
}
export function invalidExpression(expr) {
    return new RuntimeError(3015 /* RuntimeErrorCode.INVALID_EXPRESSION */, ngDevMode && `The provided transition expression "${expr}" is not supported`);
}
export function invalidTransitionAlias(alias) {
    return new RuntimeError(3016 /* RuntimeErrorCode.INVALID_TRANSITION_ALIAS */, ngDevMode && `The transition alias value "${alias}" is not supported`);
}
export function validationFailed(errors) {
    return new RuntimeError(3500 /* RuntimeErrorCode.VALIDATION_FAILED */, ngDevMode && `animation validation failed:\n${errors.map((err) => err.message).join('\n')}`);
}
export function buildingFailed(errors) {
    return new RuntimeError(3501 /* RuntimeErrorCode.BUILDING_FAILED */, ngDevMode && `animation building failed:\n${errors.map((err) => err.message).join('\n')}`);
}
export function triggerBuildFailed(name, errors) {
    return new RuntimeError(3404 /* RuntimeErrorCode.TRIGGER_BUILD_FAILED */, ngDevMode &&
        `The animation trigger "${name}" has failed to build due to the following errors:\n - ${errors
            .map((err) => err.message)
            .join('\n - ')}`);
}
export function animationFailed(errors) {
    return new RuntimeError(3502 /* RuntimeErrorCode.ANIMATION_FAILED */, ngDevMode &&
        `Unable to animate due to the following errors:${LINE_START}${errors
            .map((err) => err.message)
            .join(LINE_START)}`);
}
export function registerFailed(errors) {
    return new RuntimeError(3503 /* RuntimeErrorCode.REGISTRATION_FAILED */, ngDevMode &&
        `Unable to build the animation due to the following errors: ${errors
            .map((err) => err.message)
            .join('\n')}`);
}
export function missingOrDestroyedAnimation() {
    return new RuntimeError(3300 /* RuntimeErrorCode.MISSING_OR_DESTROYED_ANIMATION */, ngDevMode && "The requested animation doesn't exist or has already been destroyed");
}
export function createAnimationFailed(errors) {
    return new RuntimeError(3504 /* RuntimeErrorCode.CREATE_ANIMATION_FAILED */, ngDevMode &&
        `Unable to create the animation due to the following errors:${errors
            .map((err) => err.message)
            .join('\n')}`);
}
export function missingPlayer(id) {
    return new RuntimeError(3301 /* RuntimeErrorCode.MISSING_PLAYER */, ngDevMode && `Unable to find the timeline player referenced by ${id}`);
}
export function missingTrigger(phase, name) {
    return new RuntimeError(3302 /* RuntimeErrorCode.MISSING_TRIGGER */, ngDevMode &&
        `Unable to listen on the animation trigger event "${phase}" because the animation trigger "${name}" doesn\'t exist!`);
}
export function missingEvent(name) {
    return new RuntimeError(3303 /* RuntimeErrorCode.MISSING_EVENT */, ngDevMode &&
        `Unable to listen on the animation trigger "${name}" because the provided event is undefined!`);
}
export function unsupportedTriggerEvent(phase, name) {
    return new RuntimeError(3400 /* RuntimeErrorCode.UNSUPPORTED_TRIGGER_EVENT */, ngDevMode &&
        `The provided animation trigger event "${phase}" for the animation trigger "${name}" is not supported!`);
}
export function unregisteredTrigger(name) {
    return new RuntimeError(3401 /* RuntimeErrorCode.UNREGISTERED_TRIGGER */, ngDevMode && `The provided animation trigger "${name}" has not been registered!`);
}
export function triggerTransitionsFailed(errors) {
    return new RuntimeError(3402 /* RuntimeErrorCode.TRIGGER_TRANSITIONS_FAILED */, ngDevMode &&
        `Unable to process animations due to the following failed trigger transitions\n ${errors
            .map((err) => err.message)
            .join('\n')}`);
}
export function triggerParsingFailed(name, errors) {
    return new RuntimeError(3403 /* RuntimeErrorCode.TRIGGER_PARSING_FAILED */, ngDevMode &&
        `Animation parsing for the ${name} trigger have failed:${LINE_START}${errors
            .map((err) => err.message)
            .join(LINE_START)}`);
}
export function transitionFailed(name, errors) {
    return new RuntimeError(3505 /* RuntimeErrorCode.TRANSITION_FAILED */, ngDevMode && `@${name} has failed due to:\n ${errors.map((err) => err.message).join('\n- ')}`);
}
//# sourceMappingURL=data:application/json;base64,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