/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
function createListOfWarnings(warnings) {
    const LINE_START = '\n - ';
    return `${LINE_START}${warnings
        .filter(Boolean)
        .map((warning) => warning)
        .join(LINE_START)}`;
}
export function warnValidation(warnings) {
    (typeof ngDevMode === 'undefined' || ngDevMode) &&
        console.warn(`animation validation warnings:${createListOfWarnings(warnings)}`);
}
export function warnTriggerBuild(name, warnings) {
    (typeof ngDevMode === 'undefined' || ngDevMode) &&
        console.warn(`The animation trigger "${name}" has built with the following warnings:${createListOfWarnings(warnings)}`);
}
export function warnRegister(warnings) {
    (typeof ngDevMode === 'undefined' || ngDevMode) &&
        console.warn(`Animation built with the following warnings:${createListOfWarnings(warnings)}`);
}
export function triggerParsingWarnings(name, warnings) {
    (typeof ngDevMode === 'undefined' || ngDevMode) &&
        console.warn(`Animation parsing for the ${name} trigger presents the following warnings:${createListOfWarnings(warnings)}`);
}
export function pushUnrecognizedPropertiesWarning(warnings, props) {
    if (props.length) {
        warnings.push(`The following provided properties are not recognized: ${props.join(', ')}`);
    }
}
//# sourceMappingURL=data:application/json;base64,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