/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { AUTO_STYLE, } from '@angular/animations';
import { buildAnimationAst } from '../dsl/animation_ast_builder';
import { buildAnimationTimelines } from '../dsl/animation_timeline_builder';
import { ElementInstructionMap } from '../dsl/element_instruction_map';
import { createAnimationFailed, missingOrDestroyedAnimation, missingPlayer, registerFailed, } from '../error_helpers';
import { ENTER_CLASSNAME, LEAVE_CLASSNAME } from '../util';
import { warnRegister } from '../warning_helpers';
import { getOrSetDefaultValue, listenOnPlayer, makeAnimationEvent, normalizeKeyframes, optimizeGroupPlayer, } from './shared';
const EMPTY_INSTRUCTION_MAP = new ElementInstructionMap();
export class TimelineAnimationEngine {
    constructor(bodyNode, _driver, _normalizer) {
        this.bodyNode = bodyNode;
        this._driver = _driver;
        this._normalizer = _normalizer;
        this._animations = new Map();
        this._playersById = new Map();
        this.players = [];
    }
    register(id, metadata) {
        const errors = [];
        const warnings = [];
        const ast = buildAnimationAst(this._driver, metadata, errors, warnings);
        if (errors.length) {
            throw registerFailed(errors);
        }
        else {
            if (warnings.length) {
                warnRegister(warnings);
            }
            this._animations.set(id, ast);
        }
    }
    _buildPlayer(i, preStyles, postStyles) {
        const element = i.element;
        const keyframes = normalizeKeyframes(this._normalizer, i.keyframes, preStyles, postStyles);
        return this._driver.animate(element, keyframes, i.duration, i.delay, i.easing, [], true);
    }
    create(id, element, options = {}) {
        const errors = [];
        const ast = this._animations.get(id);
        let instructions;
        const autoStylesMap = new Map();
        if (ast) {
            instructions = buildAnimationTimelines(this._driver, element, ast, ENTER_CLASSNAME, LEAVE_CLASSNAME, new Map(), new Map(), options, EMPTY_INSTRUCTION_MAP, errors);
            instructions.forEach((inst) => {
                const styles = getOrSetDefaultValue(autoStylesMap, inst.element, new Map());
                inst.postStyleProps.forEach((prop) => styles.set(prop, null));
            });
        }
        else {
            errors.push(missingOrDestroyedAnimation());
            instructions = [];
        }
        if (errors.length) {
            throw createAnimationFailed(errors);
        }
        autoStylesMap.forEach((styles, element) => {
            styles.forEach((_, prop) => {
                styles.set(prop, this._driver.computeStyle(element, prop, AUTO_STYLE));
            });
        });
        const players = instructions.map((i) => {
            const styles = autoStylesMap.get(i.element);
            return this._buildPlayer(i, new Map(), styles);
        });
        const player = optimizeGroupPlayer(players);
        this._playersById.set(id, player);
        player.onDestroy(() => this.destroy(id));
        this.players.push(player);
        return player;
    }
    destroy(id) {
        const player = this._getPlayer(id);
        player.destroy();
        this._playersById.delete(id);
        const index = this.players.indexOf(player);
        if (index >= 0) {
            this.players.splice(index, 1);
        }
    }
    _getPlayer(id) {
        const player = this._playersById.get(id);
        if (!player) {
            throw missingPlayer(id);
        }
        return player;
    }
    listen(id, element, eventName, callback) {
        // triggerName, fromState, toState are all ignored for timeline animations
        const baseEvent = makeAnimationEvent(element, '', '', '');
        listenOnPlayer(this._getPlayer(id), eventName, baseEvent, callback);
        return () => { };
    }
    command(id, element, command, args) {
        if (command == 'register') {
            this.register(id, args[0]);
            return;
        }
        if (command == 'create') {
            const options = (args[0] || {});
            this.create(id, element, options);
            return;
        }
        const player = this._getPlayer(id);
        switch (command) {
            case 'play':
                player.play();
                break;
            case 'pause':
                player.pause();
                break;
            case 'reset':
                player.reset();
                break;
            case 'restart':
                player.restart();
                break;
            case 'finish':
                player.finish();
                break;
            case 'init':
                player.init();
                break;
            case 'setPosition':
                player.setPosition(parseFloat(args[0]));
                break;
            case 'destroy':
                this.destroy(id);
                break;
        }
    }
}
//# sourceMappingURL=data:application/json;base64,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