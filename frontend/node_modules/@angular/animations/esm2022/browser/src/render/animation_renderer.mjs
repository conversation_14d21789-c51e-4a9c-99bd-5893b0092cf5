import { AnimationRenderer, BaseAnimationRenderer } from './renderer';
export class AnimationRendererFactory {
    constructor(delegate, engine, _zone) {
        this.delegate = delegate;
        this.engine = engine;
        this._zone = _zone;
        this._currentId = 0;
        this._microtaskId = 1;
        this._animationCallbacksBuffer = [];
        this._rendererCache = new Map();
        this._cdRecurDepth = 0;
        engine.onRemovalComplete = (element, delegate) => {
            delegate?.removeChild(null, element);
        };
    }
    createRenderer(hostElement, type) {
        const EMPTY_NAMESPACE_ID = '';
        // cache the delegates to find out which cached delegate can
        // be used by which cached renderer
        const delegate = this.delegate.createRenderer(hostElement, type);
        if (!hostElement || !type?.data?.['animation']) {
            const cache = this._rendererCache;
            let renderer = cache.get(delegate);
            if (!renderer) {
                // Ensure that the renderer is removed from the cache on destroy
                // since it may contain references to detached DOM nodes.
                const onRendererDestroy = () => cache.delete(delegate);
                renderer = new BaseAnimationRenderer(EMPTY_NAMESPACE_ID, delegate, this.engine, onRendererDestroy);
                // only cache this result when the base renderer is used
                cache.set(delegate, renderer);
            }
            return renderer;
        }
        const componentId = type.id;
        const namespaceId = type.id + '-' + this._currentId;
        this._currentId++;
        this.engine.register(namespaceId, hostElement);
        const registerTrigger = (trigger) => {
            if (Array.isArray(trigger)) {
                trigger.forEach(registerTrigger);
            }
            else {
                this.engine.registerTrigger(componentId, namespaceId, hostElement, trigger.name, trigger);
            }
        };
        const animationTriggers = type.data['animation'];
        animationTriggers.forEach(registerTrigger);
        return new AnimationRenderer(this, namespaceId, delegate, this.engine);
    }
    begin() {
        this._cdRecurDepth++;
        if (this.delegate.begin) {
            this.delegate.begin();
        }
    }
    _scheduleCountTask() {
        queueMicrotask(() => {
            this._microtaskId++;
        });
    }
    /** @internal */
    scheduleListenerCallback(count, fn, data) {
        if (count >= 0 && count < this._microtaskId) {
            this._zone.run(() => fn(data));
            return;
        }
        const animationCallbacksBuffer = this._animationCallbacksBuffer;
        if (animationCallbacksBuffer.length == 0) {
            queueMicrotask(() => {
                this._zone.run(() => {
                    animationCallbacksBuffer.forEach((tuple) => {
                        const [fn, data] = tuple;
                        fn(data);
                    });
                    this._animationCallbacksBuffer = [];
                });
            });
        }
        animationCallbacksBuffer.push([fn, data]);
    }
    end() {
        this._cdRecurDepth--;
        // this is to prevent animations from running twice when an inner
        // component does CD when a parent component instead has inserted it
        if (this._cdRecurDepth == 0) {
            this._zone.runOutsideAngular(() => {
                this._scheduleCountTask();
                this.engine.flush(this._microtaskId);
            });
        }
        if (this.delegate.end) {
            this.delegate.end();
        }
    }
    whenRenderingDone() {
        return this.engine.whenRenderingDone();
    }
}
//# sourceMappingURL=data:application/json;base64,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