const EMPTY_ANIMATION_OPTIONS = {};
export {};
//# sourceMappingURL=data:application/json;base64,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