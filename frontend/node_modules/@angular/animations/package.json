{"name": "@angular/animations", "version": "18.2.13", "description": "Angular - animations integration with web-animations", "author": "angular", "license": "MIT", "engines": {"node": "^18.19.1 || ^20.11.1 || >=22.0.0"}, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": "18.2.13"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/animations"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": false, "module": "./fesm2022/animations.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "esm2022": "./esm2022/animations.mjs", "esm": "./esm2022/animations.mjs", "default": "./fesm2022/animations.mjs"}, "./browser": {"types": "./browser/index.d.ts", "esm2022": "./esm2022/browser/browser.mjs", "esm": "./esm2022/browser/browser.mjs", "default": "./fesm2022/browser.mjs"}, "./browser/testing": {"types": "./browser/testing/index.d.ts", "esm2022": "./esm2022/browser/testing/testing.mjs", "esm": "./esm2022/browser/testing/testing.mjs", "default": "./fesm2022/browser/testing.mjs"}}}